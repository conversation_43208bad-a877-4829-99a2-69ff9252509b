{"App": "App", "Export": "Export", "FAQ": {"ai_point_a": "Every time the AI ​​model is called, a certain amount of AI points will be consumed. \nFor specific calculation standards, please refer to the \"AI Points Calculation Standards\" above.\n\nToken calculation uses the same formula as GPT3.5, 1Token≈0.7 Chinese characters≈0.9 English words. Continuous characters may be considered as 1 Tokens.", "ai_point_expire_a": "will expire. \nAfter the current package expires, the AI ​​points will be cleared and updated to the AI ​​points of the new package. \nThe AI ​​points for the annual package last for 1 year, not every month.", "ai_point_expire_q": "Will AI points expire?", "ai_point_q": "What are AI points?", "auto_renew_a": "After the current package expires, the system will automatically renew according to the \"future package\" and the system will try to deduct the fee from the account balance. If you need to automatically renew, please reserve a quota in the account balance.", "auto_renew_q": "Will the subscription package automatically renew?", "change_package_a": "When the current package price is greater than the new package, you cannot switch immediately. You will be switched in the form of \"renewal\" after the current package expires.\n\nWhen the current package price is lower than the new package, the system will automatically calculate the remaining balance of the current package, and you can pay the price difference to switch packages.", "change_package_q": "Can I switch subscription plans?", "dataset_compute_a": "1 knowledge base storage is equal to 1 knowledge base index. \nA piece of knowledge base data can contain one or more knowledge base indexes. \nDuring enhanced training, 1 piece of data will generate 5 indexes.", "dataset_compute_q": "How to calculate knowledge base storage?", "dataset_index_a": "Won't. \nHowever, when the knowledge base index exceeds, knowledge base content cannot be inserted and updated.", "dataset_index_q": "Will the knowledge base index be deleted if it exceeds the limit?", "free_user_clean_a": "After free version users (free version without purchasing additional packages) have no usage records for 30 days, the system will automatically clear all knowledge base content under the account.", "free_user_clean_q": "Will the data in the free version be cleared?", "package_overlay_a": "OK. \nEach resource package purchased is independent and will be used stacked within its validity period. \nAI points will be deducted from the resource package that expires first.", "package_overlay_q": "Can additional resource packs be stacked?"}, "Folder": "Folder", "Login": "<PERSON><PERSON>", "Move": "Move", "Name": "Name", "Rename": "<PERSON><PERSON>", "Resume": "Resume", "Running": "Running", "UnKnow": "Unknown", "Warning": "Warning", "add_new": "Add new", "chose_condition": "Selection criteria", "chosen": "selected", "classification": "Classification", "click_to_resume": "Resume", "code_editor": "Code edit", "code_error": {"app_error": {"not_exist": "App does not exist", "un_auth_app": "No permission to operate this application"}, "chat_error": {"un_auth": "No permission to operate this conversation record"}, "error_code": {"400": "Request failed", "401": "No access", "403": "Nervous visit", "404": "Request does not exist", "405": "Request method error", "406": "Request format error", "410": "Resource deleted", "422": "Validation error", "500": "Server error occurred", "502": "Gateway error", "503": "The server is temporarily overloaded or under maintenance", "504": "Gateway timeout"}, "error_message": {"403": "Credential error", "510": "Insufficient account balance", "511": "No permission to operate this model", "513": "No permission to read the file", "514": "Api Key is illegal"}, "openapi_error": {"api_key_not_exist": "API Key does not exist", "exceed_limit": "Up to 10 sets of API keys", "un_auth": "No permission to operate this API Key"}, "outlink_error": {"invalid_link": "Sharing link is invalid", "link_not_exist": "Sharing link does not exist", "un_auth_user": "Identity verification failed"}, "plugin_error": {"not_exist": "Plugin does not exist", "un_auth": "No permission to operate this plug-in"}, "system_error": {"community_version_num_limit": "If the number limit is exceeded"}, "team_error": {"ai_points_not_enough": "", "app_amount_not_enough": "The number of applications has reached the upper limit~", "dataset_amount_not_enough": "The number of knowledge bases has reached the upper limit~", "dataset_size_not_enough": "The capacity of the knowledge base is insufficient, please expand it first~", "over_size": "error.team.overSize", "plugin_amount_not_enough": "The number of plug-ins has reached the upper limit~", "re_rank_not_enough": "Not authorized to use search rearrangement~", "un_auth": "No permission to operate this team", "website_sync_not_enough": "No permission to use Web site synchronization~"}, "token_error_code": {"403": "Invalid login status, please log in again"}, "user_error": {"balance_not_enough": "Account balance is insufficient~", "bin_visitor": "Your identity verification failed", "bin_visitor_guest": "You are currently a visitor and do not have permission to operate", "un_auth_user": "The user cannot be found"}}, "common": {"Action": "Action", "Add": "Add", "Add New": "Add New", "Add Success": "Add successfully", "All": "All", "Cancel": "Cancel", "Choose": "<PERSON><PERSON>", "Close": "Close", "Config": "Config", "Confirm": "Confirm", "Confirm Create": "Confirm Create", "Confirm Import": "Confirm Import", "Confirm Move": "Move Here", "Confirm Update": "Confirm Update", "Confirm to leave the page": "Confirm to leave the page?", "Copy": "Copy", "Copy Successful": "Copy Successful", "Create Failed": "Create Failed", "Create New": "Create New", "Create Success": "Create Success", "Create Time": "Create Time", "Creating": "Creating", "Custom Title": "Custom Title", "Delete": "Delete", "Delete Failed": "Delete Failed", "Delete Success": "Delete Success", "Delete Warning": "Delete Warning", "Delete folder": "Delete", "Detail": "Detail", "Documents": "Documents", "Done": "Done", "Edit": "Edit", "Exit": "Exit", "Expired Time": "Expired Time", "Field": "Field", "File": "File", "Finish": "Finish", "Import": "Import", "Import failed": "Import failed", "Import success": "Import success", "Input": "Input", "Input folder description": "Folder description", "Input name": "Folder name", "Intro": "Intro", "Last Step": "Last Step", "Last use time": "Last use time", "Load Failed": "Load Failed", "Loading": "Loading...", "More": "More", "Move": "Move", "MultipleRowSelect": {"No data": "No data available"}, "Name": "Name", "Next Step": "Next Step", "No more data": "No more data~", "Not open": "Unopened", "OK": "OK", "Open": "Open", "Operation": "Operation", "Other": "Other", "Output": "Output", "Params": "Params", "Password inconsistency": "Passwords do not match", "Permission": "Permissions", "Please Input Name": "Please enter a name", "Read document": "Read document", "Read intro": "Read intro", "Remove": "Remove", "Rename": "<PERSON><PERSON>", "Request Error": "Request Error", "Require Input": "Required Input", "Restart": "<PERSON><PERSON>", "Role": "Role", "Root folder": "Root folder", "Run": "Run", "Save": "Save", "Save Failed": "Saved failed", "Save Success": "Saved success", "Search": "Search", "Select File Failed": "Select File Failed", "Select template": "Select template", "Set Avatar": "Click to set avatar", "Set Name": "Set a name", "Setting": "Setting", "Status": "Status", "Submit failed": "Submit failed", "Success": "Success", "Sync success": "Sync success", "Team": "Team", "Team Tags Set": "Tags", "Un used": "Unused", "UnKnow": "Unknown", "UnKnow Source": "Unknown Source", "Unlimited": "Unlimited", "Update": "Update", "Update Failed": "Update Failed", "Update Success": "Update Success", "Update Successful": "Update Successful", "Upload File Failed": "Upload File Failed", "Username": "Username", "Waiting": "Waiting", "Warning": "Warning", "Website": "Website", "all_result": "full results", "avatar": {"Select Avatar": "Click to select avatar", "Select Failed": "Select avatar failed"}, "choosable": "Choosable", "confirm": {"Common Tip": "Operation confirmation"}, "course": {"Read Course": "Read course"}, "empty": {"Common Tip": "No data~"}, "error": {"Select avatar failed": "Avatar selection failed", "too_many_request": "Too many requests, please try again later.", "unKnow": "An unexpected error occurred~"}, "failed": "fail", "folder": {"Drag Tip": "Drag me", "Move Success": "Move successful", "Move to": "Move to", "No Folder": "No subdirectories, place here", "Open folder": "Open folder", "Root Path": "Root directory", "empty": "This directory has nothing selectable~", "open_dataset": "Open knowledge base"}, "input": {"Repeat Value": "Duplicate value"}, "is_requesting": "Requesting...", "jsonEditor": {"Parse error": "JSON may be incorrect, please check carefully"}, "link": {"UnValid": "Invalid link"}, "month": "Month", "name_is_empty": "Name cannot be empty", "no_intro": "No introduction yet", "not_support": "not support", "page_center": "Center the page", "request_end": "All loaded", "request_more": "Click to load more", "speech": {"error tip": "Speech to text failed", "not support": "Your browser does not support speech input"}, "support": "support", "system": {"Commercial version function": "Commercial version feature", "Help Chatbot": "<PERSON><PERSON><PERSON> assistant", "Use Helper": "Use helper"}, "ui": {"textarea": {"Magnifying": "Magnify"}}}, "confirm_choice": "Confirm selection", "core": {"Chat": "Cha<PERSON>", "Max Token": "Max token per data", "ai": {"AI settings": "AI settings", "Ai point price": "AI point consumption", "Max context": "Max context", "Model": "AI model", "Not deploy rerank model": "Rerank model not deployed", "Prompt": "Prompt", "Support tool": "Function call", "model": {"Dataset Agent Model": "File processing model", "Vector Model": "Index model", "doc_index_and_dialog": "Document index"}}, "app": {"Ai response": "Return AI content", "Api request": "API access", "Api request desc": "Access existing systems via API, or enterprise WeChat, Lark, etc.", "App intro": "App introduction", "Chat Variable": "Chat box variable", "Config schedule plan": "Configure scheduled execution", "Config whisper": "Configure voice input", "Interval timer config": "Scheduled execution configuration", "Interval timer run": "Scheduled execution", "Interval timer tip": "Can be scheduled to execute applications", "Make a brief introduction of your app": "Give your AI app an introduction", "Max histories": "Chat history count", "Max tokens": "Reply limit", "Name and avatar": "Avatar & name", "Onclick to save": "Click to save", "Publish": "Publish", "Publish Confirm": "Confirm to publish the app? It will immediately update the app status across all published channels.", "Publish app tip": "Once you publish your app, all distribution channels will immediately use that version", "Question Guide": "Guess you want to ask", "Question Guide Tip": "After the conversation ends, it will generate 3 guiding questions.", "Quote prompt": "Quote template prompt", "Quote templates": "Quote content templates", "Random": "Diverge", "Saved time": "Saved: {{time}}", "Search team tags": "Search tags", "Select TTS": "Select voice playback mode", "Select app from template": "Select from template", "Select quote template": "Select quote prompt template", "Set a name for your app": "Set a name for your app", "Setting ai property": "Click to configure AI model related properties", "Share link": "Login-free window", "Share link desc": "Share the link with other users, who can use it directly without logging in", "Share link desc detail": "You can directly share this model with other users for conversation, and the other party can directly start the conversation without logging in. Note, this feature will consume your account balance, please keep the link safe!", "TTS": "Voice playback", "TTS Tip": "After enabling, each conversation can use the voice playback function. Using this function may incur additional costs.", "TTS start": "Read content", "Team tags": "Team tags", "Temperature": "Temperature", "Tool call": "Tool call", "ToolCall": {"No plugin": "No available plugins", "Parameter setting": "Input parameters", "System": "System", "Team": "My", "Public": "Public", "This plugin cannot be called as a tool": "This tool cannot be used in simple mode"}, "Welcome Text": "Conversation opener", "Whisper": "Voice input", "Whisper config": "Voice input configuration", "deterministic": "Rigorous", "edit": {"Prompt Editor": "Prompt editor", "Query extension background prompt": "Conversation background description", "Query extension background tip": "Describe the scope of the current conversation to help the AI complete and expand the current question. The content filled in is usually for the assistant"}, "edit_content": "Application information editing", "error": {"App name can not be empty": "App name cannot be empty", "Get app failed": "Failed to get app"}, "feedback": {"Custom feedback": "Custom feedback", "close custom feedback": "Close feedback"}, "have_publish": "Published", "loading": "loading", "logs": {"Source And Time": "Source & Time"}, "no_app": "There is no application yet, go and create one!", "not_published": "Unpublished", "outLink": {"Can Drag": "Icon draggable", "Default open": "Open by default", "Iframe block title": "Copy the Iframe below and add it to your website", "Link block title": "Copy the link below and open it in a browser", "Script Close Icon": "Close icon", "Script Open Icon": "Open icon", "Script block title": "Add the code below to your website", "Select Mode": "Start using", "Select Using Way": "Select usage method", "Show History": "Show chat history"}, "publish": {"Fei Shu Bot Desc": "Access to Lark bot", "Fei shu bot": "Lark", "Fei shu bot publish": "Publish to Lark bot", "FOFPro Desc": "Publish to FOFPro", "FOFPro": "FOFPro", "FOFPro publish": "Publish to FOFPro and access applications through the FOFPro website and mini programs", "BenZui publish": "Publish to BenZui, access applications through the BenZui mini program", "TOWA Store Desc": "Public to TOWA Store", "Edu Store Desc": "Public to Edu Store", "BenZui Store Desc": "Public to BenZui Store", "TOWA Store": "TOWA Store", "Edu Store": "Edu Store", "BenZui Store": "BenZui Store", "TOWA Store publish": "If you wish to publish the application to the TOWA Store, please scan the QR code to contact the assistant", "Publish": "Publish", "Cancel Publish": "Cancel Publish"}, "schedule": {"Default prompt": "Default question", "Default prompt placeholder": "Default question when executing the app", "Every day": "Every day at {{hour}}:00", "Every month": "Every month on the {{day}} at {{hour}}:00", "Every week": "Every week on {{day}} at {{hour}}:00", "Interval": "Every {{interval}} hours", "Open schedule": "Scheduled execution"}, "setting": "App information settings", "share": {"Amount limit tip": "A maximum of 10 groups can be created", "Create link": "Create new link", "Create link tip": "Link created successfully. The share address has been copied, you can share it directly", "Ip limit title": "IP rate limit (people/minute)", "Is response quote": "Return quote", "Not share link": "No share link created", "Role check": "Identity verification"}, "tip": {"Add a intro to app": "Come and give the app an introduction~", "chatNodeSystemPromptTip": "The model has a fixed guide word. By adjusting this content, you can guide the model in the chat direction. \nThe content will be anchored at the beginning of the context. \nVariables can be selected via input/insert\n\nIf a knowledge base is associated, you can also use appropriate descriptions to guide the model when to call the knowledge base search. \nFor example:\n\nYou are an assistant in the movie \"Interstellar\". When users ask about content related to \"Interstellar\", please search the knowledge base and answer based on the search results.", "variableTip": "You can ask the user to fill in some content as specific variables for this round of conversation before the conversation starts. This module is located after the opening guide.\nVariables can be injected into other module string type inputs through the form of {{variable key}}, such as: prompt words, limiting words, etc.", "welcomeTextTip": "Send an initial content before each conversation starts. Supports standard Markdown syntax, additional tags available:\n[Shortcut key]: User can click to send the question directly"}, "tool_label": {"doc": "Use documentation", "github": "GitHub address", "price": "Billing instructions"}, "tts": {"Close": "Do not use", "Speech model": "Voice model", "Speech speed": "Speech speed", "Test Listen": "Test listen", "Test Listen Text": "Hello, this is a voice test, if you can hear this sentence, it means the voice playback function is normal", "Web": "Browser built-in (free)"}, "whisper": {"Auto send": "Auto send", "Auto send tip": "Automatically send after voice input is complete, no need to manually click the send button", "Auto tts response": "Auto voice response", "Auto tts response tip": "Questions sent via voice input will be responded to directly in voice form, please make sure the voice playback function is turned on.", "Close": "Close", "Not tts tip": "You have not turned on voice playback, this function cannot be used", "Open": "Open", "Switch": "Turn on voice input"}}, "chat": {"Admin Mark Content": "Corrected Reply", "Audio Not Support": "Device does not support audio playback", "Audio Speech Error": "Audio speech error", "Cancel Speak": "Cancel voice input", "Chat API is error or undefined": "Chat API error or undefined", "Confirm to clear history": "Confirm to clear the online chat history of this app? Records of sharing and API calls will not be cleared.", "Confirm to clear share chat history": "Confirm to delete all chat history?", "Converting to text": "Converting to text...", "Custom History Title": "Custom History Title", "Custom History Title Description": "If set to empty, it will automatically follow the chat history.", "Debug test": "Debug preview", "Exit Chat": "Exit chat", "Failed to initialize chat": "Failed to initialize chat", "Feedback Failed": "Feedback submission error", "Feedback Modal": "<PERSON><PERSON><PERSON><PERSON>", "Feedback Modal Tip": "Enter what you find unsatisfactory about the answer", "Feedback Submit": "Submit feedback", "Feedback Success": "Feedback successful!", "Finish Speak": "Voice input complete", "History": "History", "History Amount": "{{amount}} records", "Mark": "<PERSON> expected answer", "Mark Description": "The current marking function is in beta.\n\nAfter adding a mark, you need to select a dataset to store the marking data. You can quickly mark questions and expected answers through this function to guide the model's response next time.\n\nCurrently, like other data in the dataset, the marking function is influenced by the model and does not guarantee 100% compliance with expectations after marking.\n\nMarking data is only synchronized one-way with the dataset. If the dataset modifies the marking data, the log display of marking data cannot be synchronized", "Mark Description Title": "Introduction to marking function", "New Chat": "New conversation", "Pin": "<PERSON>n", "Question Guide": "Guess what you want to ask", "Quote": "Quote", "Quote Amount": "Dataset quotes ({{amount}} items)", "Read Mark Description": "View introduction to marking function", "Recent use": "Recent use", "Record": "Voice input", "Restart": "Restart conversation", "Select Image": "Select image", "Select dataset": "Select dataset", "Select dataset Desc": "Select a dataset to store the expected answer", "Send Message": "Send", "Speaking": "I'm listening, please speak...", "Start Chat": "Start conversation", "Type a message": "Type a question, send [Enter]/newline [Ctrl(Alt/Shift) + Enter]", "Unpin": "Unpin", "You need to a chat app": "You do not have a usable app", "error": {"Chat error": "Chat error", "Messages empty": "API content is empty, text may be too long~", "Select dataset empty": "You did not select a dataset", "User input empty": "User question input is empty", "data_error": "Exception in getting data"}, "feedback": {"Close User Like": "User expressed approval\nClick to close this mark", "Feedback Close": "Close feedback", "No Content": "User did not provide specific feedback content", "Read User dislike": "User expressed disapproval\nClick to view content"}, "logs": {"api": "API call", "online": "Online use", "share": "External link call", "test": "Test", "published": "Published app"}, "markdown": {"Edit Question": "Edit question", "Quick Question": "Click to ask now", "Send Question": "Send question"}, "quote": {"Quote Tip": "Only the actual quote content is displayed here. If the data is updated, it will not be updated in real-time here", "Read Quote": "View quote"}, "response": {"Complete Response": "Complete response", "Extension model": "Question optimization model", "Plugin response detail": "Plugin details", "Read complete response": "View details", "Read complete response tips": "Click to view detailed process", "Tool call response detail": "Tool operation details", "Tool call tokens": "Tool call Tokens consumption", "context total length": "Total context length", "module cq": "Question classification list", "module cq result": "Classification result", "module extract description": "Extract requirement description", "module extract result": "Extraction result", "module historyPreview": "Record preview (only part of the content is displayed)", "module http result": "response body", "module if else Result": "Determinator result", "module limit": "Single search limit", "module maxToken": "Maximum response tokens", "module model": "Model", "module name": "Model name", "module query": "question/search term", "module quoteList": "Quote content", "module similarity": "Similarity", "module temperature": "temperature", "module time": "Running time", "module tokens": "total tokens", "plugin output": "Plugin output value", "search using reRank": "Result rearrangement", "text output": "text output"}, "retry": "Regenerate", "tts": {"Stop Speech": "Stop"}}, "common": {"tip": {"leave page": "Content has been modified, confirm to leave the page?"}}, "dataset": {"All Dataset": "All datasets", "Avatar": "Dataset avatar", "Choose Dataset": "Associate dataset", "Collection": "Dataset", "Create dataset": "Create a dataset", "Dataset": "Dataset", "Dataset ID": "Dataset ID", "Dataset Type": "Dataset type", "Delete Confirm": "Confirm to delete this dataset? Data cannot be recovered after deletion, please confirm!", "Empty Dataset": "Empty Dataset", "Empty Dataset Tips": "No datasets yet, go create one!", "Folder placeholder": "This is a directory", "Go Dataset": "Go to dataset", "Intro Placeholder": "This dataset has no introduction~", "Manual collection": "Manual dataset", "My Dataset": "My dataset", "Name": "Dataset name", "Query extension intro": "Enabling the question optimization feature can improve the accuracy of dataset searches during continuous dialogue. After enabling this feature, AI will complete missing information based on conversation records when searching the dataset.", "Quote Length": "Quote content length", "Read Dataset": "View dataset details", "Set Website Config": "Start configuring website information", "Start export": "Export started", "Table collection": "Table dataset", "Text collection": "Text dataset", "collection": {"Click top config website": "Click to configure website", "Collection name": "Dataset name", "Collection raw text": "Dataset content", "Empty Tip": "The dataset is empty", "QA Prompt": "QA split guide words", "Start Sync Tip": "Confirm to start syncing data? Old data will be deleted and re-acquired, please confirm!", "Sync": "Sync data", "Sync Collection": "Data synchronization", "Website Empty Tip": "No associated websites yet", "Website Link": "Web site address", "id": "Collection ID", "metadata": {"Chunk Size": "Segment size", "Createtime": "Creation time", "Raw text length": "Raw text length", "Read Metadata": "View metadata", "Training Type": "Training mode", "Updatetime": "Update time", "Web page selector": "Website selector", "metadata": "<PERSON><PERSON><PERSON>", "read source": "View original content", "source": "Data source", "source name": "Source name", "source size": "Source size"}, "status": {"active": "Ready"}, "sync": {"result": {"sameRaw": "Content unchanged, no update needed", "success": "Start syncing"}}, "training": {}}, "data": {"Auxiliary Data": "Auxiliary data", "Auxiliary Data Placeholder": "This part is optional, usually to complement the data content above to build structured prompt words for special scenarios, up to {{maxToken}} characters.", "Auxiliary Data Tip": "This part is optional\nThis content is usually to complement the data content above to build structured prompt words for special scenarios", "Data Content": "Related data content", "Data Content Placeholder": "This input box is mandatory, the content is usually a description of the knowledge point or a user's question, up to {{maxToken}} characters.", "Data Content Tip": "This input box is mandatory\nThe content is usually a description of the knowledge point or a user's question.", "Default Index Tip": "Cannot be edited, the default index will use the text of the related data content and auxiliary data to generate the index directly.", "Edit": "Edit data", "Empty Tip": "This collection has no data yet~", "Main Content": "Main content", "Search data placeholder": "Search related data", "Too Long": "Total length too long", "Total Amount": "{{total}} sets", "group": "Group", "unit": "items"}, "embedding model tip": "The index model can convert natural language into vectors for semantic retrieval.\nNote, different index models cannot be used together, once you select an index model, it cannot be changed.", "error": {"Data not found": "Data does not exist or has been deleted", "Start Sync Failed": "Failed to start syncing", "Template does not exist": "Template does not exist", "invalidVectorModelOrQAModel": "Invalid vector model or QA model", "unAuthDataset": "Unauthorized to operate this dataset", "unAuthDatasetCollection": "Unauthorized to operate this collection", "unAuthDatasetData": "Unauthorized to operate this data", "unAuthDatasetFile": "Unauthorized to operate this file", "unCreateCollection": "Unauthorized to operate this data", "unLinkCollection": "Not a network link collection"}, "externalFile": "external file repository", "file": "File", "folder": "Directory", "import": {"Auto mode Estimated Price Tips": "Requires calling the file processing model, consuming more Tokens: {{price}} points/1k Tokens", "Auto process": "Automatic", "Auto process desc": "Automatically set segmentation and preprocessing rules", "Chunk Range": "Range: {{min}}~{{max}}", "Chunk Split": "Direct segmentation", "Chunk Split Tip": "Segment the text according to certain rules and convert it into a format that can be semantically searched, suitable for most scenarios. Does not require calling additional models for processing, low cost.", "Custom process": "Custom rules", "Custom process desc": "Customize segmentation and preprocessing rules", "Custom prompt": "Custom prompt words", "Custom split char": "Custom delimiter", "Custom split char Tips": "Allows you to segment based on a custom delimiter. Usually used for pre-processed data, using a specific delimiter for precise segmentation.", "Custom text": "Custom text", "Custom text desc": "Manually enter a text as a dataset", "Data Preprocessing": "Data processing", "Data process params": "Data processing parameters", "Down load csv template": "Click to download CSV template", "Embedding Estimated Price Tips": "Only using the index model, consumes a small amount of AI points: {{price}} points/1k Tokens", "Ideal chunk length": "Ideal chunk length", "Ideal chunk length Tips": "Segment according to the end symbol, and combine the segments into a block. This value determines the estimated size of the block.", "Import success": "Import successful, please wait for training", "Link name": "Network link", "Link name placeholder": "Only supports static links, if the data is empty after upload, the link may not be readable\nOne per line, up to 10 links at a time", "Local file": "Local file", "Local file desc": "Upload PDF, TXT, DOCX, etc. files", "Preview chunks": "Preview segments (up to 5)", "Preview raw text": "Preview raw text (up to 3000 characters)", "Process way": "Processing method", "QA Estimated Price Tips": "Requires calling the file processing model, consuming more AI points: {{price}} points/1k Tokens", "QA Import": "QA Split", "QA Import Tip": "According to certain rules, split the text into a larger paragraph, call AI to generate Q&A pairs for that paragraph. Has very high search accuracy, but loses a lot of content details.", "Select file": "Select file", "Select source": "Select source", "Source name": "Source name", "Sources list": "Source list", "Start upload": "Start upload", "Total files": "A total of {{total}} files", "Training mode": "Training mode", "Upload data": "Upload data", "Upload file progress": "file_upload progress", "Upload status": "Status", "Web link": "Web link", "Web link desc": "Read static web page content as a dataset"}, "link": "Link", "search": {"Dataset Search Params": "Knowledge base search configuration", "Empty result response": "Empty search reply", "Filter": "Search filter", "Max Tokens": "Maximum tokens", "Max Tokens Tips": "Maximum number of Tokens per search, approximately 1 Chinese character = 1.7 Tokens, 1 English character = 1 Token", "Min Similarity": "Minimum similarity", "Min Similarity Tips": "Different index models have different similarities, please choose the appropriate value through search testing. When using ReRank, the similarity may be very low.", "No support similarity": "Supports similarity filtering only when using result reranking or semantic retrieval", "Nonsupport": "Not supported", "Params Setting": "Search parameter settings", "Quote index": "Which quote", "ReRank": "Result reranking", "ReRank desc": "Use the reranking model for secondary sorting to enhance the overall ranking.", "Source id": "Source ID", "Source name": "Source name", "Using query extension": "Use question optimization", "mode": {"embedding": "Semantic retrieval", "embedding desc": "Use vectors for text relevance queries", "fullTextRecall": "Full-text retrieval", "fullTextRecall desc": "Use traditional full-text retrieval, suitable for finding some keywords and special subject-predicate data", "mixedRecall": "Mixed retrieval", "mixedRecall desc": "Return the combined results of vector retrieval and full-text retrieval, sorted using the RRF algorithm."}, "score": {"embedding": "Semantic retrieval", "embedding desc": "Obtain scores by calculating the distance between vectors, ranging from 0 to 1.", "fullText": "Full-text retrieval", "fullText desc": "Calculate the score of the same keywords, ranging from 0 to infinity.", "reRank": "Result reranking", "reRank desc": "Calculate the relevance between sentences using the ReRank model, ranging from 0 to 1.", "rrf": "Comprehensive ranking", "rrf desc": "Merge multiple search results by inverted calculation."}, "search mode": "Search mode"}, "status": {"active": "Ready", "syncing": "Syncing"}, "test": {"Batch test": "Batch test", "Batch test Placeholder": "Select a Csv file", "Search Test": "Search test", "Test": "Test", "Test Result": "Test result", "Test Text": "Single text test", "Test Text Placeholder": "Enter the text to be tested", "Test params": "Test parameters", "delete test history": "Delete this test result", "test history": "Test history", "test result placeholder": "Test results will be displayed here", "test result tip": "Sort based on the similarity between the knowledge base content and the test text. You can adjust the corresponding text according to the test results.\nNote: The data in the test record may have been modified, clicking on a test data will display the latest data."}, "training": {"Agent queue": "QA training queue", "Auto mode": "Enhanced processing (experimental)", "Auto mode Tip": "Increase the semantic richness of data blocks by generating related questions and summaries through sub-indexes and model calls, which is more conducive to retrieval. Requires more storage space and increases AI call frequency.", "Chunk mode": "Direct segmentation", "Full": "Estimated more than 5 minutes", "Leisure": "Idle", "QA mode": "Question and answer split", "Vector queue": "Index queue", "Waiting": "Estimated 5 minutes", "Website Sync": "Web site sync"}, "website": {"Base Url": "Base URL", "Config": "Web site configuration", "Config Description": "The Web site sync feature allows you to fill in a website's base URL, and the system will automatically crawl related web pages for knowledge base training. Only static sites will be crawled, mainly project documentation and blogs.", "Confirm Create Tips": "Confirm to sync this site, the sync task will start subsequently, please confirm!", "Confirm Update Tips": "Confirm to update the site configuration? It will start syncing immediately according to the new configuration, please confirm!", "Selector": "Selector", "Selector Course": "Selector tutorial", "Start Sync": "Start sync", "UnValid Website Tip": "Your site may be a non-static site and cannot be synced"}}, "module": {"Add question type": "Add question type", "Can not connect self": "Cannot connect to self", "Confirm Delete Node": "Confirm delete node?", "Data Type": "Data type", "Dataset quote": {"label": "Knowledge base quote", "select": "Select knowledge base reference"}, "Default Value": "Default value", "Default value": "Default value", "Default value placeholder": "If not filled, the default return is an empty string", "Edit intro": "Edit description", "Field Description": "Field description", "Field Name": "Field name", "Http request props": "Request parameters", "Http request settings": "Request settings", "Input Type": "Input type", "Laf sync params": "Synchronize parameters", "Max Length": "Maximum length", "Max Length placeholder": "Maximum length of input text", "Max Value": "Maximum value", "Min Value": "Minimum value", "QueryExtension": {"placeholder": "For example:\nIntroduction and usage of python.\nThe current conversation is related to the game 'GTA5'."}, "Quote prompt setting": "Quote prompt setting", "Select app": "Select app", "Setting quote prompt": "Configure quote prompt", "Variable": "Global variable", "Variable Setting": "Variable setting", "edit": {"Field Name Cannot Be Empty": "Field name cannot be empty"}, "extract": {"Add field": "Add field", "Enum Description": "List possible values for the field, one per line", "Enum Value": "Enumeration value", "Field Description Placeholder": "Name/age/SQL statement...", "Field Setting Title": "Extract field configuration", "Required": "Must return", "Required Description": "Even if the field cannot be extracted, the default value will be returned", "Target field": "Target field"}, "http": {"Add props": "Add parameters", "AppId": "Application ID", "AppSecret": "AppSecret", "ChatId": "Current chat ID", "Current time": "Current time", "Histories": "Last 10 chat records", "Key already exists": "Key already exists", "Key cannot be empty": "Parameter name cannot be empty", "Props name": "Parameter name", "Props tip": "Can set HTTP request related parameters\nCan use {{key}} to call global variables or external parameter input, currently available variables:\n{{variable}}", "Props value": "Parameter value", "ResponseChatItemId": "AI response ID", "Url and params have been split": "Path parameters have been automatically added to Params", "curl import": "curl import", "curl import placeholder": "Please enter curl format content, the request information of the first interface will be extracted."}, "input": {"Add Branch": "Add branch", "add": "Add condition", "description": {"Background": "You can add some specific content introductions to better identify the user's question type. This content is usually to introduce something unknown to the model.", "HTTP Dynamic Input": "Receives the output value of the previous node as a variable, which can be used by HTTP request parameters.", "Http Request Header": "Custom request headers, please strictly fill in JSON strings.\n1. Ensure there is no comma after the last property\n2. Ensure key includes double quotes\nFor example: {\"Authorization\":\"Bearer xxx\"}", "Http Request Url": "New HTTP request URL. If there are two 'request URLs', you can delete this module and re-add it to pull the latest module configuration.", "Response content": "Can use \\n to achieve continuous line breaks.\nCan be overridden by external module input. If non-string type data is passed in, it will be automatically converted to string"}, "label": {"Background": "Background knowledge", "Http Request Url": "Request URL", "Response content": "Response content", "Select dataset": "Select knowledge base", "aiModel": "AI model", "chat history": "Chat history", "user question": "User question"}, "placeholder": {"Classify background": "For example: \n1. AIGC (Artificial Intelligence Generated Content) refers to the use of artificial intelligence technology to automatically or semi-automatically generate digital content, such as text, images, music, videos, etc.\n2. AIGC technology includes but is not limited to natural language processing, computer vision, machine learning, and deep learning. These technologies can create new content or modify existing content to meet specific creative, educational, entertainment, or informational needs."}}, "laf": {"Select laf function": "Select laf function"}, "output": {"description": {"Ai response content": "Triggered after the stream response is complete", "New context": "Concatenate this response content with historical records to return as a new context", "query extension result": "Outputs as an array of strings, can directly connect this result to 'Knowledge base search' 'User question', it is recommended not to connect to 'AI dialogue' 'User question'"}, "label": {"Ai response content": "AI response content", "New context": "New context", "query extension result": "Optimized result"}}, "template": {"AI function": "AI function", "AI response switch tip": "If you wish the current node not to output content, you can turn off this switch. AI output content will not be displayed to the user, you can manually use 'AI response content' for special processing.", "AI support tool tip": "Supports function calls model, can better use tool invocation.", "Basic Node": "Basic function", "Query extension": "Question optimization", "System Plugin": "System plugin", "Public Plugin": "Public Plugin", "System input module": "System input", "TFSwitch": "Judger", "TFSwitch intro": "Outputs True or False based on the input content. By default, when the input content is false, undefined, null, 0, none, it will output false. You can also add some custom strings to supplement the output of false content. Non-character, non-number, non-boolean types, directly output True.", "Team Plugin": "My plugin", "Tool module": "Tool", "UnKnow Module": "Unknown module", "http body placeholder": "Same syntax as APIFox"}, "templates": {"Load plugin error": "Load plugin error"}, "variable": {"Custom type": "Custom variable", "Custom type desc": "Define a global variable that does not require user input.\nThis variable's value can come from API interface, share link query, or be assigned through the [variable update] module.", "add option": "Add option", "input type": "Text", "key": "Variable key", "key is required": "Variable key is required", "select type": "Dropdown single select", "text max length": "Maximum length", "textarea type": "Paragraph", "textarea type desc": "Allows users to input up to 4000 characters", "variable name": "Variable name", "variable name is required": "Variable name cannot be empty", "variable option is required": "Options cannot be all empty", "variable option is value is required": "Option content cannot be empty", "variable options": "Options"}, "variable add option": "Add option", "variable_update": "variable update", "variable_update_info": "You can update the output value of the specified node or update global variables"}, "plugin": {"Custom headers": "Custom headers", "Free": "The plugin requires no points～", "Get Plugin Module Detail Failed": "Plugin loading failed", "Http plugin intro placeholder": "For display only, no actual effect", "cost": "Plugin cost: "}, "view_chat_detail": "View conversation details", "workflow": {"Can not delete node": "This node cannot be deleted", "Change input type tip": "Changing the input type will clear the filled values, please confirm!", "Check Failed": "Workflow validation failed, please check if the nodes are correctly filled and the connections are normal", "Confirm stop debug": "Confirm to stop debugging? Debugging information will not be preserved.", "Copy node": "Node copied", "Custom inputs": "Custom inputs", "Custom outputs": "Custom outputs", "Dataset quote": "Knowledge base quote", "Debug": "Debug", "Debug Node": "Debug mode", "Failed": "Execution failed", "Not intro": "This node has no introduction~", "Running": "Running", "Skipped": "Skipped execution", "Stop debug": "Stop debugging", "Success": "Execution successful", "Value type": "Data type", "Variable": {"Variable type": "Variable type"}, "debug": {"Done": "Debugging completed", "Hide result": "Hide result", "Not result": "No run result", "Run result": "Run result", "Show result": "Show result"}, "inputType": {"JSON Editor": "JSON Editor", "Manual input": "Manual input", "Manual select": "Manual select", "Reference": "Variable reference", "dynamicTargetInput": "Dynamic external data", "input": "Single line input box", "number input": "Number input box", "selectApp": "App selection", "selectDataset": "Knowledge base selection", "selectLLMModel": "Dialogue model selection", "switch": "Switch", "textarea": "Multi-line input box"}, "publish": {"OnRevert version": "Click to revert to this version", "OnRevert version confirm": "Confirm to revert to this version? It will save the configuration of the version being edited and create a new published version for the reverted version.", "histories": "Publishing records"}, "template": {"Multimodal": "Multimodal", "Search": "Search"}, "tool": {"Handle": "Tool connector", "Select Tool": "Select tool"}, "value": "Value", "variable": "Variable"}}, "create": "to create", "cron_job_run_app": "<PERSON><PERSON> <PERSON>", "dataset": {"Confirm move the folder": "Confirm moving to the folder", "Confirm to delete the data": "Confirm to delete the data?", "Confirm to delete the file": "Confirm to delete the file and all its data?", "Create Folder": "Create Folder", "Create manual collection": "Create manual collection", "Delete Dataset Error": "Delete Dataset Error", "Edit Folder": "Edit <PERSON>", "Edit Info": "Edit Information", "Export": "Export", "Export Dataset Limit Error": "Export Dataset Error", "Folder Name": "Enter folder name", "Insert Data": "Insert Data", "Manual collection Tip": "Manual collections allow you to create an empty container to store data", "Move Failed": "Move Failed~", "Select Dataset": "Select this dataset", "Select Dataset Tips": "Only datasets from the same index model can be selected", "Select Folder": "Enter folder", "Training Name": "Data Training", "collections": {"Collection Embedding": "{{total}} groups indexing", "Confirm to delete the folder": "Confirm to delete the folder and all its contents?", "Create And Import": "Create/Import", "Data Amount": "Total data", "Select Collection": "Select file", "Select One Collection To Store": "Select a file to store"}, "data": {"Add Index": "Add custom index", "Can not edit": "No editing permission", "Custom Index Number": "Custom Index {{number}}", "Default Index": "Default Index", "Delete Tip": "Confirm to delete this data?", "Index Edit": "Data Index", "Index Placeholder": "Enter index text content", "Input Data": "Import new data", "Input Success Tip": "Data imported successfully", "Update Data": "Update Data", "Update Success Tip": "Data updated successfully", "edit": {"Content": "Data content", "Course": "Documentation", "Delete": "Delete data", "Index": "Data Index ({{amount}})"}, "input is empty": "Data content cannot be empty"}, "deleteFolderTips": "Confirm to delete this folder and all the datasets it contains? Once deleted, data cannot be recovered. Please confirm!", "test": {"noResult": "No results found"}}, "default_reply": "<PERSON><PERSON><PERSON> reply", "error": {"Create failed": "Create failed", "fileNotFound": "File not found~", "inheritPermissionError": "Inherit permission Error", "missingParams": "Insufficient parameters", "team": {"overSize": "Team members exceed the limit"}, "upload_file_error_filename": "{{name}} upload failed"}, "extraction_results": "Extract results", "field_name": "Name", "get_QR_failed": "Failed to obtain QR code", "get_app_failed": "Failed to get app", "get_laf_failed": "Failed to get Laf function list", "has_verification": "Verified, click to cancel binding", "info": {"buy_extra": "Purchase additional packages", "csv_download": "Click to download batch test template", "csv_message": "Read the first column of the CSV file for batch testing, supporting up to 100 sets of data at a time.", "felid_message": "The field key must be pure English letters or numbers, and cannot start with a number.", "free_plan": "When a free version user does not log in to the system for 30 consecutive days, the system will automatically clean up the account knowledge base.", "include": "Includes standard package and additional resource package", "node_info": "Adjusting this module will have an impact on the timing of tool calls.\n\nYou can guide the model to make tool calls by accurately describing the function of the module.", "old_version_attention": "If it is detected that your advanced arrangement is an old version, the system will automatically format it into a new version of the workflow for you.\n\n\nDue to large version differences, some workflows may not be arranged normally. Please reconnect the workflows manually. \nIf the error persists, try deleting the corresponding node and adding it again.\n\n\nYou can directly click Debug to perform workflow testing, and click Publish after debugging is completed. \nThe new workflow will not actually save and take effect until you click Publish.\n\n\nAutosave will not take effect until you publish the new workflow.", "open_api_notice": "You can fill in the relevant secret key of OpenAI/OneAPI. \nIf you fill in this content, the online platform will use [AI Dialogue], [Problem Classification] and [Content Extraction] to retrieve the Key you filled in, and no charges will be made. \nPlease pay attention to whether your Key has permission to access the corresponding model. \nThe GPT model can choose FastAI.", "open_api_placeholder": "Request address, default is openai official. \nThe forwarding address can be filled in, but \"v1\" is not automatically completed.", "resource": "Resource usage"}, "invalid_variable": "Invalid variable", "is_open": "Opened", "item_description": "Field description", "item_name": "Field name", "key_repetition": "key duplicate", "navbar": {"Account": "Account", "Chat": "Cha<PERSON>", "Datasets": "Knowledge", "Studio": "Studio", "Tools": "Tools", "Prompt": "Prompt"}, "new_create": "Create New", "no_data": "No data", "no_laf_env": "The system is not configured with Laf environment", "not_yet_introduced": "No introduction yet", "pay": {"amount": "Amount", "balance": "Account balance", "balance_notice": "Insufficient account balance", "confirm_pay": "confirm payment", "get_pay_QR": "Get the recharge QR code", "need_pay": "Need to pay", "new_package_price": "New package price", "notice": "Do not close the page", "old_package_price": "Old package price", "other": "For other amounts, please round up the whole number", "to_recharge": "Insufficient balance, please recharge", "wechat": "Please scan the QR code on WeChat to pay:", "yuan": "Yuan"}, "permission": {"Collaborator": "collaborator", "Default permission": "Default permission", "Manage": "Manage", "No InheritPermission": "The permission has been restricted, and the parent folder's permission will not be inherited,", "Not collaborator": "Not collaborator", "Owner": "Owner", "Permission": "Permission", "Permission config": "Permission config", "Private": "Private", "Private Tip": "Only available to oneself", "Public": "Team", "Public Tip": "Available to all team members", "Remove InheritPermission Confirm": "This operation will cause to lose the current permission settings, whether to continue?", "Resume InheritPermission Confirm": "Whether to resume to inherit the parent folder's permission?", "Resume InheritPermission Failed": "Resume Failed", "Resume InheritPermission Success": "Resume Success"}, "plugin": {"App": "Select app", "Currentapp": "Current app", "Description": "Description", "Edit Http Plugin": "Edit HTTP plugin", "Enter PAT": "Please enter the access token (PAT)", "Get Plugin Module Detail Failed": "Failed to get plugin information", "Import Plugin": "Import HTTP plugin", "Import from URL": "Import from URL. https://xxxx", "Intro": "Plugin introduction", "Invalid Env": "Invalid laf environment", "Invalid Schema": "Invalid schema", "Invalid URL": "Invalid URL", "Method": "Method", "Path": "Path", "Please bind laf accout first": "Please bind laf account first", "Plugin List": "Plugin list", "Search plugin": "Search plugin", "Set Name": "Name the plugin", "contribute": "Contribute plugins", "go to laf": "Go to write", "path": "Path"}, "reply_now": "Reply immediately", "required": "must", "resume_failed": "Recovery failed", "select_reference_variable": "Select Reference Variable", "share_link": "Share Link", "support": {"account": {"Individuation": "Personalization"}, "inform": {"Read": "Read"}, "openapi": {"Api baseurl": "API base URL", "Api manager": "API key management", "Copy success": "API address copied", "New api key": "New API key", "New api key tip": "Please keep your key safe, it will not be shown again~"}, "outlink": {"Delete link tip": "Confirm to delete this no-login link? After deletion, the link will immediately become invalid, but the conversation logs will still be retained, please confirm!", "Max usage points": "Points limit", "Max usage points tip": "The maximum points this link can use. Exceeding this limit will make the link unusable. -1 means unlimited.", "Usage points": "Points used", "share": {"Response Quote": "Return quote", "Response Quote tips": "Return quote content in the share link, but will not allow users to download the original document"}}, "permission": {"Permission": "Permission"}, "standard": {"AI Bonus Points": "AI points", "Expired Time": "End time", "Start Time": "Start time", "storage": "Storage amount", "type": "Type"}, "team": {"limit": {"No permission rerank": "No permission to use result rerank, please upgrade your plan"}}, "user": {"Avatar": "Avatar", "Go laf env": "Click to go to {{env}} to get the PAT credential.", "Laf account course": "View the tutorial for binding laf account.", "Laf account intro": "After binding your laf account, you will be able to use laf modules in the workflow to write code online.", "Need to login": "Please login first", "Price": "Pricing standard", "User self info": "Personal information", "auth": {"Sending Code": "Sending"}, "inform": {"System message": "System message"}, "login": {"And": "and", "Email": "Email", "Forget Password": "Forgot password?", "Github": "Login with <PERSON><PERSON><PERSON>", "Google": "Login with Google", "Password": "Password", "Password login": "Password login", "Phone": "Login with phone number", "Phone number": "Phone number", "Policy tip": "Using represents your agreement to our", "Privacy": "Privacy policy", "Provider error": "Login error, please retry", "Register": "Register account", "Root login": "Login with root user", "Root password placeholder": "Root password is the environment variable you set", "Terms": "Terms of service", "Username": "Username", "Wechat": "Login with <PERSON><PERSON><PERSON>", "can_not_login": "Unable to log in, click to contact", "error": "Login exception", "security_failed": "Security check failed", "wx_qr_login": "Wechat QR code login"}, "logout": {"confirm": "Confirm to log out?"}, "team": {"Dataset usage": "Knowledge base capacity", "Team Tags Async Success": "Synchronization complete", "member": "Member"}}, "wallet": {"Ai point every thousand tokens": "{{points}} points/1K tokens", "Amount": "Amount", "Bills": "Bills", "Buy": "Buy", "Confirm pay": "Confirm payment", "Not sufficient": "Your AI points are insufficient, please upgrade your plan or buy additional AI points before continuing.", "Plan expired time": "Plan expiration time", "Plan reset time": "Plan reset time", "Standard Plan Detail": "Plan details", "To read plan": "View plan", "amount_0": "The purchase quantity cannot be 0", "bill": {"Number": "Order number", "Status": "Status", "Type": "Order type", "payWay": {"Way": "Payment method", "balance": "Balance payment", "wx": "Wechat payment"}, "status": {"closed": "Closed", "notpay": "Not paid", "refund": "Refunded", "success": "Payment successful"}}, "buy_resource": "Purchase resource pack", "moduleName": {"index": "Index generation", "qa": "QA split"}, "noBill": "No bill records~", "subscription": {"AI points": "AI points", "AI points click to read tip": "Each call to the AI model will consume a certain amount of AI points (similar to Tokens). Click to view detailed calculation rules.", "AI points usage": "AI points usage", "AI points usage tip": "Each call to the AI model will consume a certain amount of AI points. The specific calculation standard can be referred to the 'Pricing standard' above", "Ai points": "AI points calculation standard", "Buy now": "Switch plan", "Current plan": "Current plan", "Extra ai points": "Extra AI points", "Extra dataset size": "Extra knowledge base capacity", "Extra plan": "Extra resource pack", "Extra plan tip": "When the standard plan is not enough, you can buy extra resource packs to continue using", "FAQ": "Frequently asked questions", "Month amount": "Months", "Next plan": "Future plan", "Nonsupport": "Unable to switch", "Stand plan level": "Subscription plan", "Standard update fail": "Failed to modify subscription plan", "Standard update success": "Subscription plan change successful!", "Sub plan": "Subscription plan", "Sub plan tip": "Free use of {{title}} or upgrade to a higher plan", "Team plan and usage": "Plan and usage", "Training weight": "Training priority: {{weight}}", "Update extra ai points": "Extra AI points", "Update extra dataset size": "Extra storage", "Upgrade plan": "Upgrade plan", "ai_model": "AI language model", "function": {"History store": "{{amount}} days of conversation history retention", "Max app": "{{amount}} apps", "Max dataset": "{{amount}} knowledge bases", "Max dataset size": "{{amount}} sets of knowledge base indexes", "Max members": "{{amount}} team members", "Points": "{{amount}} AI points"}, "mode": {"Month": "Monthly", "Period": "Subscription period", "Year": "Yearly", "Year sale": "Two months free"}, "point": "integral", "standardSubLevel": {"enterprise": "Enterprise edition", "experience": "Experience edition", "free": "Free edition", "free desc": "Basic functions can be used for free every month, and the knowledge base will be cleared if there is no usage record for 30 days", "team": "Team edition"}, "token_compute": "Click to view the online Tokens calculator", "type": {"balance": "Balance recharge", "extraDatasetSize": "Knowledge base expansion", "extraPoints": "AI points package", "standard": "Plan subscription"}}, "usage": {"Ai model": "AI model", "App name": "App name", "Audio Speech": "Audio playback", "Bill Module": "Billing module", "Duration": "Duration (seconds)", "Extension result": "Question optimization result", "Module name": "Module name", "Source": "Source", "Text Length": "Text length", "Time": "Generation time", "Token Length": "Token length", "Total": "Total amount", "Total points": "AI points consumed", "Usage Detail": "Usage details", "Whisper": "Voice input"}}}, "sync_link": "Sync Link", "system": {"Concat us": "Concat", "Help Document": "Help document"}, "tag_list": "Tag List", "team_tag": "Team Tag", "template": {"Quote Content Tip": "You can customize the structure of the quote content to better adapt to different scenarios. You can use some variables for template configuration:\n{{q}} - search content, {{a}} - expected content, {{source}} - source, {{sourceId}} - source file name, {{index}} - the nth quote, they are all optional, here are the default values:\n{{default}}", "Quote Prompt Tip": "You can use {{quote}} to insert the quote content template, and use {{question}} to insert the question. Here are the default values:\n{{default}}"}, "textarea_variable_picker_tip": "Input / to select variables", "tool_field": "Tool field parameter configuration", "undefined_var": "An undefined variable is referenced. Is it automatically added?", "unit": {"character": "character", "minute": "minute"}, "unusable_variable": "no usable variable", "upload_file_error": "Upload file error", "user": {"Account": "Account", "Amount of earnings": "Earnings (¥)", "Amount of inviter": "Total number of invitees", "Application Name": "Project name", "Avatar": "Avatar", "Change": "Change", "Copy invite url": "Copy invite link", "Edit name": "Click to edit nickname", "Invite Url": "Invite link", "Invite url tip": "Friends registered through this link will be permanently bound to you, and you will receive a certain balance reward when they recharge.\nIn addition, you will immediately receive a 5 yuan reward when friends register with a phone number.\nThe reward will be sent to your default team.", "Laf Account Setting": "laf account settings", "Language": "Language", "Member Name": "Nickname", "Notice": "Notice", "Notification Receive": "Notification Receive", "Notification Receive Bind": "Please bind notification receive method", "Old password is error": "Old password is incorrect", "OpenAI Account Setting": "OpenAI account settings", "Password": "Password", "Pay": "Recharge", "Personal Information": "Personal information", "Promotion": "Promotion", "Promotion Rate": "Cashback ratio", "Promotion Record": "Promotion record", "Promotion rate tip": "You will receive a certain percentage of balance reward when your friend recharges", "Replace": "Replace", "Set OpenAI Account Failed": "Failed to set OpenAI account", "Sign Out": "Sign out", "Team": "Team", "Time": "Time", "Timezone": "Timezone", "Update Password": "Update password", "Update password failed": "Failed to update password", "Update password successful": "Password updated successfully", "Usage Record": "Usage record", "apikey": {"key": "API key"}, "confirm_password": "Confirm Password", "new_password": "New Password", "no_invite_records": "No invitation record yet", "no_notice": "No notification yet", "no_usage_records": "No usage record yet", "old_password": "Old Password", "password_message": "Password must be at least 4 characters and at most 60 characters", "team": {"Balance": "Team Balance", "Check Team": "Switch", "Confirm Invite": "Confirm Invitation", "Create Team": "Create New Team", "Invite Member": "Invite Member", "Invite Member Failed Tip": "An error occurred while inviting members", "Invite Member Result Tip": "Invitation Result Tip", "Invite Member Success Tip": "Invitation completed\nSuccess: {{success}}\nInvalid usernames: {{inValid}}\nAlready in team: {{inTeam}}", "Invite Member Tips": "The invitee can access or use other resources within the team", "Leave Team": "Leave Team", "Leave Team Failed": "Failed to leave team", "Member": "Member", "Member Name": "Member Name", "Over Max Member Tip": "Maximum of {{max}} members in the team", "Personal Team": "Personal Team", "Processing invitations": "Processing Invitations", "Processing invitations Tips": "You have {{amount}} team invitations to process", "Remove Member Confirm Tip": "Confirm to remove {{username}} from the team? All their resources will be transferred to the team creator's account.", "Select Team": "Select Team", "Set Name": "Name Your Team", "Switch Team Failed": "Failed to switch team", "Tags Async": "Save", "Team Name": "Team Name", "Team Tags Async": "Tag Sync", "Team Tags Async Success": "Link error corrected, tag information updated", "Update Team": "Update Team Information", "invite": {"Accept Confirm": "Confirm to join this team?", "Accepted": "Joined Team", "Deal Width Footer Tip": "Will automatically close after processing~", "Reject": "Invitation Rejected", "Reject Confirm": "Confirm to reject this invitation?", "accept": "Accept", "reject": "Reject"}, "member": {"Confirm Leave": "Confirm to leave this team?", "active": "Joined", "reject": "Reject", "waiting": "Waiting for Acceptance"}, "role": {"Admin": "Admin", "Owner": "Owner", "Visitor": "Visitor"}}, "type": "type"}, "verification": "verify", "prompt": {"title": "Prompt Templates", "search_prompt": "Search templates...", "Add Template": "Add Template", "No description": "No description", "No matching templates": "No matching templates", "No Templates": "No Templates", "Template Title": "Template Title", "Enter template title": "Enter template title", "Template Description": "Template Description", "Enter template description": "Enter template description", "Create": "Create", "Cancel": "Cancel", "Title is required": "Title is required", "Create Template Success": "Template created successfully", "Create Template Failed": "Failed to create template", "Failed to load templates": "Failed to load templates", "Add Content": "Add Content", "No Content": "No Content", "Edit Content": "Edit Content", "Content Title": "Content Title", "Enter content title": "Enter content title", "Content": "Content", "Enter content": "Enter content", "Update": "Update", "Add": "Add", "Title and content are required": "Title and content are required", "Add Content Success": "Content added successfully", "Add Content Failed": "Failed to add content", "Update Content Success": "Content updated successfully", "Update Content Failed": "Failed to update content", "Delete Content Confirm": "Are you sure you want to delete this content?", "Delete Content Success": "Content deleted successfully", "Delete Content Failed": "Failed to delete content", "Copy Success": "<PERSON><PERSON>d successfully", "Copy Failed": "Failed to copy", "Copy": "Copy", "Failed to load template": "Failed to load template"}}