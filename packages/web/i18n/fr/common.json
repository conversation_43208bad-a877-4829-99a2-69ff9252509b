{"App": "Application", "Export": "Exporter", "FAQ": {"ai_point_a": "Chaque appel au modèle d'IA consomme un certain nombre de points d'IA. Pour les détails de calcul, ve<PERSON><PERSON><PERSON> consulter les \"Standards de calcul des points d'IA\" ci-dessus.\nLe calcul des tokens utilise la même formule que GPT3.5, 1 Token ≈ 0,7 caractères chinois ≈ 0,9 mots anglais, les caractères consécutifs peuvent être considérés comme 1 Token.", "ai_point_expire_a": "<PERSON><PERSON>, ils expirent. Lorsque votre abonnement actuel expire, les points d'IA seront effacés et remplacés par ceux du nouvel abonnement. Les points d'IA de l'abonnement annuel sont valables pour 1 an, et non mensuellement.", "ai_point_expire_q": "Les points d'IA expirent-ils ?", "ai_point_q": "Que sont les points d'IA ?", "auto_renew_a": "Lorsque votre abonnement actuel expire, le système renouvellera automatiquement selon le \"Futur abonnement\". Le système essaiera de prélever le montant sur votre solde de compte. Si vous souhaitez un renouvellement automatique, veuillez prévoir un solde suffisant.", "auto_renew_q": "L'abonnement se renouvelle-t-il automatiquement ?", "change_package_a": "Si le prix de l'abonnement actuel est supérieur au nouveau, vous ne pourrez pas changer immédiatement et le changement se fera sous forme de \"renouvellement\" après l'expiration de l'abonnement actuel.\nSi le prix de l'abonnement actuel est inférieur au nouveau, le système calculera automatiquement le solde restant et vous pourrez payer la différence pour changer d'abonnement.", "change_package_q": "<PERSON>ui<PERSON>-je changer d'abonnement ?", "dataset_compute_a": "1 stockage de base de connaissances équivaut à 1 index de base de connaissances. Une donnée de base de connaissances peut contenir 1 ou plusieurs index. Dans l'entraînement avancé, 1 donnée génère 5 index.", "dataset_compute_q": "Comment le stockage de la base de connaissances est-il calculé ?", "dataset_index_a": "Non. Mais lorsque les index dépassent la limite, vous ne pourrez pas insérer ou mettre à jour le contenu de la base de connaissances.", "dataset_index_q": "Les index de la base de connaissances seront-ils supprimés en cas de dépassement ?", "free_user_clean_a": "Pour les équipes en version gratuite (version gratuite sans achat d'abonnement supplémentaire), si aucune connexion n'est effectuée pendant 30 jours consécutifs, le système supprimera automatiquement tout le contenu des bases de connaissances de cette équipe.", "free_user_clean_q": "Les données de la version gratuite seront-elles supprimées ?", "package_overlay_a": "Oui. Chaque pack de ressources acheté est indépendant et s'additionnera pendant sa période de validité. Les points d'IA seront d'abord déduits du pack qui expire en premier.", "package_overlay_q": "Les packs de ressources supplémentaires peuvent-ils s'additionner ?"}, "Folder": "Dossier", "Login": "Connexion", "Move": "<PERSON><PERSON><PERSON><PERSON>", "Name": "Nom", "Rename": "<PERSON>mmer", "Resume": "Reprendre", "Running": "En cours d'exécution", "UnKnow": "Inconnu", "Warning": "Avertissement", "add_new": "Ajouter", "chose_condition": "Choisir une condition", "chosen": "Sélectionné", "classification": "Classification", "click_to_resume": "Cliquer pour reprendre", "code_editor": "Éditeur de code", "code_error": {"app_error": {"invalid_app_type": "Type d'application incorrect", "invalid_owner": "Propriétaire de l'application non valide", "not_exist": "L'application n'existe pas", "un_auth_app": "Pas d'autorisation pour cette application"}, "chat_error": {"un_auth": "Pas d'autorisation pour accéder à cette conversation"}, "error_code": {"400": "Échec de la requête", "401": "Pas d'accès autorisé", "403": "Accès restreint", "404": "Requête inexistante", "405": "<PERSON><PERSON><PERSON><PERSON> de requête <PERSON>e", "406": "Format de requête incorrect", "410": "Ressource supprimée", "422": "Erreur de validation", "500": "<PERSON><PERSON><PERSON> serveur", "502": "<PERSON><PERSON><PERSON> de passerelle", "503": "Serveur temporairement surchargé ou en maintenance", "504": "<PERSON><PERSON><PERSON>erelle d<PERSON>"}, "error_message": {"403": "Identifiants incorrects", "510": "Solde du compte insuffisant", "511": "Pas d'autorisation pour utiliser ce modèle", "513": "Pas d'autorisation pour lire ce fichier", "514": "Clé API non valide"}, "openapi_error": {"api_key_not_exist": "La clé API n'existe pas", "exceed_limit": "Maximum 10 groupes de clés API", "un_auth": "Pas d'autorisation pour cette clé API"}, "outlink_error": {"invalid_link": "Lien de partage non valide", "link_not_exist": "Le lien de partage n'existe pas", "un_auth_user": "Échec de vérification d'identité"}, "plugin_error": {"not_exist": "Le plugin n'existe pas", "un_auth": "Pas d'autorisation pour ce plugin"}, "system_error": {"community_version_num_limit": "Limite de <PERSON> dépass<PERSON>"}, "team_error": {"ai_points_not_enough": "", "app_amount_not_enough": "Nombre maximum d'applications atteint~", "dataset_amount_not_enough": "Nombre maximum de bases de connaissances atteint~", "dataset_size_not_enough": "Capacité de la base de connaissances insuffisante, veuillez d'abord augmenter la capacité~", "over_size": "error.team.overSize", "plugin_amount_not_enough": "Nombre maximum de plugins atteint~", "re_rank_not_enough": "Pas d'autorisation pour utiliser le reclassement~", "un_auth": "Pas d'autorisation pour cette équipe", "website_sync_not_enough": "Pas d'autorisation pour la synchronisation de sites Web~"}, "token_error_code": {"403": "Session invalide, ve<PERSON><PERSON>z vous reconnecter"}, "user_error": {"balance_not_enough": "Solde du compte insuffisant~", "bin_visitor": "Votre vérification d'identité a échoué", "bin_visitor_guest": "Vous êtes actuellement un visiteur, opération non autorisée", "un_auth_user": "Utilisateur introuvable"}}, "common": {"Action": "Action", "Add": "Ajouter", "Add New": "Nouveau", "Add Success": "<PERSON><PERSON><PERSON>", "All": "<PERSON>ut", "Cancel": "Annuler", "Choose": "Choi<PERSON>", "Close": "<PERSON><PERSON><PERSON>", "Config": "Configuration", "Confirm": "Confirmer", "Confirm Create": "Confirmer la création", "Confirm Import": "Confirmer l'importation", "Confirm Move": "<PERSON><PERSON><PERSON><PERSON> ici", "Confirm Update": "Confirmer la mise à jour", "Confirm to leave the page": "Confirmer quitter cette page ?", "Copy": "<PERSON><PERSON><PERSON>", "Copy Successful": "<PERSON><PERSON> r<PERSON>", "Create Failed": "Création échouée", "Create New": "<PERSON><PERSON><PERSON>", "Create Success": "Création réussie", "Create Time": "Date de création", "Creating": "Création en cours", "Custom Title": "<PERSON><PERSON><PERSON>", "Delete": "<PERSON><PERSON><PERSON><PERSON>", "Delete Failed": "Suppression échouée", "Delete Success": "Suppression réussie", "Delete Warning": "Avertissement de suppression", "Delete folder": "Supp<PERSON>er le dossier", "Detail": "Détails", "Documents": "Documents", "Done": "<PERSON><PERSON><PERSON><PERSON>", "Edit": "Modifier", "Exit": "<PERSON><PERSON><PERSON>", "Expired Time": "Date d'expiration", "Field": "<PERSON><PERSON>", "File": "<PERSON><PERSON><PERSON>", "Finish": "<PERSON><PERSON><PERSON>", "Import": "Importer", "Import failed": "Importation échouée", "Import success": "Importation réussie", "Input": "Entrée", "Input folder description": "Description du dossier", "Input name": "<PERSON><PERSON> un nom", "Intro": "Introduction", "Last Step": "Étape <PERSON>", "Last use time": "Dernière utilisation", "Load Failed": "Chargement échoué", "Loading": "Chargement...", "More": "Plus", "Move": "<PERSON><PERSON><PERSON><PERSON>", "MultipleRowSelect": {"No data": "Aucune valeur disponible"}, "Name": "Nom", "Next Step": "Étape suiva<PERSON>", "No more data": "Plus de données~", "Not open": "Non activé", "OK": "OK", "Open": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Operation": "Opération", "Other": "<PERSON><PERSON>", "Output": "<PERSON><PERSON><PERSON>", "Params": "Paramètres", "Password inconsistency": "Les mots de passe ne correspondent pas", "Permission": "Permission", "Please Input Name": "Veuillez saisir un nom", "Read document": "Voir le document", "Read intro": "Voir l'introduction", "Remove": "<PERSON><PERSON><PERSON>", "Rename": "<PERSON>mmer", "Request Error": "<PERSON><PERSON><PERSON> <PERSON>u<PERSON>", "Require Input": "Obligatoire", "Restart": "Recommencer", "Role": "R<PERSON><PERSON>", "Root folder": "Dossier racine", "Run": "Exécuter", "Save": "Enregistrer", "Save Failed": "Enregistrement échoué", "Save Success": "Enregistrement réussi", "Search": "<PERSON><PERSON><PERSON>", "Select File Failed": "Sélection de fichier échouée", "Select template": "Sélectionner un modèle", "Set Avatar": "Définir l'avatar", "Set Name": "Définir un nom", "Setting": "Paramètres", "Status": "Statut", "Submit failed": "Soumission échouée", "Success": "Su<PERSON>ès", "Sync success": "Synchronisation réussie", "Team": "Équipe", "Team Tags Set": "Étiquettes", "Un used": "Non utilisé", "UnKnow": "Inconnu", "UnKnow Source": "Source inconnue", "Unlimited": "Illimité", "Update": "Mettre à jour", "Update Failed": "Mise à jour échouée", "Update Success": "Mise à jour réussie", "Update Successful": "Mise à jour réussie", "Upload File Failed": "Échec du téléchargement", "Username": "Nom d'utilisateur", "Waiting": "En attente", "Warning": "Avertissement", "Website": "Site web", "all_result": "Résultat complet", "avatar": {"Select Avatar": "Cliquer pour sélectionner un avatar", "Select Failed": "Sélection d'avatar é<PERSON><PERSON>e"}, "choosable": "Sélectionnable", "confirm": {"Common Tip": "Confirmation d'opération"}, "course": {"Read Course": "Voir le tutoriel"}, "empty": {"Common Tip": "<PERSON><PERSON><PERSON> donnée disponible~"}, "error": {"Select avatar failed": "Sélection d'avatar é<PERSON><PERSON>e", "too_many_request": "<PERSON><PERSON> de requ<PERSON>, ve<PERSON><PERSON><PERSON> réessayer plus tard.", "unKnow": "Une erreur inattendue s'est produite~"}, "failed": "<PERSON><PERSON><PERSON>", "folder": {"Drag Tip": "Cliquez pour faire glisser", "Move Success": "Déplacement réussi", "Move to": "<PERSON><PERSON><PERSON><PERSON> vers", "No Folder": "Plus de sous-dossiers, placez-le ici", "Open folder": "<PERSON><PERSON><PERSON><PERSON><PERSON> le dossier", "Root Path": "Dossier racine", "empty": "Ce dossier n'a plus rien à sélectionner~", "open_dataset": "Ouv<PERSON>r la base de connaissances"}, "input": {"Repeat Value": "Valeurs en double"}, "is_requesting": "Requête en cours...", "jsonEditor": {"Parse error": "Erreur possible dans le JSON, veuillez vérifier attentivement"}, "link": {"UnValid": "<PERSON>n invalide"}, "month": "mois", "name_is_empty": "Le nom ne peut pas être vide", "no_intro": "Pas d'introduction", "not_support": "Non pris en charge", "page_center": "<PERSON>r la page", "request_end": "<PERSON>ut chargé", "request_more": "Cliquer pour charger plus", "speech": {"error tip": "Échec de la conversion vocale en texte", "not support": "Votre navigateur ne prend pas en charge l'entrée vocale"}, "support": "<PERSON><PERSON> en charge", "system": {"Commercial version function": "Veuillez passer à la version commerciale pour utiliser cette fonction : https://fastgpt.in", "Help Chatbot": "Assistant robot", "Use Helper": "Aide à l'utilisation"}, "ui": {"textarea": {"Magnifying": "<PERSON><PERSON><PERSON><PERSON>"}}}, "confirm_choice": "Confirmer la sélection", "core": {"Chat": "Conversation", "Max Token": "Limite par donnée", "ai": {"AI settings": "Configuration IA", "Ai point price": "Consommation de points IA", "Max context": "Contexte maximal", "Model": "Modèle IA", "Not deploy rerank model": "Modèle de reclassement non déployé", "Prompt": "Instruction", "Support tool": "Appel de fonction", "model": {"Dataset Agent Model": "Modèle de traitement de fichiers", "Vector Model": "Modèle d'indexation", "doc_index_and_dialog": "Index de documents & index de conversations"}}, "app": {"Ai response": "Réponse IA", "Api request": "Accès API", "Api request desc": "Intégration à des systèmes existants ou à WeChat Enterprise, Feishu, etc. via API", "App intro": "Introduction à l'application", "Chat Variable": "Variables de conversation", "Config schedule plan": "Configurer l'exécution programmée", "Config whisper": "Configurer l'entrée vocale", "Interval timer config": "Configuration d'exécution programmée", "Interval timer run": "Exécution programmée", "Interval timer tip": "Peut exécuter l'application selon un calendrier", "Make a brief introduction of your app": "Donnez une introduction à votre application IA", "Max histories": "Nombre d'historiques", "Max tokens": "Limite de réponse", "Name and avatar": "Avatar & nom", "Onclick to save": "Cliquer pour enregistrer", "Publish": "Publier", "Publish Confirm": "Confirmer la publication ? Cela mettra immédiatement à jour l'application sur tous les canaux de publication.", "Publish app tip": "Après publication, tous les canaux utiliseront immédiatement cette version", "Question Guide": "Suggestions de questions", "Question Guide Tip": "Après la conversation, 3 questions d'orientation seront généré<PERSON>.", "Quote prompt": "Instruction de citation", "Quote templates": "<PERSON><PERSON><PERSON><PERSON>", "Random": "Divergent", "Saved time": "Enregistré : {{time}}", "Search team tags": "Rechercher des étiquettes", "Select TTS": "Sélectionner le mode de lecture vocale", "Select app from template": "Sélectionner depuis un modèle", "Select quote template": "Sélectionner un modèle d'instruction de citation", "Set a name for your app": "Donnez un nom à votre application", "Setting ai property": "Cliquer pour configurer les propriétés du modèle IA", "Share link": "Fenêtre sans connexion", "Share link desc": "Partager un lien permettant une utilisation directe sans connexion", "Share link desc detail": "Vous pouvez partager ce modèle directement avec d'autres utilisateurs pour converser, sans qu'ils aient besoin de se connecter. Attention, cette fonction consommera le solde de votre compte, protégez bien le lien !", "TTS": "Lecture vocale", "TTS Tip": "Une fois activée, la fonction de lecture vocale sera disponible après chaque conversation. Cette fonction peut entraîner des frais supplémentaires.", "TTS start": "<PERSON><PERSON> le contenu", "Team tags": "Étiquettes d'équipe", "Temperature": "Température", "Tool call": "A<PERSON> d'outil", "ToolCall": {"No plugin": "Aucun plugin disponible", "Parameter setting": "Paramètres d'entrée", "System": "Système", "Team": "Mes plugins", "Public": "Public", "This plugin cannot be called as a tool": "Cet outil ne peut pas être utilisé en mode simplifié"}, "Welcome Text": "Message d'accueil", "Whisper": "Entrée vocale", "Whisper config": "Configuration de l'entrée vocale", "deterministic": "<PERSON><PERSON><PERSON><PERSON>", "edit": {"Prompt Editor": "Éditeur d'instructions", "Query extension background prompt": "Description du contexte de conversation", "Query extension background tip": "<PERSON><PERSON><PERSON><PERSON>z le cadre de la conversation actuelle pour aider l'IA à compléter et étendre la question. Le contenu saisi correspond généralement à cet assistant"}, "edit_content": "Modifier les informations", "error": {"App name can not be empty": "Le nom de l'application ne peut pas être vide", "Get app failed": "Échec de récupération de l'application"}, "feedback": {"Custom feedback": "Retour personnalis<PERSON>", "close custom feedback": "<PERSON><PERSON><PERSON> le retour"}, "have_publish": "<PERSON><PERSON><PERSON>", "loading": "Chargement", "logs": {"Source And Time": "Source & heure"}, "no_app": "Pas encore d'application, créez-en une !", "not_published": "Non publié", "outLink": {"Can Drag": "L'icône peut être déplacée", "Default open": "Ouvert par défaut", "Iframe block title": "Copiez l'iframe ci-dessous dans votre site web", "Link block title": "Co<PERSON>z le lien ci-dessous et ouvrez-le dans un navigateur", "Script Close Icon": "Icône de fermeture", "Script Open Icon": "Icône d'ouverture", "Script block title": "Ajoutez le code ci-dessous à votre site web", "Select Mode": "Commencer à utiliser", "Select Using Way": "Choisir le mode d'utilisation", "Show History": "Afficher l'historique des conversations"}, "publish": {"Fei Shu Bot Desc": "Intégrer au robot Feishu", "Fei shu bot": "<PERSON><PERSON><PERSON>", "Fei shu bot publish": "Publier sur le robot Feishu", "FOFPro Desc": "Publier sur FOFPro", "FOFPro": "FOFPro", "FOFPro publish": "Publier sur FOFPro, accès via le site web et mini-programme FOFPro", "BenZui publish": "Publier sur BenZui, accès via le mini-programme BenZui", "TOWA Store Desc": "Publier sur TOWA Store", "Edu Store Desc": "Publier sur Siwei Lian Store", "BenZui Store Desc": "Publier sur BenZui", "TOWA Store": "TOWA Store", "Edu Store": "Siwei Lian Store", "BenZui Store": "<PERSON><PERSON><PERSON>", "TOWA Store publish": "Si vous souhaitez publier votre application sur TOWA Store, scannez le QR code pour contacter l'assistant", "Publish": "Publier", "Cancel Publish": "Annuler la publication"}, "schedule": {"Default prompt": "Question par défaut", "Default prompt placeholder": "Question par défaut lors de l'exécution de l'application", "Every day": "<PERSON>que jour à {{hour}}:00", "Every month": "Le {{day}} de chaque mois à {{hour}}:00", "Every week": "Chaque {{day}} à {{hour}}:00", "Interval": "Toutes les {{interval}} heures", "Open schedule": "Exécution programmée"}, "setting": "Paramètres de l'application", "share": {"Amount limit tip": "Maximum 10 groupes", "Create link": "Créer un nouveau lien", "Create link tip": "Création réussie. Adresse de partage copiée, prête à être partagée", "Ip limit title": "Limite IP (personnes/minute)", "Is response quote": "<PERSON><PERSON><PERSON> les citations", "Not share link": "Aucun lien de partage créé", "Role check": "Vérification d'identité"}, "tip": {"Add a intro to app": "Ajoutez une introduction à votre application~", "chatNodeSystemPromptTip": "Instructions fixes pour le modèle qui guident la direction de la conversation. Ce contenu sera fixé au début du contexte. Insérez des variables en tapant /\nSi vous avez lié une base de connaissances, vous pouvez également guider le modèle sur quand utiliser la recherche dans la base. Par exemple :\nVous êtes un assistant pour le film \"Interstellar\". Lorsque l'utilisateur pose des questions sur \"Interstellar\", recherchez dans la base de connaissances et répondez en combinant les résultats de recherche.", "variableTip": "Vous pouvez demander aux utilisateurs de remplir certaines informations avant de commencer la conversation, qui serviront de variables spécifiques. Ce module apparaît après le message d'accueil.\nLes variables peuvent être injectées dans d'autres modules de type string via la syntaxe {{nom_variable}}, par exemple dans les instructions, les mots limites, etc.", "welcomeTextTip": "Contenu initial envoyé au début de chaque conversation. Prend en charge la syntaxe Markdown standard, avec des balises supplémentaires :\n[Bouton rapide] : l'utilisateur peut envoyer directement cette question en cliquant"}, "tool_label": {"doc": "Documentation", "github": "<PERSON><PERSON><PERSON>", "price": "Informations de tarification"}, "tts": {"Close": "Ne pas utiliser", "Speech model": "<PERSON><PERSON><PERSON><PERSON> vocal", "Speech speed": "Vitesse", "Test Listen": "Tester", "Test Listen Text": "<PERSON><PERSON><PERSON>, ceci est un test vocal. Si vous entendez cette phrase, la fonction de lecture vocale fonctionne correctement", "Web": "Navigateur intégré (gratuit)"}, "whisper": {"Auto send": "Envoi automatique", "Auto send tip": "Envoyer directement après la saisie vocale, sans cliquer sur le bouton d'envoi", "Auto tts response": "Réponse vocale automatique", "Auto tts response tip": "Les questions envoyées par entrée vocale recevront directement une réponse vocale. Assurez-vous que la fonction de lecture vocale est activée.", "Close": "Désactivé", "Not tts tip": "Vous n'avez pas activé la lecture vocale, cette fonction ne peut pas être utilisée", "Open": "Activé", "Switch": "Activer l'entrée vocale"}}, "chat": {"Admin Mark Content": "Réponse corrigée", "Audio Not Support": "Votre appareil ne prend pas en charge la lecture audio", "Audio Speech Error": "Erreur de lecture vocale", "Cancel Speak": "Annuler l'entrée vocale", "Chat API is error or undefined": "Erreur d'API de conversation ou réponse vide", "Confirm to clear history": "Confirmer l'effacement de l'historique des conversations en ligne pour cette application ? Les historiques de partage et d'appels API ne seront pas effacés.", "Confirm to clear share chat history": "Confirmer la suppression de tout l'historique des conversations ?", "Converting to text": "Conversion en texte...", "Custom History Title": "Titre personnalisé pour l'historique", "Custom History Title Description": "Si laissé vide, suivra automatiquement l'historique des conversations.", "Debug test": "Aperçu de débogage", "Exit Chat": "<PERSON><PERSON><PERSON> la <PERSON>", "Failed to initialize chat": "Échec d'initialisation de la conversation", "Feedback Failed": "Échec d'envoi du retour", "Feedback Modal": "Retour sur la réponse", "Feedback Modal Tip": "<PERSON><PERSON><PERSON> ce qui ne vous satisfait pas dans la réponse", "Feedback Submit": "<PERSON><PERSON><PERSON><PERSON> le retour", "Feedback Success": "Retour envoyé avec succès !", "Finish Speak": "Entrée vocale terminée", "History": "Historique", "History Amount": "{{amount}} enregistrements", "Mark": "Annoter la réponse attendue", "Mark Description": "La fonction d'annotation est en version bêta.\n\nAprès avoir cliqué pour ajouter une annotation, vous devrez sélectionner une base de connaissances pour stocker les données d'annotation. Cette fonction vous permet d'annoter rapidement des questions et des réponses attendues pour guider les futures réponses du modèle.\n\nActuellement, comme pour les autres données de la base de connaissances, les annotations sont soumises à l'influence du modèle et ne garantissent pas une correspondance à 100% avec les attentes.\n\nLes données d'annotation sont synchronisées de manière unidirectionnelle avec la base de connaissances. Si vous modifiez ces données dans la base, l'annotation affichée dans les journaux ne sera pas mise à jour.", "Mark Description Title": "Introduction à la fonction d'annotation", "New Chat": "Nouvelle conversation", "Pin": "<PERSON><PERSON><PERSON>", "Question Guide": "Suggestions de questions", "Quote": "Citation", "Quote Amount": "Citations de la base ({{amount}})", "Read Mark Description": "Voir l'introduction à la fonction d'annotation", "Recent use": "Utilisation récente", "Record": "Entrée vocale", "Restart": "Redémarrer la conversation", "Select Image": "Sélectionner une image", "Select dataset": "Sélectionner une base de connaissances", "Select dataset Desc": "Sélectionnez une base de connaissances pour stocker la réponse attendue", "Send Message": "Envoyer", "Speaking": "J'é<PERSON>ute, parlez...", "Start Chat": "Commencer la conversation", "Type a message": "Saisis<PERSON>z une question, envoyez [Entrée]/nouvelle ligne [Ctrl(Alt/Shift) + Entrée]", "Unpin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "You need to a chat app": "Vous n'avez pas d'application disponible", "error": {"Chat error": "Erreur de conversation", "Messages empty": "Contenu de l'interface vide, le texte est peut-être trop long~", "Select dataset empty": "Vous n'avez pas sélectionné de base de connaissances", "User input empty": "La question de l'utilisateur est vide", "data_error": "Erreur de récupération des données"}, "feedback": {"Close User Like": "L'utilisateur a approuvé\nCliquez pour fermer ce marqueur", "Feedback Close": "<PERSON><PERSON><PERSON> le retour", "No Content": "L'utilisateur n'a pas fourni de contenu spécifique", "Read User dislike": "L'utilisateur a désapprouvé\nCliquez pour voir le contenu"}, "logs": {"api": "Appel API", "online": "Utilisation en ligne", "share": "Appel de lien externe", "test": "Test", "published": "Application publiée"}, "markdown": {"Edit Question": "Modifier la question", "Quick Question": "Cliquez pour poser cette question", "Send Question": "Envoyer la question"}, "quote": {"Quote Tip": "Seul le contenu réellement cité est affiché ici. Si les données sont mises à jour, cet affichage ne sera pas actualisé en temps réel", "Read Quote": "Voir la citation"}, "response": {"Complete Response": "Réponse complète", "Extension model": "Modèle d'optimisation de question", "Plugin response detail": "Détails du plugin", "Read complete response": "Voir les détails", "Read complete response tips": "Cliquez pour voir le processus détaillé", "Tool call response detail": "Détails d'exécution de l'outil", "Tool call tokens": "Consommation de tokens pour l'appel d'outil", "context total length": "Longueur totale du contexte", "module cq": "Liste de classification des questions", "module cq result": "Résultat de classification", "module extract description": "Extraction de la description de contexte", "module extract result": "Résultat d'extraction", "module historyPreview": "Aperçu de l'historique (contenu partiel)", "module http result": "Corps de réponse", "module if else Result": "Résultat du discriminateur", "module limit": "Limite par recherche", "module maxToken": "Tokens maximum de réponse", "module model": "<PERSON><PERSON><PERSON><PERSON>", "module name": "Nom du modèle", "module query": "Question/terme de recherche", "module quoteList": "Contenu cité", "module similarity": "Similarité", "module temperature": "Température", "module time": "Durée d'exécution", "module tokens": "<PERSON><PERSON><PERSON> to<PERSON>ux", "plugin output": "Sortie du plugin", "search using reRank": "Reclassement des résultats", "text output": "Sortie texte"}, "retry": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tts": {"Stop Speech": "<PERSON><PERSON><PERSON><PERSON>"}}, "common": {"tip": {"leave page": "Le contenu a été modifié, confirmez-vous vouloir quitter la page ?"}}, "dataset": {"All Dataset": "Toutes les bases de connaissances", "Avatar": "Avatar de la base de connaissances", "Choose Dataset": "Associer une base de connaissances", "Collection": "Collection", "Create dataset": "<PERSON><PERSON>er une base de connaissances", "Dataset": "Base de connaissances", "Dataset ID": "ID de la base", "Dataset Type": "Type de base", "Delete Confirm": "Confirmer la suppression de cette base de connaissances ? Les données ne pourront pas être récupérées après suppression, veuil<PERSON><PERSON> confirmer !", "Empty Dataset": "Base de données vide", "Empty Dataset Tips": "Pas encore de base de connaissances, créez-en une !", "Folder placeholder": "Ceci est un répertoire", "Go Dataset": "Aller à la base de connaissances", "Intro Placeholder": "Cette base de connaissances n'a pas encore de description~", "Manual collection": "Collection manuelle", "My Dataset": "Mes bases de connaissances", "Name": "Nom de la base", "Query extension intro": "L'activation de la fonction d'optimisation des questions peut améliorer la précision de recherche dans la base de connaissances lors de conversations continues. Lorsque cette fonction est activée, l'IA complétera les informations manquantes dans la question en se basant sur l'historique des conversations.", "Quote Length": "Longueur du contenu cité", "Read Dataset": "Voir les détails de la base", "Set Website Config": "Configurer les informations du site web", "Start export": "Exportation commencée", "Table collection": "Collection de tableaux", "Text collection": "Collection de textes", "collection": {"Click top config website": "Cliquer pour configurer le site", "Collection name": "Nom de la collection", "Collection raw text": "Contenu de la collection", "Empty Tip": "Collection vide", "QA Prompt": "Instruction de découpage QA", "Start Sync Tip": "Confirmer le début de la synchronisation ? Les anciennes données seront supprimées avant la récupération des nouvelles, veuil<PERSON><PERSON> confirmer !", "Sync": "Synchroniser les données", "Sync Collection": "Synchronisation des données", "Website Empty Tip": "Aucun site web associé", "Website Link": "Adresse du site web", "id": "ID de collection", "metadata": {"Chunk Size": "<PERSON><PERSON>", "Createtime": "Date de création", "Raw text length": "Longueur du texte original", "Read Metadata": "Voir les métadonnées", "Training Type": "Mode d'entraînement", "Updatetime": "Date de mise à jour", "Web page selector": "Sélecteur de page web", "metadata": "Métadonnées", "read source": "Voir le contenu original", "source": "Source des données", "source name": "Nom de la source", "source size": "<PERSON><PERSON>"}, "status": {"active": "<PERSON><PERSON><PERSON><PERSON>"}, "sync": {"result": {"sameRaw": "Contenu inchangé, pas de mise à jour nécessaire", "success": "Synchronisation commencée"}}, "training": {}}, "data": {"Auxiliary Data": "Données auxiliaires", "Auxiliary Data Placeholder": "Cette section est facultative, généralement utilisée en complément du \"Contenu des données\" pour construire des instructions structurées dans des scénarios spéciaux, maximum {{maxToken}} caractères.", "Auxiliary Data Tip": "Cette section est facultative\nCe contenu est généralement utilisé en complément du contenu des données pour construire des instructions structurées dans des scénarios spéciaux", "Data Content": "Contenu des données", "Data Content Placeholder": "Ce champ est obligatoire, il s'agit généralement d'une description d'un point de connaissance ou d'une question d'utilisateur, maximum {{maxToken}} caractères.", "Data Content Tip": "Ce champ est obligatoire\nCe contenu est généralement une description d'un point de connaissance ou une question d'utilisateur.", "Default Index Tip": "Non modifiable, l'index par défaut utilisera directement le texte du \"Contenu des données\" et des \"Données auxiliaires\" pour générer l'index.", "Edit": "Modifier les données", "Empty Tip": "Cette collection n'a pas encore de données~", "Main Content": "Contenu principal", "Search data placeholder": "Rechercher des données", "Too Long": "Longueur totale dépassée", "Total Amount": "{{total}} groupes", "group": "groupe", "unit": "élément"}, "embedding model tip": "Le modèle d'indexation peut convertir le langage naturel en vecteurs pour la recherche sémantique.\nAttention, différents modèles d'indexation ne peuvent pas être utilisés ensemble. Une fois le modèle d'indexation choisi, il ne pourra plus être modifié.", "error": {"Data not found": "Les données n'existent pas ou ont été supprimées", "Start Sync Failed": "Échec du démarrage de la synchronisation", "Template does not exist": "Le modèle n'existe pas", "invalidVectorModelOrQAModel": "<PERSON><PERSON><PERSON> de VectorModel ou de modèle QA", "unAuthDataset": "Pas d'autorisation pour cette base de connaissances", "unAuthDatasetCollection": "Pas d'autorisation pour cette collection", "unAuthDatasetData": "Pas d'autorisation pour ces données", "unAuthDatasetFile": "Pas d'autorisation pour ce fichier", "unCreateCollection": "Pas d'autorisation pour ces données", "unLinkCollection": "Ce n'est pas une collection de liens réseau"}, "externalFile": "Bibliothèque de fichiers externes", "file": "<PERSON><PERSON><PERSON>", "folder": "Dossier", "import": {"Auto mode Estimated Price Tips": "Nécessite l'appel au modèle de traitement de fichiers, consommation importante de tokens : {{price}} points/1K tokens", "Auto process": "Automatique", "Auto process desc": "Configuration automatique des règles de découpage et de prétraitement", "Chunk Range": "Plage : {{min}}~{{max}}", "Chunk Split": "Découpage direct", "Chunk Split Tip": "Divise le texte selon certaines règles puis le convertit en format adapté à la recherche sémantique, convient à la plupart des scénarios. Ne nécessite pas d'appel supplémentaire au modèle, coût faible.", "Custom process": "<PERSON><PERSON><PERSON>", "Custom process desc": "Personnaliser les règles de découpage et de prétraitement", "Custom prompt": "Instruction personnalisée", "Custom split char": "Séparateur <PERSON>", "Custom split char Tips": "Permet de découper selon un séparateur personnalisé. Généralement utilisé pour des données déjà traitées, avec un séparateur spécifique pour un découpage précis.", "Custom text": "Texte personnalis<PERSON>", "Custom text desc": "Saisir manuellement un texte comme ensemble de données", "Data Preprocessing": "Traitement des données", "Data process params": "Paramètres de traitement", "Down load csv template": "Cliquer pour télécharger le modèle CSV", "Embedding Estimated Price Tips": "Utilise uniquement le modèle d'indexation, faible consommation de points IA : {{price}} points/1K tokens", "Ideal chunk length": "Longueur idéale de bloc", "Ideal chunk length Tips": "Divise selon les marqueurs de fin et combine plusieurs segments en un bloc. Cette valeur détermine la taille estimée du bloc.", "Import success": "Importation r<PERSON><PERSON><PERSON>, veuillez attendre l'entraînement", "Link name": "Lien web", "Link name placeholder": "Supporte uniquement les liens statiques. Si les données sont vides après téléchargement, le lien pourrait être illisible\nUn lien par ligne, maximum 10 liens par fois", "Local file": "Fichier local", "Local file desc": "Télécharger des fichiers PDF, TXT, DOCX, etc.", "Preview chunks": "Aperçu des segments (max 5)", "Preview raw text": "Aperçu du texte source (max 3000 caractères)", "Process way": "Méthode de traitement", "QA Estimated Price Tips": "Nécessite l'appel au modèle de traitement de fichiers, consommation importante de points IA : {{price}} points/1K tokens", "QA Import": "Découpage QA", "QA Import Tip": "Divise le texte en paragraphes relativement grands selon certaines règles, puis appelle l'IA pour générer des paires question-réponse. Précision de recherche très élevée, mais perte de nombreux détails.", "Select file": "Sélectionner un fichier", "Select source": "Sélectionner la source", "Source name": "Nom de la source", "Sources list": "Liste des sources", "Start upload": "Commencer le téléchargement", "Total files": "{{total}} fichiers au total", "Training mode": "Mode d'entraînement", "Upload data": "Télécharger des données", "Upload file progress": "Progression du téléchargement", "Upload status": "Statut", "Web link": "Lien web", "Web link desc": "Lire le contenu d'une page web statique comme ensemble de données"}, "link": "<PERSON><PERSON>", "search": {"Dataset Search Params": "Configuration de recherche", "Empty result response": "Réponse en cas de recherche vide", "Filter": "Filtre de recherche", "Max Tokens": "Limite de citation", "Max Tokens Tips": "Nombre maximum de tokens par recherche. En chinois environ 1 caractère = 1,7 tokens, en anglais environ 1 caractère = 1 token", "Min Similarity": "Similarité minimale", "Min Similarity Tips": "La similarité varie selon les modèles d'indexation. Testez la recherche pour choisir une valeur appropriée. <PERSON><PERSON>, la similarité peut être très basse.", "No support similarity": "Le filtrage par similarité n'est pris en charge qu'avec le reclassement des résultats ou la recherche sémantique", "Nonsupport": "Non pris en charge", "Params Setting": "Paramètres de recherche", "Quote index": "Index de citation", "ReRank": "Reclassement des résultats", "ReRank desc": "Utilise un modèle de reclassement pour effectuer un tri secondaire, améliorant le classement global.", "Source id": "ID de source", "Source name": "Nom de la source citée", "Using query extension": "Utiliser l'optimisation des questions", "mode": {"embedding": "Recherche sémantique", "embedding desc": "Utilise des vecteurs pour rechercher la pertinence textuelle", "fullTextRecall": "Recherche plein texte", "fullTextRecall desc": "Utilise la recherche plein texte traditionnelle, adaptée pour trouver des mots-clés spécifiques et des structures sujet-prédicat particulières", "mixedRecall": "Recherche mixte", "mixedRecall desc": "Combine les résultats de la recherche vectorielle et de la recherche plein texte, utilisant l'algorithme RRF pour le classement."}, "score": {"embedding": "Recherche sémantique", "embedding desc": "Calcule le score en mesurant la distance entre les vecteurs, plage de 0 à 1.", "fullText": "Recherche plein texte", "fullText desc": "Calcule le score basé sur les mots-clés correspondants, plage de 0 à l'infini.", "reRank": "Reclassement", "reRank desc": "Calcule la corrélation entre phrases via le modèle Rerank, plage de 0 à 1.", "rrf": "Classement global", "rrf desc": "Fusionne plusieurs résultats de recherche par calcul de classement inversé."}, "search mode": "Mode de recherche"}, "status": {"active": "<PERSON><PERSON><PERSON><PERSON>", "syncing": "Synchronisation en cours"}, "test": {"Batch test": "Test par lot", "Batch test Placeholder": "Sélectionnez un fichier CSV", "Search Test": "Test de recherche", "Test": "Tester", "Test Result": "Résultat du test", "Test Text": "Test de texte unique", "Test Text Placeholder": "Saisissez le texte à tester", "Test params": "Paramètres de test", "delete test history": "Supprimer ce résultat de test", "test history": "Historique des tests", "test result placeholder": "Les résultats du test s'afficheront ici", "test result tip": "Les résultats sont classés selon la similarité entre le contenu de la base de connaissances et le texte de test. Vous pouvez ajuster les textes en fonction des résultats.\nAttention : les données dans l'historique des tests peuvent avoir été modifiées, cliquer sur une donnée de test affichera les données les plus récentes."}, "training": {"Agent queue": "File d'attente QA", "Auto mode": "Traitement avancé (expérimental)", "Auto mode Tip": "Enrichit les blocs de données en générant des questions connexes et des résumés via des sous-index et des appels au modèle. Améliore la recherche mais consomme plus d'espace de stockage et augmente les appels IA.", "Chunk mode": "Découpage direct", "Full": "Estimation > 5 minutes", "Leisure": "Libre", "QA mode": "Découpage QA", "Vector queue": "File d'attente d'indexation", "Waiting": "Estimation 5 minutes", "Website Sync": "Synchronisation de site web"}, "website": {"Base Url": "URL de base", "Config": "Configuration du site web", "Config Description": "La fonction de synchronisation de site web vous permet d'entrer l'URL de base d'un site, et le système explorera automatiquement les pages connexes pour l'entraînement de la base de connaissances. Fonctionne uniquement avec les sites statiques, principalement pour la documentation de projets et les blogs.", "Confirm Create Tips": "Confirmer la synchronisation de ce site ? La tâche de synchronisation démarrera ensuite, veuil<PERSON><PERSON> confirmer !", "Confirm Update Tips": "Confirmer la mise à jour de la configuration ? La synchronisation commencera immédiatement avec la nouvelle configuration, veuillez confirmer !", "Selector": "<PERSON><PERSON><PERSON><PERSON>", "Selector Course": "Tutoriel d'utilisation", "Start Sync": "Commencer la synchronisation", "UnValid Website Tip": "Votre site n'est probablement pas un site statique, impossible de synchroniser"}}, "module": {"Add question type": "Ajouter un type de question", "Can not connect self": "Impossible de se connecter à soi-même", "Confirm Delete Node": "Confirmer la suppression de ce nœud ?", "Data Type": "Type de données", "Dataset quote": {"label": "Citation de base de connaissances", "select": "<PERSON><PERSON><PERSON><PERSON><PERSON> une citation"}, "Default Value": "Valeur par défaut", "Default value": "Valeur par défaut", "Default value placeholder": "<PERSON> vide, retournera une chaîne vide par défaut", "Edit intro": "Modifier la description", "Field Description": "Description du champ", "Field Name": "Nom du champ", "Http request props": "Paramètres de requête", "Http request settings": "Configuration de requête", "Input Type": "Type d'entrée", "Laf sync params": "Paramètres de synchronisation", "Max Length": "Longueur maximale", "Max Length placeholder": "Longueur maximale du texte d'entrée", "Max Value": "Valeur maximale", "Min Value": "Valeur minimale", "QueryExtension": {"placeholder": "Par exemple :\nQuestions sur l'introduction et l'utilisation de Python.\nLa conversation actuelle concerne le jeu \"GTA5\"."}, "Quote prompt setting": "Configuration de l'instruction de citation", "Select app": "Sélectionner une application", "Setting quote prompt": "Configurer l'instruction de citation", "Variable": "Variable globale", "Variable Setting": "Configuration de variable", "edit": {"Field Name Cannot Be Empty": "Le nom du champ ne peut pas être vide"}, "extract": {"Add field": "Ajouter un champ", "Enum Description": "<PERSON><PERSON><PERSON>rez les valeurs possibles pour ce champ, une par ligne", "Enum Value": "Valeurs énumérées", "Field Description Placeholder": "Nom/âge/requête SQL...", "Field Setting Title": "Configuration des champs d'extraction", "Required": "Retour obligatoire", "Required Description": "<PERSON><PERSON>me si le champ ne peut pas être extrait, une valeur par défaut sera retournée", "Target field": "Champ cible"}, "http": {"Add props": "Ajouter un paramètre", "AppId": "ID d'application", "AppSecret": "AppSecret", "ChatId": "ID de conversation actuelle", "Current time": "Heure actuelle", "Histories": "Historique", "Key already exists": "La clé existe déjà", "Key cannot be empty": "Le nom du paramètre ne peut pas être vide", "Props name": "Nom du paramètre", "Props tip": "Vous pouvez définir les paramètres de requête HTTP\nUtilisez {{key}} pour appeler des variables globales ou des entrées de paramètres externes. Variables actuellement disponibles :\n{{variable}}", "Props value": "Valeur du paramètre", "ResponseChatItemId": "ID de réponse IA", "Url and params have been split": "Les paramètres d'URL ont été automatiquement ajoutés aux Params", "curl import": "Importation cURL", "curl import placeholder": "Veuillez saisir le contenu au format cURL, les informations de la première interface seront extraites."}, "input": {"Add Branch": "Ajouter une branche", "add": "Ajouter une condition", "description": {"Background": "Vous pouvez ajouter des descriptions de contenus spécifiques pour mieux identifier les types de questions des utilisateurs. Ce contenu sert généralement à présenter au modèle quelque chose qu'il ne connaît pas.", "HTTP Dynamic Input": "Reçoit les valeurs de sortie des nœuds précédents comme variables, qui peuvent être utilisées par les paramètres de requête HTTP.", "Http Request Header": "En-têtes de requête personnalis<PERSON>, veuil<PERSON><PERSON> saisir une chaîne JSON stricte.\n1. Assurez-vous que la dernière propriété n'a pas de virgule\n2. Assurez-vous que les clés sont entre guillemets doubles\nExemple : {\"Authorization\":\"Bearer xxx\"}", "Http Request Url": "Nouvelle adresse de requête HTTP. Si deux \"adresses de requête\" apparaissent, supprimez ce module et ajoutez-le à nouveau pour obtenir la configuration la plus récente.", "Response content": "Vous pouvez utiliser \\n pour créer des sauts de ligne consécutifs.\nVous pouvez implémenter des réponses via des entrées de modules externes, qui remplaceront le contenu saisi ici.\nLes données non textuelles seront automatiquement converties en chaînes"}, "label": {"Background": "Connaissances de fond", "Http Request Url": "<PERSON><PERSON><PERSON> <PERSON>", "Response content": "Contenu de réponse", "Select dataset": "Sélectionner une base de connaissances", "aiModel": "Modèle IA", "chat history": "Historique de conversation", "user question": "Question de l'utilisateur"}, "placeholder": {"Classify background": "Par exemple :\n1. AIGC (Contenu Généré par l'Intelligence Artificielle) désigne l'utilisation de technologies d'IA pour générer automatiquement ou semi-automatiquement du contenu numérique comme du texte, des images, de la musique, des vidéos, etc.\n2. Les technologies AIGC comprennent, sans s'y limiter, le traitement du langage naturel, la vision par ordinateur, l'apprentissage automatique et l'apprentissage profond. Ces technologies peuvent créer de nouveaux contenus ou modifier des contenus existants pour répondre à des besoins créatifs, éducatifs, de divertissement ou d'information spécifiques."}}, "laf": {"Select laf function": "Sélectionner une fonction laf"}, "output": {"description": {"Ai response content": "Sera déclenché une fois la réponse stream terminée", "New context": "Concatène le contenu de cette réponse avec l'historique pour créer un nouveau contexte", "query extension result": "Sortie sous forme de tableau de chaînes, peut être directement connectée à \"Question utilisateur\" de \"Recherche dans la base de connaissances\", déconseillé de connecter à \"Question utilisateur\" de \"Conversation IA\""}, "label": {"Ai response content": "Contenu de réponse IA", "New context": "Nouveau contexte", "query extension result": "Résultat d'optimisation"}}, "template": {"AI function": "Capacités IA", "AI response switch tip": "Si vous ne souhaitez pas que ce nœud produise de contenu, désactivez ce commutateur. Le contenu généré par l'IA ne sera pas montré à l'utilisateur, vous pouvez traiter manuellement le \"Contenu de réponse IA\" pour des cas spéciaux.", "AI support tool tip": "Les modèles prenant en charge les appels de fonction peuvent mieux utiliser les appels d'outils.", "Basic Node": "Fonctions de base", "Query extension": "Optimisation de question", "System Plugin": "Plugin système", "Public Plugin": "Plugin public", "System input module": "Entrée système", "TFSwitch": "Discriminateur", "TFSwitch intro": "Produit une sortie True ou False selon le contenu d'entrée. <PERSON><PERSON> <PERSON><PERSON><PERSON>, lorsque l'entrée est false, undefined, null, 0, none, la sortie sera false. Vous pouvez ajouter des chaînes personnalisées pour compléter les contenus produisant false. Les types non-chaîne, non-nombre, non-booléen produisent directement true.", "Team Plugin": "Mes plugins", "Tool module": "Outil", "UnKnow Module": "<PERSON><PERSON><PERSON> inconnu", "http body placeholder": "Même syntaxe qu'Apifox"}, "templates": {"Load plugin error": "Échec de chargement du plugin"}, "variable": {"Custom type": "Variable personnalisée", "Custom type desc": "Permet de définir une variable globale qui n'a pas besoin d'être remplie par l'utilisateur.\nLa valeur de cette variable peut provenir d'une interface API, d'une requête de lien partagé ou être assignée via le module [Mise à jour de variable].", "add option": "Ajouter une option", "input type": "Texte", "key": "Clé de variable", "key is required": "La clé de variable est obligatoire", "select type": "Sélection unique", "text max length": "Longueur maximale", "textarea type": "Paragraphe", "textarea type desc": "Permet à l'utilisateur d'entrer jusqu'à 4000 caractères", "variable name": "Nom de variable", "variable name is required": "Le nom de variable ne peut pas être vide", "variable option is required": "Les options ne peuvent pas être toutes vides", "variable option is value is required": "Le contenu de l'option ne peut pas être vide", "variable options": "Options"}, "variable add option": "Ajouter une option", "variable_update": "Mise à jour de variable", "variable_update_info": "P<PERSON>t mettre à jour la valeur de sortie d'un nœud spécifique ou mettre à jour une variable globale"}, "plugin": {"Custom headers": "<PERSON>-t<PERSON><PERSON> person<PERSON>", "Free": "Ce plugin ne nécessite pas de points~", "Get Plugin Module Detail Failed": "Chargement du plugin anormal", "Http plugin intro placeholder": "Uniquement pour affichage, sans effet réel", "cost": "Consommation de points :"}, "view_chat_detail": "Voir les détails de la conversation", "workflow": {"Can not delete node": "Ce nœud ne peut pas être supprimé", "Change input type tip": "La modification du type d'entrée effacera les valeurs déjà saisies, ve<PERSON><PERSON><PERSON> confirmer !", "Check Failed": "Vérification du flux de travail échouée, veuillez vérifier si les nœuds sont correctement remplis et si les connexions sont normales", "Confirm stop debug": "Confirmer l'arrêt du débogage ? Les informations de débogage ne seront pas conservées.", "Copy node": "<PERSON><PERSON><PERSON> copié", "Custom inputs": "Entrées personnalisées", "Custom outputs": "Sorties personnalisées", "Dataset quote": "Citation de base de connaissances", "Debug": "Déboguer", "Debug Node": "Mode Debug", "Failed": "Échec d'exécution", "Not intro": "Ce nœud n'a pas de description~", "Running": "En cours d'exécution", "Skipped": "Exécution ignorée", "Stop debug": "<PERSON><PERSON><PERSON><PERSON> le débogage", "Success": "Exécution réussie", "Value type": "Type de données", "Variable": {"Variable type": "Type de variable"}, "debug": {"Done": "Débogage terminé", "Hide result": "Masquer le résultat", "Not result": "Pas de résultat d'exécution", "Run result": "Résultat d'exécution", "Show result": "Aff<PERSON>r le résultat"}, "inputType": {"JSON Editor": "É<PERSON>eur J<PERSON>", "Manual input": "<PERSON><PERSON>", "Manual select": "Sélection manuelle", "Reference": "Référence de variable", "dynamicTargetInput": "Données externes dynamiques", "input": "Champ de saisie simple", "number input": "Champ numérique", "selectApp": "Sélection d'application", "selectDataset": "Sélection de base de connaissances", "selectLLMModel": "Sélection de modèle de conversation", "switch": "Interrupteur", "textarea": "Champ de saisie multiple"}, "publish": {"OnRevert version": "Cliquer pour revenir à cette version", "OnRevert version confirm": "Confirmer le retour à cette version ? La configuration de la version en cours d'édition sera sauvegardée, et une nouvelle version publiée sera créée pour la version restaurée.", "histories": "Historique des publications"}, "template": {"Multimodal": "Multimodal", "Search": "Recherche"}, "tool": {"Handle": "Connecteur d'outil", "Select Tool": "Sélectionner un outil"}, "value": "<PERSON><PERSON>", "variable": "Variable"}}, "create": "<PERSON><PERSON><PERSON>", "cron_job_run_app": "Tâche planifiée", "dataset": {"Confirm move the folder": "Confirmer le déplacement vers ce répertoire", "Confirm to delete the data": "Confirmer la suppression de ces données ?", "Confirm to delete the file": "Confirmer la suppression de ce fichier et de toutes ses données ?", "Create Folder": "<PERSON><PERSON><PERSON> un dossier", "Create manual collection": "<PERSON><PERSON>er une collection manuelle", "Delete Dataset Error": "Erreur de suppression de la base de connaissances", "Edit Folder": "Modifier le dossier", "Edit Info": "Modifier les informations", "Export": "Exporter", "Export Dataset Limit Error": "Échec d'exportation des données", "Folder Name": "<PERSON><PERSON> le nom du dossier", "Insert Data": "<PERSON><PERSON><PERSON><PERSON>", "Manual collection Tip": "Une collection manuelle permet de créer un conteneur vide pour y ajouter des données", "Move Failed": "Erreur lors du déplacement~", "Select Dataset": "Sélectionner cette base de connaissances", "Select Dataset Tips": "Seules les bases de connaissances utilisant le même modèle d'indexation peuvent être sélectionnées", "Select Folder": "Entrer dans le dossier", "Training Name": "Entraînement des données", "collections": {"Collection Embedding": "{{total}} groupes en indexation", "Confirm to delete the folder": "Confirmer la suppression de ce dossier et de tout son contenu ?", "Create And Import": "Nouveau/Importer", "Data Amount": "Quantité totale de données", "Select Collection": "Sélectionner un fichier", "Select One Collection To Store": "Sélectionner un fichier pour le stockage"}, "data": {"Add Index": "Ajouter un index personnalisé", "Can not edit": "Pas de permission d'édition", "Custom Index Number": "Index personnalisé {{number}}", "Default Index": "Index par défaut", "Delete Tip": "Confirmer la suppression de ces données ?", "Index Edit": "Index de données", "Index Placeholder": "Saisir le contenu de l'index", "Input Data": "Importer de nouvelles données", "Input Success Tip": "Importation de données réussie", "Update Data": "Mettre à jour les données", "Update Success Tip": "Mise à jour des données réussie", "edit": {"Content": "Contenu des données", "Course": "Documentation", "Delete": "Supprimer les données", "Index": "Index de données ({{amount}})"}, "input is empty": "Le contenu des données ne peut pas être vide"}, "deleteFolderTips": "Confirmer la suppression de ce dossier et de toutes les bases de connaissances qu'il contient ? Les données ne pourront pas être récupérées après suppression, veuil<PERSON><PERSON> confirmer !", "test": {"noResult": "Aucun résultat de recherche"}}, "default_reply": "Réponse par défaut", "error": {"Create failed": "Création échouée", "fileNotFound": "Fichier introuvable~", "inheritPermissionError": "<PERSON><PERSON><PERSON> de permission héritée", "missingParams": "Paramètres manquants", "team": {"overSize": "Nombre maximum de membres d'équipe dépassé"}, "upload_file_error_filename": "{{name}} échec du téléchargement"}, "extraction_results": "Résultats d'extraction", "field_name": "Nom du champ", "get_QR_failed": "Échec d'obtention du QR code", "get_app_failed": "Échec d'obtention de l'application", "get_laf_failed": "Échec d'obtention de la liste des fonctions Laf", "has_verification": "<PERSON><PERSON><PERSON><PERSON><PERSON>, cliquer pour annuler la liaison", "info": {"buy_extra": "Acheter un forfait supplémentaire", "csv_download": "Cliquer pour télécharger le modèle de test par lot", "csv_message": "Lit la première colonne du fichier CSV pour effectuer des tests par lot, maximum 100 groupes de données par opération.", "felid_message": "La clé du champ doit être composée uniquement de lettres ou de chiffres, et ne peut pas commencer par un chiffre.", "free_plan": "Pour les équipes en version gratuite, si aucune connexion n'est effectuée pendant 30 jours consécutifs, le système supprimera automatiquement les bases de connaissances du compte.", "include": "Inclut le forfait standard et les packs de ressources supplémentaires", "node_info": "L'ajustement de ce module affectera le moment des appels d'outils.\nVous pouvez guider le modèle pour les appels d'outils en décrivant précisément la fonction de ce module.", "old_version_attention": "Votre orchestration avancée est détectée comme étant de l'ancienne version, le système la formatera automatiquement en nouvelle version de flux de travail.\n\nEn raison des différences importantes entre les versions, certains flux de travail pourraient ne pas s'organiser correctement. Veuillez reconnecter manuellement les flux de travail. En cas d'anomalie persistante, essayez de supprimer les nœuds concernés puis de les rajouter.\n\nVous pouvez cliquer directement sur Déboguer pour tester le flux de travail, puis sur Publier une fois le débogage terminé. Le nouveau flux de travail ne sera réellement enregistré et appliqué qu'après avoir cliqué sur Publier.\n\nLa sauvegarde automatique ne fonctionnera pas tant que vous n'aurez pas publié le nouveau flux de travail.", "open_api_notice": "Vous pouvez saisir les clés d'API OpenAI/OneAPI. Si vous remplissez ces informations, l'utilisation de \"Conversation IA\", \"Classification de questions\" et \"Extraction de contenu\" sur la plateforme en ligne utilisera votre clé sans facturation. Assurez-vous que votre clé a les permissions d'accès aux modèles correspondants. Pour les modèles GPT, vous pouvez choisir FastAI.", "open_api_placeholder": "URL de requête, par défaut celle d'OpenAI officiel. Vous pouvez saisir une adresse de proxy, \"v1\" n'est pas automatiquement complété", "resource": "Utilisation des ressources"}, "invalid_variable": "Variable invalide", "is_open": "Activer", "item_description": "Description du champ", "item_name": "Nom du champ", "key_repetition": "Clé en double", "navbar": {"Account": "<PERSON><PERSON><PERSON>", "Chat": "Dialogue", "Datasets": "Bases", "Studio": "Studio", "Tools": "Outils", "Prompt": "Instructions"}, "new_create": "Nouveau", "no_data": "<PERSON><PERSON><PERSON> don<PERSON>", "no_laf_env": "Environnement Laf non configuré", "not_yet_introduced": "Pas encore d'introduction", "pay": {"amount": "<PERSON><PERSON>", "balance": "Solde du compte", "balance_notice": "Solde du compte insuffisant", "confirm_pay": "Confirmer le paiement", "get_pay_QR": "Obtenir le QR code de recharge", "need_pay": "À payer", "new_package_price": "Prix du nouveau forfait", "notice": "Ne fermez pas cette page", "old_package_price": "Prix de l'ancien forfait", "other": "<PERSON>tre montant, veuillez saisir un nombre entier", "to_recharge": "Solde insuffisant, recharger", "wechat": "Veuillez scanner le code avec WeChat pour payer : {{price}} yuans, ne fermez pas cette page", "yuan": "{{amount}} yuans"}, "permission": {"Collaborator": "Collaborateur", "Default permission": "Permission par défaut", "Manage": "<PERSON><PERSON><PERSON>", "No InheritPermission": "Permissions restreintes, n'hérite plus des permissions du dossier parent,", "Not collaborator": "Pas encore de collaborateur", "Owner": "<PERSON><PERSON><PERSON><PERSON>", "Permission": "Permissions", "Permission config": "Configuration des permissions", "Private": "Priv<PERSON>", "Private Tip": "Accessible uniquement par vous", "Public": "Équipe", "Public Tip": "Accessible par tous les membres de l'équipe", "Remove InheritPermission Confirm": "Cette opération désactivera l'héritage des permissions, continuer ?", "Resume InheritPermission Confirm": "Restaurer l'héritage des permissions du dossier parent ?", "Resume InheritPermission Failed": "Échec de la restauration", "Resume InheritPermission Success": "Restauration ré<PERSON>ie", "change_owner": "Transférer la propriété", "change_owner_failed": "Échec du transfert de propriété", "change_owner_placeholder": "Saisir un nom d'utilisateur pour rechercher un compte", "change_owner_success": "Transfert de propriété réussi", "change_owner_tip": "<PERSON><PERSON>, vous conserverez vos droits d'administrateur", "change_owner_to": "Transférer à"}, "plugin": {"App": "Sélectionner une application", "Currentapp": "Application actuelle", "Description": "Description", "Edit Http Plugin": "Modifier le plugin HTTP", "Enter PAT": "Veuillez saisir le jeton d'accès personnel (PAT)", "Get Plugin Module Detail Failed": "Erreur d'obtention des informations du plugin", "Import Plugin": "Importer un plugin HTTP", "Import from URL": "Importer depuis une URL. https://xxxx", "Intro": "Introduction au plugin", "Invalid Env": "Erreur d'environnement laf", "Invalid Schema": "<PERSON><PERSON><PERSON> invalide", "Invalid URL": "URL invalide", "Method": "Méthode", "Path": "Chemin", "Please bind laf accout first": "Veuillez d'abord lier un compte laf", "Plugin List": "Liste des plugins", "Search plugin": "Rechercher un plugin", "Set Name": "Donner un nom au plugin", "contribute": "Contribuer un plugin", "go to laf": "<PERSON><PERSON>", "path": "Chemin"}, "reply_now": "Répondre maintenant", "required": "Obligatoire", "resume_failed": "Échec de la reprise", "select_reference_variable": "Sélectionner une variable de référence", "share_link": "Lien de partage", "support": {"account": {"Individuation": "Personnalisation"}, "inform": {"Read": "<PERSON>"}, "openapi": {"Api baseurl": "URL de base de l'API", "Api manager": "Gestion des clés API", "Copy success": "URL de l'API copiée", "New api key": "Nouvelle clé API", "New api key tip": "Veuillez conserver votre clé en lieu sûr, elle ne sera plus affichée~"}, "outlink": {"Delete link tip": "Confirmer la suppression de ce lien sans connexion ? Après suppression, ce lien sera immédiatement invalidé, mais les journaux de conversation seront conservés, ve<PERSON><PERSON><PERSON> confirmer !", "Max usage points": "Limite de points", "Max usage points tip": "Nombre maximum de points que ce lien peut utiliser, au-delà duquel il ne pourra plus être utilisé. -1 signifie sans limite.", "Usage points": "Consommation de points", "share": {"Response Quote": "<PERSON><PERSON><PERSON> les citations", "Response Quote tips": "Retourne le contenu cité dans le lien de partage, mais ne permet pas aux utilisateurs de télécharger le document original"}}, "permission": {"Permission": "Permissions"}, "standard": {"AI Bonus Points": "Points IA", "Expired Time": "Date de fin", "Start Time": "Date de début", "storage": "Stockage", "type": "Type"}, "team": {"limit": {"No permission rerank": "Pas d'autorisation pour utiliser le reclassement, veuillez mettre à niveau votre forfait"}}, "user": {"Avatar": "Avatar", "Go laf env": "Cliquer pour accéder à {{env}} et obtenir les informations d'identification PAT.", "Laf account course": "Voir le tutoriel de liaison de compte laf.", "Laf account intro": "Après avoir lié votre compte laf, vous pourrez utiliser le module laf dans les flux de travail pour écrire du code en ligne.", "Need to login": "Veuillez vous connecter d'abord", "Price": "Standards de facturation", "User self info": "Informations personnelles", "auth": {"Sending Code": "Envoi en cours"}, "inform": {"System message": "Message système"}, "login": {"And": "et", "Email": "Email", "Forget Password": "Mot de passe oublié ?", "Github": "Connexion GitHub", "Google": "Connexion Google", "Password": "Mot de passe", "Password login": "Connexion par mot de passe", "Phone": "Connexion par téléphone", "Phone number": "Numéro de téléphone", "Policy tip": "En utilisant ce service, vous acceptez nos", "Privacy": "Politique de confidentialité", "Provider error": "<PERSON><PERSON>ur de connexion, veuil<PERSON>z réessayer", "Register": "<PERSON><PERSON><PERSON> un compte", "Root login": "Se connecter en tant qu'utilisateur root", "Root password placeholder": "Le mot de passe root est celui que vous avez défini dans les variables d'environnement", "Terms": "Conditions d'utilisation", "Username": "Nom d'utilisateur", "Wechat": "Connexion WeChat", "can_not_login": "Impossible de se connecter, cliquer pour contacter", "error": "Erreur de connexion", "security_failed": "Échec de la vérification de sécurité", "wx_qr_login": "Scanner le QR code WeChat pour se connecter"}, "logout": {"confirm": "Confirmer la déconnexion ?"}, "team": {"Dataset usage": "Capacité de la base de connaissances", "Team Tags Async Success": "Synchronisation terminée", "member": "Membre"}}, "wallet": {"Ai point every thousand tokens": "{{points}} points/1K tokens", "Amount": "<PERSON><PERSON>", "Bills": "Factures", "Buy": "<PERSON><PERSON><PERSON>", "Confirm pay": "Confirmation de paiement", "Not sufficient": "Vos points IA sont insuffisants, veuillez d'abord mettre à niveau votre forfait ou acheter des points IA supplémentaires pour continuer.", "Plan expired time": "Date d'expiration du forfait", "Plan reset time": "Date de réinitialisation du forfait", "Standard Plan Detail": "<PERSON>é<PERSON> du forfait", "To read plan": "Voir le forfait", "amount_0": "La quantité d'achat ne peut pas être 0", "bill": {"Number": "<PERSON><PERSON><PERSON><PERSON> de commande", "Status": "Statut", "Type": "Type de commande", "payWay": {"Way": "Mode de paiement", "balance": "Paiement par solde", "wx": "Paiement WeChat"}, "status": {"closed": "Ferm<PERSON>", "notpay": "Non payée", "refund": "Remboursée", "success": "<PERSON><PERSON><PERSON>"}}, "buy_resource": "Acheter un pack de ressources", "moduleName": {"index": "Génération d'index", "qa": "Découpage QA"}, "noBill": "Aucun historique de factures~", "subscription": {"AI points": "Points IA", "AI points click to read tip": "Chaque appel à un modèle IA consomme un certain nombre de points IA (similaires aux tokens). Cliquez pour voir les règles de calcul détaillées.", "AI points usage": "Utilisation des points IA", "AI points usage tip": "Chaque appel à un modèle IA consomme un certain nombre de points IA. Pour les standards de calcul spécifiques, veuil<PERSON><PERSON> consulter les \"Standards de facturation\" ci-dessus", "Ai points": "Standards de calcul des points IA", "Buy now": "Changer de forfait", "Current plan": "Forfait actuel", "Extra ai points": "Points IA supplémentaires", "Extra dataset size": "Capacité de base de connaissances supplémentaire", "Extra plan": "Pack de ressources supplémentaires", "Extra plan tip": "Lorsque le forfait standard ne suffit pas, vous pouvez acheter des packs de ressources supplémentaires pour continuer à utiliser le service", "FAQ": "Questions fréquentes", "Month amount": "Nombre de mois", "Next plan": "Futur forfait", "Nonsupport": "Changement impossible", "Stand plan level": "Forfait d'abonnement", "Standard update fail": "Erreur de modification du forfait d'abonnement", "Standard update success": "Changement de forfait d'abonnement réussi !", "Sub plan": "Forfait d'abonnement", "Sub plan tip": "Utilisez gratuitement {{title}} ou passez à un forfait supérieur", "Team plan and usage": "Forfait et utilisation", "Training weight": "Priorité d'entraînement : {{weight}}", "Update extra ai points": "Points IA supplémentaires", "Update extra dataset size": "Capacité de stockage supplémentaire", "Upgrade plan": "Mettre à niveau le forfait", "ai_model": "Mod<PERSON><PERSON> de langage IA", "function": {"History store": "Conservation des conversations pendant {{amount}} jours", "Max app": "{{amount}} applications et plugins", "Max dataset": "{{amount}} bases de connaissances", "Max dataset size": "{{amount}} groupes d'index de base de connaissances", "Max members": "{{amount}} membres d'équipe", "Points": "{{amount}} points IA"}, "mode": {"Month": "<PERSON><PERSON><PERSON>", "Period": "Période d'abonnement", "Year": "<PERSON><PERSON>", "Year sale": "Deux mois offerts"}, "point": "points", "standardSubLevel": {"enterprise": "Version Entreprise", "experience": "Version Essai", "free": "Version Gratuite", "free desc": "Utilisation gratuite des fonctionnalités de base chaque mois, les bases de connaissances seront automatiquement supprimées après 30 jours sans connexion", "team": "Version Équipe"}, "token_compute": "Cliquer pour voir le calculateur de tokens en ligne", "type": {"balance": "Recharge de solde", "extraDatasetSize": "Extension de base de connaissances", "extraPoints": "Forfait de points IA", "standard": "Abonnement forfaitaire"}}, "usage": {"Ai model": "Modèle IA", "App name": "Nom de l'application", "Audio Speech": "Lecture vocale", "Bill Module": "Module de facturation", "Duration": "<PERSON><PERSON><PERSON> (secondes)", "Extension result": "Résultat d'optimisation de question", "Module name": "Nom du module", "Source": "Source", "Text Length": "Longueur du texte", "Time": "Heure de génération", "Token Length": "Longueur en tokens", "Total": "Montant total", "Total points": "Consommation de points IA", "Usage Detail": "Détails d'utilisation", "Whisper": "Entrée vocale"}}}, "sync_link": "Lien de synchronisation", "system": {"Concat us": "Nous contacter", "Help Document": "Documentation d'aide"}, "tag_list": "Liste des tags", "team_tag": "Tags d'équipe", "template": {"Quote Content Tip": "Vous pouvez personnaliser la structure du contenu cité pour mieux l'adapter à différents scénarios. Vous pouvez utiliser certaines variables pour configurer le modèle :\n{{q}} - Contenu recherché, {{a}} - Contenu attendu, {{source}} - Source, {{sourceId}} - Nom du fichier source, {{index}} - Numéro de citation, tous sont optionnels, voici la valeur par défaut :\n{{default}}", "Quote Prompt Tip": "Vous pouvez utiliser {{quote}} pour insérer le modèle de contenu cité, et {{question}} pour insérer la question. Voici la valeur par défaut :\n{{default}}"}, "textarea_variable_picker_tip": "Tapez / pour sélectionner des variables", "tool_field": "Configuration des paramètres de champ d'outil", "undefined_var": "Référence à une variable non définie, ajouter automatiquement ?", "unit": {"character": "caractères", "minute": "minutes"}, "unusable_variable": "Aucune variable disponible", "upload_file_error": "Échec du téléchargement du fichier", "user": {"Account": "<PERSON><PERSON><PERSON>", "Amount of earnings": "Gains (¥)", "Amount of inviter": "Nombre cumulé d'invitations", "Application Name": "Nom du projet", "Avatar": "Avatar", "Change": "Modifier", "Copy invite url": "Copier le lien d'invitation", "Edit name": "Cliquer pour modifier le pseudo", "Invite Url": "Lien d'invitation", "Invite url tip": "Les amis qui s'inscrivent via ce lien seront liés à vous de façon permanente, et vous recevrez une récompense de solde lorsqu'ils effectueront des recharges.\nDe plus, lorsque vos amis s'inscrivent avec un numéro de téléphone, vous recevrez immédiatement une récompense de 5 yuans.\nLes récompenses seront envoyées à votre équipe par défaut.", "Laf Account Setting": "Configuration du compte laf", "Language": "<PERSON><PERSON>", "Member Name": "<PERSON><PERSON><PERSON>", "Notice": "Notifications", "Notification Receive": "Réception des notifications", "Notification Receive Bind": "Veuillez d'abord lier un moyen de réception des notifications", "Old password is error": "Ancien mot de passe incorrect", "OpenAI Account Setting": "Configuration du compte OpenAI", "Password": "Mot de passe", "Pay": "Recharger", "Personal Information": "Informations personnelles", "Promotion": "Promotion", "Promotion Rate": "<PERSON>x de cashback", "Promotion Record": "Historique de promotion", "Promotion rate tip": "Lorsque vos amis rechargent, vous recevrez un certain pourcentage de récompense de solde", "Replace": "<PERSON><PERSON>lace<PERSON>", "Set OpenAI Account Failed": "Erreur de configuration du compte OpenAI", "Sign Out": "Déconnexion", "Team": "Équipe", "Time": "<PERSON><PERSON>", "Timezone": "<PERSON><PERSON> ho<PERSON>", "Update Password": "Modifier le mot de passe", "Update password failed": "Erreur de modification du mot de passe", "Update password successful": "Modification du mot de passe réussie", "Usage Record": "Historique d'utilisation", "apikey": {"key": "Clé API"}, "confirm_password": "Confirmer le mot de passe", "new_password": "Nouveau mot de passe", "no_invite_records": "Aucun historique d'invitation", "no_notice": "Aucune notification", "no_usage_records": "Aucun historique d'utilisation", "old_password": "Ancien mot de passe", "password_message": "Le mot de passe doit comporter entre 4 et 60 caractères", "team": {"Balance": "Solde de l'équipe", "Check Team": "Changer", "Confirm Invite": "Confirmer l'invitation", "Create Team": "Créer une nouvelle équipe", "Invite Member": "Inviter un membre", "Invite Member Failed Tip": "Erreur lors de l'invitation du membre", "Invite Member Result Tip": "Résultat de l'invitation", "Invite Member Success Tip": "Invitation de membres termin<PERSON> : {{success}} personnes\nNom d'utilisateur invalide : {{inValid}}\nDéjà dans l'équipe : {{inTeam}}", "Invite Member Tips": "L'autre partie pourra consulter ou utiliser d'autres ressources de l'équipe", "Leave Team": "<PERSON><PERSON>ter l'équipe", "Leave Team Failed": "Erreur lors du départ de l'équipe", "Member": "Membre", "Member Name": "Nom du membre", "Over Max Member Tip": "L'équipe est limitée à {{max}} personnes", "Personal Team": "Équipe <PERSON>", "Processing invitations": "Traiter les invitations", "Processing invitations Tips": "Vous avez {{amount}} invitations d'équipe à traiter", "Remove Member Confirm Tip": "Confirmer le retrait de {{username}} de l'équipe ?", "Select Team": "Sélection d'équipe", "Set Name": "Donner un nom à l'équipe", "Switch Team Failed": "Erreur de changement d'équipe", "Tags Async": "Enregistrer", "Team Name": "Nom de l'équipe", "Team Tags Async": "Synchronisation des tags", "Team Tags Async Success": "Lien d'erreur enregistré avec succès, informations de tag mises à jour", "Update Team": "Mettre à jour les informations de l'équipe", "invite": {"Accept Confirm": "Confirmer l'adhésion à cette équipe ?", "Accepted": "A rejoint l'équipe", "Deal Width Footer Tip": "La fenêtre se fermera automatiquement une fois le traitement terminé~", "Reject": "Invitation refusée", "Reject Confirm": "Confirmer le refus de cette invitation ?", "accept": "Accepter", "reject": "Refuser"}, "member": {"Confirm Leave": "Confirmer le départ de cette équipe ?", "active": "A rejoint", "reject": "<PERSON><PERSON><PERSON><PERSON>", "waiting": "En attente d'acceptation"}, "role": {"Admin": "Administrateur", "Owner": "<PERSON><PERSON><PERSON><PERSON>", "Visitor": "Visiteur"}}, "type": "Type"}, "verification": "Vérification", "prompt": {"title": "<PERSON><PERSON><PERSON><PERSON> d'instructions", "search_prompt": "Rechercher un modèle...", "Add Template": "Ajouter un modèle", "No description": "Pas de description", "No matching templates": "<PERSON><PERSON><PERSON> modèle correspondant", "No Templates": "<PERSON><PERSON><PERSON> mod<PERSON>", "Template Title": "<PERSON>itre du modèle", "Enter template title": "<PERSON><PERSON> le titre du modèle", "Template Description": "Description du modèle", "Enter template description": "<PERSON><PERSON> la description du modèle", "Create": "<PERSON><PERSON><PERSON>", "Cancel": "Annuler", "Title is required": "Le titre est obligatoire", "Create Template Success": "Création du modèle réussie", "Create Template Failed": "Échec de création du modèle", "Failed to load templates": "Échec de chargement des modèles", "Add Content": "A<PERSON>ter du contenu", "No Content": "Aucun contenu", "Edit Content": "Modifier le contenu", "Content Title": "Titre du contenu", "Enter content title": "<PERSON><PERSON> le titre du contenu", "Content": "Contenu", "Enter content": "<PERSON><PERSON> le contenu", "Update": "Mettre à jour", "Add": "Ajouter", "Title and content are required": "Le titre et le contenu sont obligatoires", "Add Content Success": "Ajout du contenu réussi", "Add Content Failed": "Échec d'ajout du contenu", "Update Content Success": "Mise à jour du contenu réussie", "Update Content Failed": "Échec de mise à jour du contenu", "Delete Content Confirm": "Confirmer la suppression de ce contenu ?", "Delete Content Success": "Suppression du contenu réussie", "Delete Content Failed": "Échec de suppression du contenu", "Copy Success": "<PERSON><PERSON> r<PERSON>", "Copy Failed": "Échec de copie", "Copy": "<PERSON><PERSON><PERSON>", "Failed to load template": "Échec de chargement du modèle"}}