{"App": "应用", "Export": "导出", "FAQ": {"ai_point_a": "每次调用AI模型时，都会消耗一定的AI积分。具体的计算标准可参考上方的“AI 积分计算标准”。\nToken计算采用GPT3.5相同公式，1Token≈0.7中文字符≈0.9英文单词，连续出现的字符可能被认为是1个Tokens。", "ai_point_expire_a": "会过期。当前套餐过期后，AI积分将会清空，并更新为新套餐的AI积分。年度套餐的AI积分时长为1年，而不是每个月。", "ai_point_expire_q": "AI积分会过期么？", "ai_point_q": "什么是AI积分？", "auto_renew_a": "当前套餐过期后，系统会自动根据“未来套餐”进行续费，系统会尝试从账户余额进行扣费，如果您需要自动续费，请在账户余额中预留额度。", "auto_renew_q": "订阅套餐会自动续费么？", "change_package_a": "当前套餐价格大于新套餐时，无法立即切换，将会在当前套餐过期后以“续费”形式进行切换。\n当前套餐价格小于新套餐时，系统会自动计算当前套餐剩余余额，您可支付差价进行套餐切换。", "change_package_q": "能否切换订阅套餐？", "dataset_compute_a": "1条知识库存储等于1条知识库索引。一条知识库数据可以包含1条或多条知识库索引。增强训练中，1条数据会生成5条索引。", "dataset_compute_q": "知识库存储怎么计算？", "dataset_index_a": "不会。但知识库索引超出时，无法插入和更新知识库内容。", "dataset_index_q": "知识库索引超出会删除么？", "free_user_clean_a": "免费版团队（免费版且未购买额外套餐）连续 30 天未登录系统，系统会自动清除该团队下所有知识库内容。", "free_user_clean_q": "免费版数据会清除么？", "package_overlay_a": "可以的。每次购买的资源包都是独立的，在其有效期内将会叠加使用。AI积分会优先扣除最先过期的资源包。", "package_overlay_q": "额外资源包可以叠加么？"}, "Folder": "文件夹", "Login": "登录", "Move": "移动", "Name": "名称", "Rename": "重命名", "Resume": "恢复", "Running": "运行中", "UnKnow": "未知", "Warning": "提示", "add_new": "新增", "chose_condition": "选择条件", "chosen": "已选", "classification": "分类", "click_to_resume": "点击恢复", "code_editor": "代码编辑", "code_error": {"app_error": {"invalid_app_type": "错误的应用类型", "invalid_owner": "非法的应用所有者", "not_exist": "应用不存在", "un_auth_app": "无权操作该应用"}, "chat_error": {"un_auth": "没有权限操作此对话记录"}, "error_code": {"400": "请求失败", "401": "无访问权限", "403": "紧张访问", "404": "请求不存在", "405": "请求方法错误", "406": "请求格式错误", "410": "资源已删除", "422": "验证错误", "500": "服务器发生错误", "502": "网关错误", "503": "服务器暂时过载或正在维护", "504": "网关超时"}, "error_message": {"403": "凭证错误", "510": "账户余额不足", "511": "没有权限操作此模型", "513": "没有权限读取该文件", "514": "Api Key 不合法"}, "openapi_error": {"api_key_not_exist": "Api Key 不存在", "exceed_limit": "最多 10 组 API 密钥", "un_auth": "无权操作该 Api Key"}, "outlink_error": {"invalid_link": "分享链接无效", "link_not_exist": "分享链接不存在", "un_auth_user": "身份校验失败"}, "plugin_error": {"not_exist": "插件不存在", "un_auth": "无权操作该插件"}, "system_error": {"community_version_num_limit": "超出知识库数量限制"}, "team_error": {"ai_points_not_enough": "", "app_amount_not_enough": "应用数量已达上限~", "dataset_amount_not_enough": "知识库数量已达上限~", "dataset_size_not_enough": "知识库容量不足，请先扩容~", "over_size": "error.team.overSize", "plugin_amount_not_enough": "插件数量已达上限~", "re_rank_not_enough": "无权使用检索重排~", "un_auth": "无权操作该团队", "website_sync_not_enough": "无权使用Web站点同步~"}, "token_error_code": {"403": "登录状态无效，请重新登录"}, "user_error": {"balance_not_enough": "账号余额不足~", "bin_visitor": "您的身份校验未通过", "bin_visitor_guest": "您当前身份为游客，无权操作", "un_auth_user": "找不到该用户"}}, "common": {"Action": "操作", "Add": "添加", "Add New": "新增", "Add Success": "添加成功", "All": "全部", "Cancel": "取消", "Choose": "选择", "Close": "关闭", "Config": "配置", "Confirm": "确认", "Confirm Create": "确认创建", "Confirm Import": "确认导入", "Confirm Move": "移动到这", "Confirm Update": "确认更新", "Confirm to leave the page": "确认离开该页面？", "Copy": "复制", "Copy Successful": "复制成功", "Create Failed": "创建异常", "Create New": "新建", "Create Success": "创建成功", "Create Time": "创建时间", "Creating": "创建中", "Custom Title": "自定义标题", "Delete": "删除", "Delete Failed": "删除失败", "Delete Success": "删除成功", "Delete Warning": "删除警告", "Delete folder": "删除文件夹", "Detail": "详情", "Documents": "文档", "Done": "完成", "Edit": "编辑", "Exit": "退出", "Expired Time": "过期时间", "Field": "字段", "File": "文件", "Finish": "完成", "Import": "导入", "Import failed": "导入失败", "Import success": "导入成功", "Input": "输入", "Input folder description": "文件夹描述", "Input name": "取个名字", "Intro": "介绍", "Last Step": "上一步", "Last use time": "最后使用时间", "Load Failed": "加载失败", "Loading": "加载中...", "More": "更多", "Move": "移动", "MultipleRowSelect": {"No data": "没有可选值"}, "Name": "名称", "Next Step": "下一步", "No more data": "没有更多了~", "Not open": "未开启", "OK": "好的", "Open": "打开", "Operation": "操作", "Other": "其他", "Output": "输出", "Params": "参数", "Password inconsistency": "两次密码不一致", "Permission": "权限", "Please Input Name": "请输入名称", "Read document": "查看文档", "Read intro": "查看说明", "Remove": "移除", "Rename": "重命名", "Request Error": "请求异常", "Require Input": "必填", "Restart": "重新开始", "Role": "权限", "Root folder": "根目录", "Run": "运行", "Save": "保存", "Save Failed": "保存异常", "Save Success": "保存成功", "Search": "搜索", "Select File Failed": "选择文件异常", "Select template": "选择模板", "Set Avatar": "点击设置头像", "Set Name": "取个名字", "Setting": "设置", "Status": "状态", "Submit failed": "提交失败", "Success": "成功", "Sync success": "同步成功", "Team": "团队", "Team Tags Set": "标签", "Un used": "未使用", "UnKnow": "未知", "UnKnow Source": "未知来源", "Unlimited": "无限制", "Update": "更新", "Update Failed": "更新异常", "Update Success": "更新成功", "Update Successful": "更新成功", "Upload File Failed": "上传文件失败", "Username": "用户名", "Waiting": "等待中", "Warning": "警告", "Website": "网站", "all_result": "完整结果", "avatar": {"Select Avatar": "点击选择头像", "Select Failed": "选择头像异常"}, "choosable": "可选", "confirm": {"Common Tip": "操作确认"}, "course": {"Read Course": "查看教程"}, "empty": {"Common Tip": "没有什么数据噢~"}, "error": {"Select avatar failed": "头像选择异常", "too_many_request": "请求太频繁了，请稍后重试。", "unKnow": "出现了点意外~"}, "failed": "失败", "folder": {"Drag Tip": "点我可拖动", "Move Success": "移动成功", "Move to": "移动到", "No Folder": "没有子目录了，就放这里吧", "Open folder": "打开文件夹", "Root Path": "根目录", "empty": "这个目录已经没东西可选了~", "open_dataset": "打开知识库"}, "input": {"Repeat Value": "有重复的值"}, "is_requesting": "请求中……", "jsonEditor": {"Parse error": "JSON 可能有误，请仔细检查"}, "link": {"UnValid": "无效的链接"}, "month": "月", "name_is_empty": "名称不能为空", "no_intro": "暂无介绍", "not_support": "不支持", "page_center": "页面居中", "request_end": "已加载全部", "request_more": "点击加载更多", "speech": {"error tip": "语音转文字失败", "not support": "您的浏览器不支持语音输入"}, "support": "支持", "system": {"Commercial version function": "请升级商业版后使用该功能：https://fastgpt.in", "Help Chatbot": "机器人助手", "Use Helper": "使用帮助"}, "ui": {"textarea": {"Magnifying": "放大"}}}, "confirm_choice": "确认选择", "core": {"Chat": "对话", "Max Token": "单条数据上限", "ai": {"AI settings": "AI 配置", "Ai point price": "AI 积分消耗", "Max context": "最大上下文", "Model": "AI 模型", "Not deploy rerank model": "未部署重排模型", "Prompt": "提示词", "Support tool": "函数调用", "model": {"Dataset Agent Model": "文件处理模型", "Vector Model": "索引模型", "doc_index_and_dialog": "文档索引 & 对话索引"}}, "app": {"Ai response": "返回 AI 内容", "Api request": "API 访问", "Api request desc": "通过 API 接入到已有系统中，或企微、飞书等", "App intro": "应用介绍", "Chat Variable": "对话框变量", "Config schedule plan": "配置定时执行", "Config whisper": "配置语音输入", "Interval timer config": "定时执行配置", "Interval timer run": "定时执行", "Interval timer tip": "可定时执行应用", "Make a brief introduction of your app": "给你的 AI 应用一个介绍", "Max histories": "聊天记录数量", "Max tokens": "回复上限", "Name and avatar": "头像 & 名称", "Onclick to save": "点击保存", "Publish": "发布", "Publish Confirm": "确认发布应用？会立即更新所有发布渠道的应用状态。", "Publish app tip": "发布应用后，所有发布渠道将会立即使用该版本", "Question Guide": "猜你想问", "Question Guide Tip": "对话结束后，会为生成 3 个引导性问题。", "Quote prompt": "引用模板提示词", "Quote templates": "引用内容模板", "Random": "发散", "Saved time": "已保存：{{time}}", "Search team tags": "搜索标签", "Select TTS": "选择语音播放模式", "Select app from template": "从模板中选择", "Select quote template": "选择引用提示模板", "Set a name for your app": "给应用设置一个名称", "Setting ai property": "点击配置 AI 模型相关属性", "Share link": "免登录窗口", "Share link desc": "分享链接给其他用户，无需登录即可直接进行使用", "Share link desc detail": "可以直接分享该模型给其他用户去进行对话，对方无需登录即可直接进行对话。注意，这个功能会消耗你账号的余额，请保管好链接！", "TTS": "语音播放", "TTS Tip": "开启后，每次对话后可使用语音播放功能。使用该功能可能产生额外费用。", "TTS start": "朗读内容", "Team tags": "团队标签", "Temperature": "温度", "Tool call": "工具调用", "ToolCall": {"No plugin": "没有可用的插件", "Parameter setting": "输入参数", "System": "系统", "Team": "我的", "Public": "公共", "This plugin cannot be called as a tool": "该工具无法在简易模式中使用"}, "Welcome Text": "对话开场白", "Whisper": "语音输入", "Whisper config": "语音输入配置", "deterministic": "严谨", "edit": {"Prompt Editor": "提示词编辑", "Query extension background prompt": "对话背景描述", "Query extension background tip": "描述当前对话的范围，便于 AI 为当前问题进行补全和扩展。填写的内容，通常为该助手"}, "edit_content": "应用信息编辑", "error": {"App name can not be empty": "应用名不能为空", "Get app failed": "获取应用异常"}, "feedback": {"Custom feedback": "自定义反馈", "close custom feedback": "关闭反馈"}, "have_publish": "已发布", "loading": "加载中", "logs": {"Source And Time": "来源 & 时间"}, "no_app": "还没有应用，快去创建一个吧！", "not_published": "未发布", "outLink": {"Can Drag": "图标可拖拽", "Default open": "默认打开", "Iframe block title": "复制下面 iframe 加入到你的网站中", "Link block title": "将下面链接复制到浏览器打开", "Script Close Icon": "关闭图标", "Script Open Icon": "打开图标", "Script block title": "将下面代码加入到你的网站中", "Select Mode": "开始使用", "Select Using Way": "选择使用方式", "Show History": "展示历史对话"}, "publish": {"Fei Shu Bot Desc": "接入到飞书机器人中", "Fei shu bot": "飞书", "Fei shu bot publish": "发布到飞书机器人", "FOFPro Desc": "发布到 FOFPro", "FOFPro": "FOFPro", "FOFPro publish": "发布到 FOFPro，通过 FOFPro 网站和小程序访问应用", "BenZui publish": "发布到笨嘴神器，通过笨嘴神器小程序访问应用", "TOWA Store Desc": "发布到 TOWA Store", "Edu Store Desc": "发布到思维炼 Store", "BenZui Store Desc": "发布到笨嘴神器", "TOWA Store": "TOWA Store", "Edu Store": "思维炼 Store", "BenZui Store": "笨嘴神器", "TOWA Store publish": "如果你希望把应用发布到 TOWA Store，请扫描二维码联系小助手", "Publish": "发布", "Cancel Publish": "取消发布"}, "schedule": {"Default prompt": "默认问题", "Default prompt placeholder": "执行应用时的默认问题", "Every day": "每天 {{hour}}:00", "Every month": "每月 {{day}} 号 {{hour}}:00", "Every week": "每周{{day}} {{hour}}:00", "Interval": "每 {{interval}} 小时", "Open schedule": "定时执行"}, "setting": "应用信息设置", "share": {"Amount limit tip": "最多创建 10 组", "Create link": "创建新链接", "Create link tip": "创建成功。已复制分享地址，可直接分享使用", "Ip limit title": "IP 限流（人/分钟）", "Is response quote": "返回引用", "Not share link": "没有创建分享链接", "Role check": "身份校验"}, "tip": {"Add a intro to app": "快来给应用一个介绍~", "chatNodeSystemPromptTip": "模型固定的引导词，通过调整该内容，可以引导模型聊天方向。该内容会被固定在上下文的开头。可通过输入 / 插入选择变量\n如果关联了知识库，你还可以通过适当的描述，来引导模型何时去调用知识库搜索。例如：\n你是电影《星际穿越》的助手，当用户询问与《星际穿越》相关的内容时，请搜索知识库并结合搜索结果进行回答。", "variableTip": "可以在对话开始前，要求用户填写一些内容作为本轮对话的特定变量。该模块位于开场引导之后。\n变量可以通过 {{变量key}} 的形式注入到其他模块 string 类型的输入中，例如：提示词、限定词等", "welcomeTextTip": "每次对话开始前，发送一个初始内容。支持标准 Markdown 语法，可使用的额外标记：\n[快捷按键]：用户点击后可以直接发送该问题"}, "tool_label": {"doc": "使用文档", "github": "GitHub地址", "price": "计费说明"}, "tts": {"Close": "不使用", "Speech model": "语音模型", "Speech speed": "语速", "Test Listen": "试听", "Test Listen Text": "你好，这是语音测试，如果你能听到这句话，说明语音播放功能正常", "Web": "浏览器自带(免费)"}, "whisper": {"Auto send": "自动发送", "Auto send tip": "语音输入完毕后直接发送，不需要再手动点击发送按键", "Auto tts response": "自动语音回复", "Auto tts response tip": "通过语音输入发送的问题，会直接以语音的形式响应，请确保打开了语音播报功能。", "Close": "关闭", "Not tts tip": "你没有开启语音播放，该功能无法使用", "Open": "开启", "Switch": "开启语音输入"}}, "chat": {"Admin Mark Content": "纠正后的回复", "Audio Not Support": "设备不支持语音播放", "Audio Speech Error": "语音播报异常", "Cancel Speak": "取消语音输入", "Chat API is error or undefined": "对话接口报错或返回为空", "Confirm to clear history": "确认清空该应用的在线聊天记录？分享和 API 调用的记录不会被清空。", "Confirm to clear share chat history": "确认删除所有聊天记录？", "Converting to text": "正在转换为文本...", "Custom History Title": "自定义历史记录标题", "Custom History Title Description": "如果设置为空，会自动跟随聊天记录。", "Debug test": "调试预览", "Exit Chat": "退出聊天", "Failed to initialize chat": "初始化聊天失败", "Feedback Failed": "提交反馈异常", "Feedback Modal": "结果反馈", "Feedback Modal Tip": "输入你觉得回答不满意的地方", "Feedback Submit": "提交反馈", "Feedback Success": "反馈成功！", "Finish Speak": "语音输入完成", "History": "记录", "History Amount": "{{amount}} 条记录", "Mark": "标注预期回答", "Mark Description": "当前标注功能为测试版。\n\n点击添加标注后，需要选择一个知识库，以便存储标注数据。你可以通过该功能快速的标注问题和预期回答，以便引导模型下次的回答。\n\n目前，标注功能同知识库其他数据一样，受模型的影响，不代表标注后 100% 符合预期。\n\n标注数据仅单向与知识库同步，如果知识库修改了该标注数据，日志展示的标注数据无法同步。", "Mark Description Title": "标注功能介绍", "New Chat": "新对话", "Pin": "置顶", "Question Guide": "猜你想问", "Quote": "引用", "Quote Amount": "知识库引用（{{amount}} 条）", "Read Mark Description": "查看标注功能介绍", "Recent use": "最近使用", "Record": "语音输入", "Restart": "重开对话", "Select Image": "选择图片", "Select dataset": "选择知识库", "Select dataset Desc": "选择一个知识库存储预期答案", "Send Message": "发送", "Speaking": "我在听，请说...", "Start Chat": "开始对话", "Type a message": "输入问题，发送 [Enter]/换行 [Ctrl(Alt/Shift) + Enter]", "Unpin": "取消置顶", "You need to a chat app": "你没有可用的应用", "error": {"Chat error": "对话出现异常", "Messages empty": "接口内容为空，可能文本超长了~", "Select dataset empty": "你没有选择知识库", "User input empty": "传入的用户问题为空", "data_error": "获取数据异常"}, "feedback": {"Close User Like": "用户表示赞同\n点击关闭该标记", "Feedback Close": "关闭反馈", "No Content": "用户没有填写具体反馈内容", "Read User dislike": "用户表示反对\n点击查看内容"}, "logs": {"api": "API 调用", "online": "在线使用", "share": "外部链接调用", "test": "测试", "published": "已发布的应用"}, "markdown": {"Edit Question": "编辑问题", "Quick Question": "点我立即提问", "Send Question": "发送问题"}, "quote": {"Quote Tip": "此处仅显示实际引用内容，若数据有更新，此处不会实时更新", "Read Quote": "查看引用"}, "response": {"Complete Response": "完整响应", "Extension model": "问题优化模型", "Plugin response detail": "插件详情", "Read complete response": "查看详情", "Read complete response tips": "点击查看详细流程", "Tool call response detail": "工具运行详情", "Tool call tokens": "工具调用 tokens 消耗", "context total length": "上下文总长度", "module cq": "问题分类列表", "module cq result": "分类结果", "module extract description": "提取背景描述", "module extract result": "提取结果", "module historyPreview": "记录预览(仅展示部分内容)", "module http result": "响应体", "module if else Result": "判断器结果", "module limit": "单次搜索上限", "module maxToken": "最大响应 tokens", "module model": "模型", "module name": "模型名", "module query": "问题/检索词", "module quoteList": "引用内容", "module similarity": "相似度", "module temperature": "温度", "module time": "运行时长", "module tokens": "总 tokens", "plugin output": "插件输出值", "search using reRank": "结果重排", "text output": "文本输出"}, "retry": "重新生成", "tts": {"Stop Speech": "停止"}}, "common": {"tip": {"leave page": "内容已修改，确认离开页面吗？"}}, "dataset": {"All Dataset": "全部知识库", "Avatar": "知识库头像", "Choose Dataset": "关联知识库", "Collection": "数据集", "Create dataset": "创建一个知识库", "Dataset": "知识库", "Dataset ID": "知识库 ID", "Dataset Type": "知识库类型", "Delete Confirm": "确认删除该知识库？删除后数据无法恢复，请确认！", "Empty Dataset": "空数据集", "Empty Dataset Tips": "还没有知识库，快去创建一个吧！", "Folder placeholder": "这是一个目录", "Go Dataset": "前往知识库", "Intro Placeholder": "这个知识库还没有介绍~", "Manual collection": "手动数据集", "My Dataset": "我的知识库", "Name": "知识库名称", "Query extension intro": "开启问题优化功能，可以提高提高连续对话时，知识库搜索的精度。开启该功能后，在进行知识库搜索时，会根据对话记录，利用 AI 补全问题缺失的信息。", "Quote Length": "引用内容长度", "Read Dataset": "查看知识库详情", "Set Website Config": "开始配置网站信息", "Start export": "已开始导出", "Table collection": "表格数据集", "Text collection": "文本数据集", "collection": {"Click top config website": "点击配置网站", "Collection name": "数据集名称", "Collection raw text": "数据集内容", "Empty Tip": "数据集空空如也", "QA Prompt": "QA 拆分引导词", "Start Sync Tip": "确认开始同步数据？将会删除旧数据后重新获取，请确认！", "Sync": "同步数据", "Sync Collection": "数据同步", "Website Empty Tip": "还没有关联网站", "Website Link": "Web 站点地址", "id": "集合 ID", "metadata": {"Chunk Size": "分割大小", "Createtime": "创建时间", "Raw text length": "原文长度", "Read Metadata": "查看元数据", "Training Type": "训练模式", "Updatetime": "更新时间", "Web page selector": "网站选择器", "metadata": "元数据", "read source": "查看原始内容", "source": "数据来源", "source name": "来源名", "source size": "来源大小"}, "status": {"active": "已就绪"}, "sync": {"result": {"sameRaw": "内容未变动，无需更新", "success": "开始同步"}}, "training": {}}, "data": {"Auxiliary Data": "辅助数据", "Auxiliary Data Placeholder": "该部分为可选填项，通常是为了与前面的【数据内容】配合，构建结构化提示词，用于特殊场景，最多 {{maxToken}} 字。", "Auxiliary Data Tip": "该部分为可选填项\n该内容通常是为了与前面的数据内容配合，构建结构化提示词，用于特殊场景", "Data Content": "相关数据内容", "Data Content Placeholder": "该输入框是必填项，该内容通常是对于知识点的描述，也可以是用户的问题，最多 {{maxToken}} 字。", "Data Content Tip": "该输入框是必填项\n该内容通常是对于知识点的描述，也可以是用户的问题。", "Default Index Tip": "无法编辑，默认索引会使用【相关数据内容】与【辅助数据】的文本直接生成索引。", "Edit": "编辑数据", "Empty Tip": "这个集合还没有数据~", "Main Content": "主要内容", "Search data placeholder": "搜索相关数据", "Too Long": "总长度超长了", "Total Amount": "{{total}} 组", "group": "组", "unit": "条"}, "embedding model tip": "索引模型可以将自然语言转成向量，用于进行语义检索。\n注意，不同索引模型无法一起使用，选择完索引模型后将无法修改。", "error": {"Data not found": "数据不存在或已被删除", "Start Sync Failed": "开始同步失败", "Template does not exist": "模板不存在", "invalidVectorModelOrQAModel": "VectorModel 或 QA 模型错误", "unAuthDataset": "无权操作该知识库", "unAuthDatasetCollection": "无权操作该数据集", "unAuthDatasetData": "无权操作该数据", "unAuthDatasetFile": "无权操作该文件", "unCreateCollection": "无权操作该数据", "unLinkCollection": "不是网络链接集合"}, "externalFile": "外部文件库", "file": "文件", "folder": "目录", "import": {"Auto mode Estimated Price Tips": "需调用文件处理模型，需要消耗较多 tokens：{{price}} 积分/1K tokens", "Auto process": "自动", "Auto process desc": "自动设置分割和预处理规则", "Chunk Range": "范围：{{min}}~{{max}}", "Chunk Split": "直接分段", "Chunk Split Tip": "将文本按一定的规则进行分段处理后，转成可进行语义搜索的格式，适合绝大多数场景。不需要调用模型额外处理，成本低。", "Custom process": "自定义规则", "Custom process desc": "自定义设置分制和预处理规则", "Custom prompt": "自定义提示词", "Custom split char": "自定义分隔符", "Custom split char Tips": "允许你根据自定义的分隔符进行分块。通常用于已处理好的数据，使用特定的分隔符来精确分块。", "Custom text": "自定义文本", "Custom text desc": "手动输入一段文本作为数据集", "Data Preprocessing": "数据处理", "Data process params": "数据处理参数", "Down load csv template": "点击下载 CSV 模板", "Embedding Estimated Price Tips": "仅使用索引模型，消耗少量 AI 积分：{{price}} 积分/1K tokens", "Ideal chunk length": "理想分块长度", "Ideal chunk length Tips": "按结束符号进行分段，并将多个分段组成一个分块，该值决定了分块的预估大小。", "Import success": "导入成功，请等待训练", "Link name": "网络链接", "Link name placeholder": "仅支持静态链接，如果上传后数据为空，可能该链接无法被读取\n每行一个，每次最多 10 个链接", "Local file": "本地文件", "Local file desc": "上传 PDF、TXT、DOCX 等格式的文件", "Preview chunks": "预览分段（最多 5 段）", "Preview raw text": "预览源文本（最多 3000 字）", "Process way": "处理方式", "QA Estimated Price Tips": "需调用文件处理模型，需要消耗较多 AI 积分：{{price}} 积分/1K tokens", "QA Import": "QA 拆分", "QA Import Tip": "根据一定规则，将文本拆成一段较大的段落，调用 AI 为该段落生成问答对。有非常高的检索精度，但是会丢失很多内容细节。", "Select file": "选择文件", "Select source": "选择来源", "Source name": "来源名", "Sources list": "来源列表", "Start upload": "开始上传", "Total files": "共 {{total}} 个文件", "Training mode": "训练模式", "Upload data": "上传数据", "Upload file progress": "文件上传进度", "Upload status": "状态", "Web link": "网页链接", "Web link desc": "读取静态网页内容作为数据集"}, "link": "链接", "search": {"Dataset Search Params": "知识库搜索配置", "Empty result response": "空搜索回复", "Filter": "搜索过滤", "Max Tokens": "引用上限", "Max Tokens Tips": "单次搜索最大的 token 数量，中文约 1 字=1.7 tokens，英文约 1 字=1 token", "Min Similarity": "最低相关度", "Min Similarity Tips": "不同索引模型的相关度有区别，请通过搜索测试来选择合适的数值，使用 Rerank 时，相关度可能会很低。", "No support similarity": "仅使用结果重排或语义检索时，支持相关度过滤", "Nonsupport": "不支持", "Params Setting": "搜索参数设置", "Quote index": "第几个引用", "ReRank": "结果重排", "ReRank desc": "使用重排模型来进行二次排序，可增强综合排名。", "Source id": "来源 ID", "Source name": "引用来源名", "Using query extension": "使用问题优化", "mode": {"embedding": "语义检索", "embedding desc": "使用向量进行文本相关性查询", "fullTextRecall": "全文检索", "fullTextRecall desc": "使用传统的全文检索，适合查找一些关键词和主谓语特殊的数据", "mixedRecall": "混合检索", "mixedRecall desc": "使用向量检索与全文检索的综合结果返回，使用 RRF 算法进行排序。"}, "score": {"embedding": "语义检索", "embedding desc": "通过计算向量之间的距离获取得分，范围为 0~1。", "fullText": "全文检索", "fullText desc": "计算相同关键词的得分，范围为 0~无穷。", "reRank": "结果重排", "reRank desc": "通过 Rerank 模型计算句子之间的关联度，范围为 0~1。", "rrf": "综合排名", "rrf desc": "通过倒排计算的方式，合并多个检索结果。"}, "search mode": "搜索模式"}, "status": {"active": "已就绪", "syncing": "同步中"}, "test": {"Batch test": "批量测试", "Batch test Placeholder": "选择一个 CSV 文件", "Search Test": "搜索测试", "Test": "测试", "Test Result": "测试结果", "Test Text": "单个文本测试", "Test Text Placeholder": "输入需要测试的文本", "Test params": "测试参数", "delete test history": "删除该测试结果", "test history": "测试历史", "test result placeholder": "测试结果将在这里展示", "test result tip": "根据知识库内容与测试文本的相似度进行排序，你可以根据测试结果调整对应的文本。\n注意：测试记录中的数据可能已经被修改过，点击某条测试数据后将展示最新的数据。"}, "training": {"Agent queue": "QA 训练排队", "Auto mode": "增强处理（实验）", "Auto mode Tip": "通过子索引以及调用模型生成相关问题与摘要，来增加数据块的语义丰富度，更利于检索。需要消耗更多的存储空间和增加 AI 调用次数。", "Chunk mode": "直接分段", "Full": "预计 5 分钟以上", "Leisure": "空闲", "QA mode": "问答拆分", "Vector queue": "索引排队", "Waiting": "预计 5 分钟", "Website Sync": "Web 站点同步"}, "website": {"Base Url": "根地址", "Config": "Web 站点配置", "Config Description": "Web 站点同步功能允许你填写一个网站的根地址，系统会自动深度抓取相关的网页进行知识库训练。仅会抓取静态的网站，以项目文档、博客为主。", "Confirm Create Tips": "确认同步该站点，同步任务将随后开启，请确认！", "Confirm Update Tips": "确认更新站点配置？会立即按新的配置开始同步，请确认！", "Selector": "选择器", "Selector Course": "使用教程", "Start Sync": "开始同步", "UnValid Website Tip": "您的站点可能非静态站点，无法同步"}}, "module": {"Add question type": "添加问题类型", "Can not connect self": "不能连接自身", "Confirm Delete Node": "确认删除该节点？", "Data Type": "数据类型", "Dataset quote": {"label": "知识库引用", "select": "选择知识库引用"}, "Default Value": "默认值", "Default value": "默认值", "Default value placeholder": "不填则默认返回空字符", "Edit intro": "编辑描述", "Field Description": "字段描述", "Field Name": "字段名", "Http request props": "请求参数", "Http request settings": "请求配置", "Input Type": "输入类型", "Laf sync params": "同步参数", "Max Length": "最大长度", "Max Length placeholder": "输入文本的最大长度", "Max Value": "最大值", "Min Value": "最小值", "QueryExtension": {"placeholder": "例如：\n关于 Python 的介绍和使用等问题。\n当前对话与游戏《GTA5》有关。"}, "Quote prompt setting": "引用提示词配置", "Select app": "选择应用", "Setting quote prompt": "配置引用提示词", "Variable": "全局变量", "Variable Setting": "变量设置", "edit": {"Field Name Cannot Be Empty": "字段名不能为空"}, "extract": {"Add field": "新增字段", "Enum Description": "列举出该字段可能的值，每行一个", "Enum Value": "枚举值", "Field Description Placeholder": "姓名/年龄/SQL 语句……", "Field Setting Title": "提取字段配置", "Required": "必须返回", "Required Description": "即使无法提取该字段，也会使用默认值进行返回", "Target field": "目标字段"}, "http": {"Add props": "添加参数", "AppId": "应用 ID", "AppSecret": "AppSecret", "ChatId": "当前对话 ID", "Current time": "当前时间", "Histories": "历史记录", "Key already exists": "Key 已经存在", "Key cannot be empty": "参数名不能为空", "Props name": "参数名", "Props tip": "可以设置 HTTP 请求的相关参数\n可通过 {{key}} 来调用全局变量或外部参数输入，当前可使用变量：\n{{variable}}", "Props value": "参数值", "ResponseChatItemId": "AI 回复的 ID", "Url and params have been split": "路径参数已被自动加入 Params 中", "curl import": "cURL 导入", "curl import placeholder": "请输入 cURL 格式内容，将会提取第一个接口的请求信息。"}, "input": {"Add Branch": "添加分支", "add": "添加条件", "description": {"Background": "你可以添加一些特定内容的介绍，从而更好的识别用户的问题类型。这个内容通常是给模型介绍一个它不知道的内容。", "HTTP Dynamic Input": "接收前方节点的输出值作为变量，这些变量可以被 HTTP 请求参数使用。", "Http Request Header": "自定义请求头，请严格填入 JSON 字符串。\n1. 确保最后一个属性没有逗号\n2. 确保 key 包含双引号\n例如：{\"Authorization\":\"Bearer xxx\"}", "Http Request Url": "新的 HTTP 请求地址。如果出现两个“请求地址”，可以删除该模块重新加入，会拉取最新的模块配置。", "Response content": "可以使用 \\n 来实现连续换行。\n可以通过外部模块输入实现回复，外部模块输入时会覆盖当前填写的内容。\n如传入非字符串类型数据将会自动转成字符串"}, "label": {"Background": "背景知识", "Http Request Url": "请求地址", "Response content": "回复的内容", "Select dataset": "选择知识库", "aiModel": "AI 模型", "chat history": "聊天记录", "user question": "用户问题"}, "placeholder": {"Classify background": "例如：\n1. AIGC（人工智能生成内容）是指使用人工智能技术自动或半自动地生成数字内容，如文本、图像、音乐、视频等。\n2. AIGC 技术包括但不限于自然语言处理、计算机视觉、机器学习和深度学习。这些技术可以创建新内容或修改现有内容，以满足特定的创意、教育、娱乐或信息需求。"}}, "laf": {"Select laf function": "选择 laf 函数"}, "output": {"description": {"Ai response content": "将在 stream 回复完毕后触发", "New context": "将本次回复内容拼接上历史记录，作为新的上下文返回", "query extension result": "以字符串数组的形式输出，可将该结果直接连接到“知识库搜索”的“用户问题”中，建议不要连接到“AI 对话”的“用户问题”中"}, "label": {"Ai response content": "AI 回复内容", "New context": "新的上下文", "query extension result": "优化结果"}}, "template": {"AI function": "AI能力", "AI response switch tip": "如果你希望当前节点不输出内容，可以关闭该开关。AI 输出的内容不会展示给用户，你可以手动的使用“AI 回复内容”进行特殊处理。", "AI support tool tip": "支持函数调用的模型，可以更好的使用工具调用。", "Basic Node": "基础功能", "Query extension": "问题优化", "System Plugin": "系统插件", "Public Plugin": "公共插件", "System input module": "系统输入", "TFSwitch": "判断器", "TFSwitch intro": "根据传入的内容进行 True False 输出。默认情况下，当传入的内容为 false, undefined, null, 0, none 时，会输出 false。你也可以增加一些自定义的字符串来补充输出 false 的内容。非字符、非数字、非布尔类型，直接输出 true。", "Team Plugin": "我的插件", "Tool module": "工具", "UnKnow Module": "未知模块", "http body placeholder": "与 Apifox 相同的语法"}, "templates": {"Load plugin error": "加载插件失败"}, "variable": {"Custom type": "自定义变量", "Custom type desc": "可以定义一个无需用户填写的全局变量。\n该变量的值可以来自于 API 接口，分享链接的 Query 或通过【变量更新】模块进行赋值。", "add option": "添加选项", "input type": "文本", "key": "变量 key", "key is required": "变量 key 是必须的", "select type": "下拉单选", "text max length": "最大长度", "textarea type": "段落", "textarea type desc": "允许用户最多输入4000字的对话框", "variable name": "变量名", "variable name is required": "变量名不能为空", "variable option is required": "选项不能全空", "variable option is value is required": "选项内容不能为空", "variable options": "选项"}, "variable add option": "添加选项", "variable_update": "变量更新", "variable_update_info": "可以更新指定节点的输出值或更新全局变量"}, "plugin": {"Custom headers": "自定义请求头", "Free": "该插件无需积分消耗～", "Get Plugin Module Detail Failed": "加载插件异常", "Http plugin intro placeholder": "仅做展示，无实际效果", "cost": "积分消耗："}, "view_chat_detail": "查看对话详情", "workflow": {"Can not delete node": "该节点不允许删除", "Change input type tip": "修改输入类型会清空已填写的值，请确认！", "Check Failed": "工作流校验失败，请检查节点是否正确填值，以及连线是否正常", "Confirm stop debug": "确认终止调试？调试信息将会不保留。", "Copy node": "已复制节点", "Custom inputs": "自定义输入", "Custom outputs": "自定义输出", "Dataset quote": "知识库引用", "Debug": "调试", "Debug Node": "Debug 模式", "Failed": "运行失败", "Not intro": "这个节点没有介绍~", "Running": "运行中", "Skipped": "跳过运行", "Stop debug": "停止调试", "Success": "运行成功", "Value type": "数据类型", "Variable": {"Variable type": "变量类型"}, "debug": {"Done": "完成调试", "Hide result": "隐藏结果", "Not result": "无运行结果", "Run result": "运行结果", "Show result": "展示结果"}, "inputType": {"JSON Editor": "JSON 输入框", "Manual input": "手动输入", "Manual select": "手动选择", "Reference": "变量引用", "dynamicTargetInput": "动态外部数据", "input": "单行输入框", "number input": "数字输入框", "selectApp": "应用选择", "selectDataset": "知识库选择", "selectLLMModel": "对话模型选择", "switch": "开关", "textarea": "多行输入框"}, "publish": {"OnRevert version": "点击回退到该版本", "OnRevert version confirm": "确认回退至该版本？会为您保存编辑中版本的配置，并为回退版本创建一个新的发布版本。", "histories": "发布记录"}, "template": {"Multimodal": "多模态", "Search": "搜索"}, "tool": {"Handle": "工具连接器", "Select Tool": "选择工具"}, "value": "值", "variable": "变量"}}, "create": "去创建", "cron_job_run_app": "定时任务", "dataset": {"Confirm move the folder": "确认移动到该目录", "Confirm to delete the data": "确认删除该数据？", "Confirm to delete the file": "确认删除该文件及其所有数据？", "Create Folder": "创建文件夹", "Create manual collection": "创建手动数据集", "Delete Dataset Error": "删除知识库异常", "Edit Folder": "编辑文件夹", "Edit Info": "编辑信息", "Export": "导出", "Export Dataset Limit Error": "导出数据失败", "Folder Name": "输入文件夹名称", "Insert Data": "插入", "Manual collection Tip": "手动数据集允许创建一个空的容器装入数据", "Move Failed": "移动出现错误~", "Select Dataset": "选择该知识库", "Select Dataset Tips": "仅能选择同一个索引模型的知识库", "Select Folder": "进入文件夹", "Training Name": "数据训练", "collections": {"Collection Embedding": "{{total}} 组索引中", "Confirm to delete the folder": "确认删除该文件夹及里面所有内容？", "Create And Import": "新建/导入", "Data Amount": "数据总量", "Select Collection": "选择文件", "Select One Collection To Store": "选择一个文件进行存储"}, "data": {"Add Index": "新增自定义索引", "Can not edit": "无编辑权限", "Custom Index Number": "自定义索引{{number}}", "Default Index": "默认索引", "Delete Tip": "确认删除该条数据？", "Index Edit": "数据索引", "Index Placeholder": "输入索引文本内容", "Input Data": "导入新数据", "Input Success Tip": "导入数据成功", "Update Data": "更新数据", "Update Success Tip": "更新数据成功", "edit": {"Content": "数据内容", "Course": "说明文档", "Delete": "删除数据", "Index": "数据索引（{{amount}}）"}, "input is empty": "数据内容不能为空 "}, "deleteFolderTips": "确认删除该文件夹及其包含的所有知识库？删除后数据无法恢复，请确认！", "test": {"noResult": "搜索结果为空"}}, "default_reply": "默认回复", "error": {"Create failed": "创建失败", "fileNotFound": "文件找不到了~", "inheritPermissionError": "权限继承错误", "missingParams": "参数缺失", "team": {"overSize": "团队成员超出上限"}, "upload_file_error_filename": "{{name}} 上传失败"}, "extraction_results": "提取结果", "field_name": "字段名", "get_QR_failed": "获取二维码失败", "get_app_failed": "获取应用失败", "get_laf_failed": "获取Laf函数列表失败", "has_verification": "已验证，点击取消绑定", "info": {"buy_extra": "购买额外套餐", "csv_download": "点击下载批量测试模板", "csv_message": "读取 CSV 文件第一列进行批量测试，单次最多支持 100 组数据。", "felid_message": "字段key必须是纯英文字母或数字，并且不能以数字开头。", "free_plan": "免费版团队连续30天未登录系统时，系统会自动清理账号知识库。", "include": "包含标准套餐与额外资源包", "node_info": "调整该模块会对工具调用时机有影响。\n你可以通过精确的描述该模块功能，引导模型进行工具调用。", "old_version_attention": "检测到您的高级编排为旧版，系统将为您自动格式化成新版工作流。\n\n由于版本差异较大，会导致一些工作流无法正常排布，请重新手动连接工作流。如仍异常，可尝试删除对应节点后重新添加。\n\n你可以直接点击调试进行工作流测试，调试完毕后点击发布。直到你点击发布，新工作流才会真正保存生效。\n\n在你发布新工作流前，自动保存不会生效。", "open_api_notice": "可以填写 OpenAI/OneAPI的相关秘钥。如果你填写了该内容，在线上平台使用【AI对话】、【问题分类】和【内容提取】将会走你填写的Key，不会计费。请注意你的Key 是否有访问对应模型的权限。GPT模型可以选择 FastAI。", "open_api_placeholder": "请求地址，默认为 openai 官方。可填中转地址，未自动补全 \"v1\"", "resource": "资源用量"}, "invalid_variable": "无效变量", "is_open": "是否开启", "item_description": "字段描述", "item_name": "字段名", "key_repetition": "key 重复", "navbar": {"Account": "账号", "Chat": "聊天", "Datasets": "知识库", "Studio": "工作台", "Tools": "工具", "Prompt": "提示词"}, "new_create": "新建", "no_data": "暂无数据", "no_laf_env": "系统未配置Laf环境", "not_yet_introduced": "暂无介绍", "pay": {"amount": "金额", "balance": "账号余额", "balance_notice": "账号余额不足", "confirm_pay": "确认支付", "get_pay_QR": "获取充值二维码", "need_pay": "需支付", "new_package_price": "新套餐价格", "notice": "请勿关闭页面", "old_package_price": "旧套餐价格", "other": "其他金额，请取整数", "to_recharge": "余额不足，去充值", "wechat": "请微信扫码支付: {{price}}元，请勿关闭页面", "yuan": "{{amount}}元"}, "permission": {"Collaborator": "协作者", "Default permission": "默认权限", "Manage": "管理", "No InheritPermission": "已限制权限，不再继承父级文件夹的权限，", "Not collaborator": "暂无协作者", "Owner": "创建者", "Permission": "权限", "Permission config": "权限配置", "Private": "私有", "Private Tip": "仅自己可用", "Public": "团队", "Public Tip": "团队所有成员可使用", "Remove InheritPermission Confirm": "此操作会导致权限继承失效，是否进行？", "Resume InheritPermission Confirm": "是否恢复为继承父级文件夹的权限？", "Resume InheritPermission Failed": "恢复失败", "Resume InheritPermission Success": "恢复成功", "change_owner": "转移所有权", "change_owner_failed": "转移所有权失败", "change_owner_placeholder": "输入用户名查找账号", "change_owner_success": "成功转移所有权", "change_owner_tip": "转移后将保留您的管理员权限", "change_owner_to": "转移给"}, "plugin": {"App": "选择应用", "Currentapp": "当前应用", "Description": "描述", "Edit Http Plugin": "编辑 HTTP 插件", "Enter PAT": "请输入访问凭证（PAT）", "Get Plugin Module Detail Failed": "获取插件信息异常", "Import Plugin": "导入 HTTP 插件", "Import from URL": "从 URL 导入。https://xxxx", "Intro": "插件介绍", "Invalid Env": "laf 环境错误", "Invalid Schema": "Schema 无效", "Invalid URL": "URL 无效", "Method": "方法", "Path": "路径", "Please bind laf accout first": "请先绑定 laf 账号", "Plugin List": "插件列表", "Search plugin": "搜索插件", "Set Name": "给插件取个名字", "contribute": "贡献插件", "go to laf": "去编写", "path": "路径"}, "reply_now": "立即回复", "required": "必须", "resume_failed": "恢复失败", "select_reference_variable": "选择引用变量", "share_link": "分享链接", "support": {"account": {"Individuation": "个性化"}, "inform": {"Read": "已读"}, "openapi": {"Api baseurl": "API 根地址", "Api manager": "API 秘钥管理", "Copy success": "已复制 API 地址", "New api key": "新的 API 秘钥", "New api key tip": "请保管好你的秘钥，秘钥不会再次展示~"}, "outlink": {"Delete link tip": "确认删除该免登录链接？删除后，该链接将会立即失效，对话日志仍会保留，请确认！", "Max usage points": "积分上限", "Max usage points tip": "该链接最多允许使用多少积分，超出后将无法使用。-1 代表无限制。", "Usage points": "积分消耗", "share": {"Response Quote": "返回引用", "Response Quote tips": "在分享链接中返回引用内容，但不会允许用户下载原文档"}}, "permission": {"Permission": "权限"}, "standard": {"AI Bonus Points": "AI 积分", "Expired Time": "结束时间", "Start Time": "开始时间", "storage": "存储量", "type": "类型"}, "team": {"limit": {"No permission rerank": "无权使用结果重排，请升级您的套餐"}}, "user": {"Avatar": "头像", "Go laf env": "点击前往 {{env}} 获取 PAT 凭证。", "Laf account course": "查看绑定 laf 账号教程。", "Laf account intro": "绑定你的 laf 账号后，你将可以在工作流中使用 laf 模块，实现在线编写代码。", "Need to login": "请先登录", "Price": "计费标准", "User self info": "个人信息", "auth": {"Sending Code": "正在发送"}, "inform": {"System message": "系统消息"}, "login": {"And": "和", "Email": "邮箱", "Forget Password": "忘记密码？", "Github": "GitHub 登录", "Google": "Google 登录", "Password": "密码", "Password login": "密码登录", "Phone": "手机号登录", "Phone number": "手机号", "Policy tip": "使用即代表你同意我们的", "Privacy": "隐私政策", "Provider error": "登录异常，请重试", "Register": "注册账号", "Root login": "使用 root 用户登录", "Root password placeholder": "root 密码为你设置的环境变量", "Terms": "服务协议", "Username": "用户名", "Wechat": "微信登录", "can_not_login": "无法登录，点击联系", "error": "登录异常", "security_failed": "安全校验失败", "wx_qr_login": "微信扫码登录"}, "logout": {"confirm": "确认退出登录?"}, "team": {"Dataset usage": "知识库容量", "Team Tags Async Success": "同步完成", "member": "成员"}}, "wallet": {"Ai point every thousand tokens": "{{points}} 积分/1K tokens", "Amount": "金额", "Bills": "账单", "Buy": "购买", "Confirm pay": "支付确认", "Not sufficient": "您的 AI 积分不足，请先升级套餐或购买额外 AI 积分后继续使用。", "Plan expired time": "套餐到期时间", "Plan reset time": "套餐重置时间", "Standard Plan Detail": "套餐详情", "To read plan": "查看套餐", "amount_0": "购买数量不能为0", "bill": {"Number": "订单号", "Status": "状态", "Type": "订单类型", "payWay": {"Way": "支付方式", "balance": "余额支付", "wx": "微信支付"}, "status": {"closed": "已关闭", "notpay": "未支付", "refund": "已退款", "success": "支付成功"}}, "buy_resource": "购买资源包", "moduleName": {"index": "索引生成", "qa": "QA 拆分"}, "noBill": "无账单记录~", "subscription": {"AI points": "AI 积分", "AI points click to read tip": "每次调用 AI 模型时，都会消耗一定的 AI 积分（类似于 token）。点击可查看详细计算规则。", "AI points usage": "AI 积分使用量", "AI points usage tip": "每次调用 AI 模型时，都会消耗一定的 AI 积分。具体的计算标准可参考上方的“计费标准”", "Ai points": "AI 积分计算标准", "Buy now": "切换套餐", "Current plan": "当前套餐", "Extra ai points": "额外 AI 积分", "Extra dataset size": "额外知识库容量", "Extra plan": "额外资源包", "Extra plan tip": "标准套餐不够时，您可以购买额外资源包继续使用", "FAQ": "常见问题", "Month amount": "月数", "Next plan": "未来套餐", "Nonsupport": "无法切换", "Stand plan level": "订阅套餐", "Standard update fail": "修改订阅套餐异常", "Standard update success": "变更订阅套餐成功！", "Sub plan": "订阅套餐", "Sub plan tip": "免费使用 {{title}} 或升级更高的套餐", "Team plan and usage": "套餐与用量", "Training weight": "训练优先级：{{weight}}", "Update extra ai points": "额外 AI 积分", "Update extra dataset size": "额外存储量", "Upgrade plan": "升级套餐", "ai_model": "AI语言模型", "function": {"History store": "{{amount}} 天对话记录保留", "Max app": "{{amount}} 个应用&插件", "Max dataset": "{{amount}} 个知识库", "Max dataset size": "{{amount}} 组知识库索引", "Max members": "{{amount}} 个团队成员", "Points": "{{amount}} AI 积分"}, "mode": {"Month": "按月", "Period": "订阅周期", "Year": "按年", "Year sale": "赠送两个月"}, "point": "积分", "standardSubLevel": {"enterprise": "企业版", "experience": "体验版", "free": "免费版", "free desc": "每月均可免费使用基础功能，连续 30 天未登录系统，将会自动清除知识库", "team": "团队版"}, "token_compute": "点击查看在线 Tokens 计算器", "type": {"balance": "余额充值", "extraDatasetSize": "知识库扩容", "extraPoints": "AI 积分套餐", "standard": "套餐订阅"}}, "usage": {"Ai model": "AI 模型", "App name": "应用名", "Audio Speech": "语音播放", "Bill Module": "扣费模块", "Duration": "时长（秒）", "Extension result": "问题优化结果", "Module name": "模块名", "Source": "来源", "Text Length": "文本长度", "Time": "生成时间", "Token Length": "token 长度", "Total": "总金额", "Total points": "AI 积分消耗", "Usage Detail": "使用详情", "Whisper": "语音输入"}}}, "sync_link": "同步链接", "system": {"Concat us": "联系我们", "Help Document": "帮助文档"}, "tag_list": "标签列表", "team_tag": "团队标签", "template": {"Quote Content Tip": "可以自定义引用内容的结构，以更好的适配不同场景。可以使用一些变量来进行模板配置：\n{{q}} - 检索内容，{{a}} - 预期内容，{{source}} - 来源，{{sourceId}} - 来源文件名，{{index}} - 第 n 个引用，他们都是可选的，下面是默认值：\n{{default}}", "Quote Prompt Tip": "可以用 {{quote}} 来插入引用内容模板，使用 {{question}} 来插入问题。下面是默认值：\n{{default}}"}, "textarea_variable_picker_tip": "输入 / 可选择变量", "tool_field": "工具字段参数配置", "undefined_var": "引用了未定义的变量，是否自动添加？", "unit": {"character": "字符", "minute": "分钟"}, "unusable_variable": "无可用变量", "upload_file_error": "上传文件失败", "user": {"Account": "账号", "Amount of earnings": "收益（￥）", "Amount of inviter": "累计邀请人数", "Application Name": "项目名", "Avatar": "头像", "Change": "变更", "Copy invite url": "复制邀请链接", "Edit name": "点击修改昵称", "Invite Url": "邀请链接", "Invite url tip": "通过该链接注册的好友将永久与你绑定，其充值时你会获得一定余额奖励。\n此外，好友使用手机号注册时，你将立即获得 5 元奖励。\n奖励会发送到您的默认团队中。", "Laf Account Setting": "laf 账号配置", "Language": "语言", "Member Name": "昵称", "Notice": "通知", "Notification Receive": "通知接收", "Notification Receive Bind": "请先绑定通知接收途径", "Old password is error": "旧密码错误", "OpenAI Account Setting": "OpenAI 账号配置", "Password": "密码", "Pay": "充值", "Personal Information": "个人信息", "Promotion": "促销", "Promotion Rate": "返现比例", "Promotion Record": "推广记录", "Promotion rate tip": "好友充值时你将获得一定比例的余额奖励", "Replace": "更换", "Set OpenAI Account Failed": "设置 OpenAI 账号异常", "Sign Out": "登出", "Team": "团队", "Time": "时间", "Timezone": "时区", "Update Password": "修改密码", "Update password failed": "修改密码异常", "Update password successful": "修改密码成功", "Usage Record": "使用记录", "apikey": {"key": "API 秘钥"}, "confirm_password": "确认密码", "new_password": "新密码", "no_invite_records": "暂无邀请记录", "no_notice": "暂无通知", "no_usage_records": "暂无使用记录", "old_password": "旧密码", "password_message": "密码最少 4 位最多 60 位", "team": {"Balance": "团队余额", "Check Team": "切换", "Confirm Invite": "确认邀请", "Create Team": "创建新团队", "Invite Member": "邀请成员", "Invite Member Failed Tip": "邀请成员出现异常", "Invite Member Result Tip": "邀请结果提示", "Invite Member Success Tip": "邀请成员完成\n成功：{{success}} 人\n用户名无效：{{inValid}}\n已在团队中：{{inTeam}}", "Invite Member Tips": "对方可查阅或使用团队内的其他资源", "Leave Team": "离开团队", "Leave Team Failed": "离开团队异常", "Member": "成员", "Member Name": "成员名", "Over Max Member Tip": "团队最多 {{max}} 人", "Personal Team": "个人团队", "Processing invitations": "处理邀请", "Processing invitations Tips": "你有 {{amount}} 个需要处理的团队邀请", "Remove Member Confirm Tip": "确认将 {{username}} 移出团队？", "Select Team": "团队选择", "Set Name": "给团队取个名字", "Switch Team Failed": "切换团队异常", "Tags Async": "保存", "Team Name": "团队名", "Team Tags Async": "标签同步", "Team Tags Async Success": "链接报错成功，标签信息更新", "Update Team": "更新团队信息", "invite": {"Accept Confirm": "确认加入该团队？", "Accepted": "已加入团队", "Deal Width Footer Tip": "处理完会自动关闭噢~", "Reject": "已拒绝邀请", "Reject Confirm": "确认拒绝该邀请？", "accept": "接受", "reject": "拒绝"}, "member": {"Confirm Leave": "确认离开该团队？", "active": "已加入", "reject": "拒绝", "waiting": "待接受"}, "role": {"Admin": "管理员", "Owner": "创建者", "Visitor": "访客"}}, "type": "类型"}, "verification": "验证", "prompt": {"title": "提示词模板", "search_prompt": "搜索模板...", "Add Template": "添加模板", "No description": "暂无描述", "No matching templates": "没有匹配的模板", "No Templates": "暂无模板", "Template Title": "模板标题", "Enter template title": "输入模板标题", "Template Description": "模板描述", "Enter template description": "输入模板描述", "Create": "创建", "Cancel": "取消", "Title is required": "标题不能为空", "Create Template Success": "创建模板成功", "Create Template Failed": "创建模板失败", "Failed to load templates": "加载模板失败", "Add Content": "添加内容", "No Content": "暂无内容", "Edit Content": "编辑内容", "Content Title": "内容标题", "Enter content title": "输入内容标题", "Content": "内容", "Enter content": "输入内容", "Update": "更新", "Add": "添加", "Title and content are required": "标题和内容不能为空", "Add Content Success": "添加内容成功", "Add Content Failed": "添加内容失败", "Update Content Success": "更新内容成功", "Update Content Failed": "更新内容失败", "Delete Content Confirm": "确认删除该内容？", "Delete Content Success": "删除内容成功", "Delete Content Failed": "删除内容失败", "Copy Success": "复制成功", "Copy Failed": "复制失败", "Copy": "复制", "Failed to load template": "加载模板失败"}}