{"author": "", "version": "489", "name": "文本生成图片（即梦）", "avatar": "/imgs/plugins/text2imageJimeng.png", "intro": "根据文本生成图片", "showStatus": false, "weight": 10, "isTool": true, "templateType": "tools", "workflow": {"nodes": [{"nodeId": "pluginInput", "name": "自定义插件输入", "intro": "可以配置插件需要哪些输入，利用这些输入来运行插件", "avatar": "core/workflow/template/workflowStart", "flowNodeType": "pluginInput", "showStatus": false, "position": {"x": 48.4057934204464, "y": -1015.8589586029767}, "version": "481", "inputs": [{"renderTypeList": ["reference"], "selectedTypeIndex": 0, "valueType": "string", "canEdit": true, "key": "text", "label": "text", "description": "待生成图片的描述信息", "required": true, "toolDescription": "待生成图片的描述信息"}, {"renderTypeList": ["numberInput"], "selectedTypeIndex": 0, "valueType": "number", "canEdit": true, "key": "seed", "label": "seed", "description": "随机种子，若参数为相同正整数且其他参数均一致，则生成图片极大概率效果一致。\n默认数值-1，即随机生成种子值", "required": false, "defaultValue": "-1"}, {"renderTypeList": ["numberInput"], "selectedTypeIndex": 0, "valueType": "number", "canEdit": true, "key": "scale", "label": "scale", "description": "图片生成时，对于文本描述的遵循程度。数值越小，遵循程度越弱。数值越大，遵循程度越强\n默认值：2.5\n取值范围：[1，10]", "required": false, "defaultValue": "2.5", "min": 1, "max": 10}, {"renderTypeList": ["numberInput"], "selectedTypeIndex": 0, "valueType": "number", "canEdit": true, "key": "width", "label": "width", "description": "生成图像的宽度（像素）\n默认值:1328\n取值范围:[512,2048]", "required": false, "defaultValue": "1328", "min": 512, "max": 2048}, {"renderTypeList": ["numberInput"], "selectedTypeIndex": 0, "valueType": "number", "canEdit": true, "key": "height", "label": "height", "description": "生成图像的高度（像素）\n默认值:1328\n取值范围:[512,2048]", "required": false, "defaultValue": "1328", "min": 512, "max": 2048}], "outputs": [{"id": "description", "valueType": "string", "key": "text", "label": "text", "type": "hidden"}, {"id": "seed", "valueType": "number", "key": "seed", "label": "seed", "type": "hidden"}, {"id": "scale", "valueType": "number", "key": "scale", "label": "scale", "type": "hidden"}, {"id": "width", "valueType": "number", "key": "width", "label": "width", "type": "hidden"}, {"id": "height", "valueType": "number", "key": "height", "label": "height", "type": "hidden"}]}, {"nodeId": "pluginOutput", "name": "自定义插件输出", "intro": "自定义配置外部输出，使用插件时，仅暴露自定义配置的输出", "avatar": "core/workflow/template/pluginOutput", "flowNodeType": "pluginOutput", "showStatus": false, "position": {"x": 2071.531669701357, "y": -1287.7051739028761}, "version": "481", "inputs": [{"renderTypeList": ["reference"], "valueType": "string", "canEdit": true, "key": "image_url", "label": "image_url", "description": "图片的链接地址", "value": ["eaJe5wkNTC3m", "cL68LlJZ2oCU"]}, {"renderTypeList": ["reference"], "valueType": "string", "canEdit": true, "key": "markdown", "label": "markdown", "description": "", "value": ["iKhHQiIya06k", "system_text"]}], "outputs": []}, {"nodeId": "eaJe5wkNTC3m", "name": "HTTP 请求", "intro": "可以发出一个 HTTP 请求，实现更为复杂的操作（联网搜索、数据库查询等）", "avatar": "core/workflow/template/httpRequest", "flowNodeType": "httpRequest468", "showStatus": true, "position": {"x": 703.412381695632, "y": -1689.046722814696}, "version": "481", "inputs": [{"key": "system_addInputParam", "renderTypeList": ["addInputParam"], "valueType": "dynamic", "label": "", "required": false, "description": "core.module.input.description.HTTP Dynamic Input", "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": true}}, {"key": "system_httpMethod", "renderTypeList": ["custom"], "valueType": "string", "label": "", "value": "POST", "required": true}, {"key": "system_httpReqUrl", "renderTypeList": ["hidden"], "valueType": "string", "label": "", "description": "core.module.input.description.Http Request Url", "placeholder": "https://api.ai.com/getInventory", "required": false, "value": "https://pro.fofinvesting.com/api/ai/chat/visual/text2image/v3"}, {"key": "system_httpHeader", "renderTypeList": ["custom"], "valueType": "any", "value": [{"key": "Authorization", "type": "string", "value": "aigle-prod"}], "label": "", "description": "core.module.input.description.Http Request Header", "placeholder": "core.module.input.description.Http Request Header", "required": false}, {"key": "system_httpParams", "renderTypeList": ["hidden"], "valueType": "any", "value": [], "label": "", "required": false}, {"key": "system_httpJsonBody", "renderTypeList": ["hidden"], "valueType": "any", "value": "{\r\n    \"prompt\": \"{{text}}\",\r\n    \"seed\": {{seed}},\r\n    \"scale\": {{scale}},\r\n    \"width\": {{width}},\r\n    \"height\": {{height}}\r\n}", "label": "", "required": false}, {"renderTypeList": ["reference"], "valueType": "string", "canEdit": true, "key": "text", "label": "text", "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": true}, "required": true, "value": ["pluginInput", "description"]}, {"renderTypeList": ["reference"], "valueType": "number", "canEdit": true, "key": "seed", "label": "seed", "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": true}, "required": true, "value": ["pluginInput", "seed"]}, {"renderTypeList": ["reference"], "valueType": "number", "canEdit": true, "key": "scale", "label": "scale", "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": true}, "required": true, "value": ["pluginInput", "scale"]}, {"renderTypeList": ["reference"], "valueType": "number", "canEdit": true, "key": "width", "label": "width", "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": true}, "required": true, "value": ["pluginInput", "width"]}, {"renderTypeList": ["reference"], "valueType": "number", "canEdit": true, "key": "height", "label": "height", "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": true}, "required": true, "value": ["pluginInput", "height"]}], "outputs": [{"id": "error", "key": "error", "label": "请求错误", "description": "HTTP请求错误信息，成功时返回空", "valueType": "object", "type": "static"}, {"id": "httpRawResponse", "key": "httpRawResponse", "label": "原始响应", "required": true, "description": "HTTP请求的原始响应。只能接受字符串或JSON类型响应数据。", "valueType": "any", "type": "static"}, {"id": "system_addOutputParam", "key": "system_addOutputParam", "type": "dynamic", "valueType": "dynamic", "label": "", "customFieldConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": false}}, {"id": "cL68LlJZ2oCU", "valueType": "string", "type": "dynamic", "key": "data.image_urls[0]", "label": "data.image_urls[0]"}]}, {"nodeId": "iKhHQiIya06k", "name": "文本拼接", "intro": "可对固定或传入的文本进行加工后输出，非字符串类型数据最终会转成字符串类型。", "avatar": "core/workflow/template/textConcat", "flowNodeType": "textEditor", "position": {"x": 1328.2736209711045, "y": -888.4172167532734}, "version": "486", "inputs": [{"key": "system_addInputParam", "renderTypeList": ["addInputParam"], "valueType": "dynamic", "label": "", "required": false, "description": "可以引用其他节点的输出，作为文本拼接的变量，通过 {{字段名}} 来引用变量", "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": false}}, {"key": "system_textareaInput", "renderTypeList": ["textarea"], "valueType": "string", "required": true, "label": "拼接文本", "placeholder": "可通过 {{字段名}} 来引用变量", "value": "![图片]({{image_url}})"}, {"renderTypeList": ["reference"], "valueType": "string", "canEdit": true, "key": "image_url", "label": "image_url", "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": false}, "required": true, "value": ["eaJe5wkNTC3m", "cL68LlJZ2oCU"]}], "outputs": [{"id": "system_text", "key": "system_text", "label": "拼接结果", "type": "static", "valueType": "string"}]}], "edges": [{"source": "pluginInput", "target": "eaJe5wkNTC3m", "sourceHandle": "pluginInput-source-right", "targetHandle": "eaJe5wkNTC3m-target-left"}, {"source": "iKhHQiIya06k", "target": "pluginOutput", "sourceHandle": "iKhHQiIya06k-source-right", "targetHandle": "pluginOutput-target-left"}, {"source": "eaJe5wkNTC3m", "target": "iKhHQiIya06k", "sourceHandle": "eaJe5wkNTC3m-source-right", "targetHandle": "iKhHQiIya06k-target-left"}, {"source": "eaJe5wkNTC3m", "target": "pluginOutput", "sourceHandle": "eaJe5wkNTC3m-source-right", "targetHandle": "pluginOutput-target-left"}]}}