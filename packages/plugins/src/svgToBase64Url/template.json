{"author": "", "version": "486", "name": "Svg to Base64Url", "avatar": "/imgs/plugins/svgToBase64Url.png", "intro": "Convert SVG code block to accessible image link", "showStatus": false, "weight": 10, "isTool": true, "templateType": "tools", "workflow": {"nodes": [{"nodeId": "vIJwGZBuK4F3", "name": "插件输入", "intro": "可以配置插件需要哪些输入，利用这些输入来运行插件", "avatar": "core/workflow/template/workflowStart", "flowNodeType": "pluginInput", "showStatus": false, "position": {"x": 494.2610759045666, "y": -274.6190549937865}, "version": "481", "inputs": [{"key": "content", "valueType": "string", "label": "content", "renderTypeList": ["reference"], "required": true, "description": "包含SVG内容的markdown格式代码块", "toolDescription": "包含SVG内容的markdown格式代码块", "canEdit": true}], "outputs": [{"id": "q480wMRWGse5", "key": "content", "valueType": "string", "label": "content", "type": "source", "edit": true}]}, {"nodeId": "n8ML7PFNcGcC", "name": "自定义插件输出", "intro": "自定义配置外部输出，使用插件时，仅暴露自定义配置的输出", "avatar": "core/workflow/template/pluginOutput", "flowNodeType": "pluginOutput", "showStatus": false, "position": {"x": 2361.247666066118, "y": -355.07974981268694}, "version": "481", "inputs": [{"key": "url", "valueType": "string", "label": "url", "renderTypeList": ["reference"], "required": false, "description": "图片的链接", "canEdit": true, "value": ["vlWWGFdKbT17", "qLUQfhG0ILRX"]}, {"renderTypeList": ["reference"], "valueType": "string", "canEdit": true, "key": "markdown", "label": "markdown", "description": "图片的链接，按 markdown 格式", "value": ["vVQtJVHePgH9", "system_text"]}], "outputs": [{"id": "n8ML7PFNcGcC", "key": "result", "valueType": "string", "label": "result", "type": "static"}]}, {"nodeId": "vVQtJVHePgH9", "name": "文本拼接", "intro": "可对固定或传入的文本进行加工后输出，非字符串类型数据最终会转成字符串类型。", "avatar": "core/workflow/template/textConcat", "flowNodeType": "textEditor", "position": {"x": 1773.405684304501, "y": -21.86223317976568}, "version": "486", "inputs": [{"key": "system_addInputParam", "renderTypeList": ["addInputParam"], "valueType": "dynamic", "label": "", "required": false, "description": "可以引用其他节点的输出，作为文本拼接的变量，通过 {{字段名}} 来引用变量", "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": false}}, {"key": "system_textareaInput", "renderTypeList": ["textarea"], "valueType": "string", "required": true, "label": "拼接文本", "placeholder": "可通过 {{字段名}} 来引用变量", "value": "![图片]({{$vlWWGFdKbT17.qLUQfhG0ILRX$}})"}], "outputs": [{"id": "system_text", "key": "system_text", "label": "拼接结果", "type": "static", "valueType": "string"}]}, {"nodeId": "vlWWGFdKbT17", "name": "代码运行", "intro": "执行一段简单的脚本代码，通常用于进行复杂的数据处理。", "avatar": "core/workflow/template/codeRun", "flowNodeType": "code", "showStatus": true, "position": {"x": 1212.5360370549108, "y": -547.7251827860698}, "version": "482", "inputs": [{"key": "system_addInputParam", "renderTypeList": ["addInputParam"], "valueType": "dynamic", "label": "", "required": false, "description": "这些变量会作为代码的运行的输入参数", "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": true}}, {"key": "codeType", "renderTypeList": ["hidden"], "label": "", "value": "js"}, {"key": "code", "renderTypeList": ["custom"], "label": "", "value": "function main({svg_str}){\n    // 使用正则表达式匹配代码块中的内容\n    const match = svg_str.match(/```[\\w]*\\n([\\s\\S]*?)```/);\n\n    if (!match) {\n        // 如果没有匹配到代码块，返回一个错误信息或空结果\n        return {\n            result: null,\n            error: \"未找到有效的代码块标记。\"\n        };\n    }\n\n    // 提取代码块中的 SVG 内容\n    const extractedSvg = match[1].trim();\n    \n    const base64 = strToBase64(extractedSvg,'data:image/svg+xml;base64,')\n\n    return {\n        result: base64\n    }\n}"}, {"renderTypeList": ["reference"], "valueType": "string", "canEdit": true, "key": "svg_str", "label": "svg_str", "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": true}, "required": true, "value": ["vIJwGZBuK4F3", "q480wMRWGse5"]}], "outputs": [{"id": "system_rawResponse", "key": "system_rawResponse", "label": "完整响应数据", "valueType": "object", "type": "static"}, {"id": "error", "key": "error", "label": "运行错误", "description": "代码运行错误信息，成功时返回空", "valueType": "object", "type": "static"}, {"id": "system_addOutputParam", "key": "system_addOutputParam", "type": "dynamic", "valueType": "dynamic", "label": "", "customFieldConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": false}, "description": "将代码中 return 的对象作为输出，传递给后续的节点。变量名需要对应 return 的 key"}, {"id": "qLUQfhG0ILRX", "type": "dynamic", "key": "result", "valueType": "string", "label": "result"}]}], "edges": [{"source": "vVQtJVHePgH9", "target": "n8ML7PFNcGcC", "sourceHandle": "vVQtJVHePgH9-source-right", "targetHandle": "n8ML7PFNcGcC-target-left"}, {"source": "vIJwGZBuK4F3", "target": "vlWWGFdKbT17", "sourceHandle": "vIJwGZBuK4F3-source-right", "targetHandle": "vlWWGFdKbT17-target-left"}, {"source": "vlWWGFdKbT17", "target": "n8ML7PFNcGcC", "sourceHandle": "vlWWGFdKbT17-source-right", "targetHandle": "n8ML7PFNcGcC-target-left"}, {"source": "vlWWGFdKbT17", "target": "vVQtJVHePgH9", "sourceHandle": "vlWWGFdKbT17-source-right", "targetHandle": "vVQtJVHePgH9-target-left"}]}}