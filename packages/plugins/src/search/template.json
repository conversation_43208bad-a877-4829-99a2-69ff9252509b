{"author": "", "version": "486", "name": "网络搜索", "avatar": "/imgs/plugins/search.png", "intro": "通过关键字进行网络搜索，获取相关的搜索结果", "showStatus": false, "weight": 10, "isTool": true, "templateType": "tools", "workflow": {"nodes": [{"nodeId": "pluginInput", "name": "自定义插件输入", "intro": "可以配置插件需要哪些输入，利用这些输入来运行插件", "avatar": "core/workflow/template/workflowStart", "flowNodeType": "pluginInput", "showStatus": false, "position": {"x": 178.478190372021, "y": -729.4543757516363}, "version": "481", "inputs": [{"renderTypeList": ["reference"], "selectedTypeIndex": 0, "valueType": "string", "canEdit": true, "key": "query", "label": "query", "description": "用来搜索的关键字", "required": true, "toolDescription": "用来搜索的关键字"}], "outputs": [{"id": "url", "valueType": "string", "key": "query", "label": "query", "type": "hidden"}]}, {"nodeId": "pluginOutput", "name": "自定义插件输出", "intro": "自定义配置外部输出，使用插件时，仅暴露自定义配置的输出", "avatar": "core/workflow/template/pluginOutput", "flowNodeType": "pluginOutput", "showStatus": false, "position": {"x": 2134.0707650462195, "y": -726.2319492822604}, "version": "481", "inputs": [{"renderTypeList": ["reference"], "valueType": "object", "canEdit": true, "key": "result", "label": "result", "description": "", "value": ["y1MbiAaNf5Fv", "system_rawResponse"]}], "outputs": []}, {"nodeId": "qk042M32Z0Ef", "name": "HTTP 请求", "intro": "可以发出一个 HTTP 请求，实现更为复杂的操作（联网搜索、数据库查询等）", "avatar": "core/workflow/template/httpRequest", "flowNodeType": "httpRequest468", "showStatus": true, "position": {"x": 802.5215637527085, "y": -1007.6421535709627}, "version": "481", "inputs": [{"key": "system_addInputParam", "renderTypeList": ["addInputParam"], "valueType": "dynamic", "label": "", "required": false, "description": "core.module.input.description.HTTP Dynamic Input", "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": true}}, {"key": "system_httpMethod", "renderTypeList": ["custom"], "valueType": "string", "label": "", "value": "GET", "required": true}, {"key": "system_httpReqUrl", "renderTypeList": ["hidden"], "valueType": "string", "label": "", "description": "core.module.input.description.Http Request Url", "placeholder": "https://api.ai.com/getInventory", "required": false, "value": "https://modelapi.qutke.cn/search/bing"}, {"key": "system_httpHeader", "renderTypeList": ["custom"], "valueType": "any", "value": [], "label": "", "description": "core.module.input.description.Http Request Header", "placeholder": "core.module.input.description.Http Request Header", "required": false}, {"key": "system_httpParams", "renderTypeList": ["hidden"], "valueType": "any", "value": [{"key": "q", "type": "string", "value": "{{keyword}}"}], "label": "", "required": false}, {"key": "system_httpJsonBody", "renderTypeList": ["hidden"], "valueType": "any", "value": "", "label": "", "required": false}, {"renderTypeList": ["reference"], "valueType": "string", "canEdit": true, "key": "keyword", "label": "keyword", "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": true}, "required": true, "value": ["pluginInput", "url"]}], "outputs": [{"id": "error", "key": "error", "label": "请求错误", "description": "HTTP请求错误信息，成功时返回空", "valueType": "object", "type": "static"}, {"id": "httpRawResponse", "key": "httpRawResponse", "label": "原始响应", "required": true, "description": "HTTP请求的原始响应。只能接受字符串或JSON类型响应数据。", "valueType": "any", "type": "static"}, {"id": "system_addOutputParam", "key": "system_addOutputParam", "type": "dynamic", "valueType": "dynamic", "label": "", "customFieldConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": false}}]}, {"nodeId": "y1MbiAaNf5Fv", "name": "代码运行", "intro": "执行一段简单的脚本代码，通常用于进行复杂的数据处理。", "avatar": "core/workflow/template/codeRun", "flowNodeType": "code", "showStatus": true, "position": {"x": 1580.4185639044574, "y": -976.5371676896104}, "version": "482", "inputs": [{"key": "system_addInputParam", "renderTypeList": ["addInputParam"], "valueType": "dynamic", "label": "", "required": false, "description": "这些变量会作为代码的运行的输入参数", "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": true}}, {"key": "codeType", "renderTypeList": ["hidden"], "label": "", "value": "js"}, {"key": "code", "renderTypeList": ["custom"], "label": "", "value": "function main({data}){\n    const result = data.webPages.value.map((item, index) => ({\n      title: item.name,\n      link: item.url,\n      snippet: `\n      [webpage ${index + 1} begin]\n      ${item.snippet}\n      新闻标题：${item.name}\n      网页链接：${item.url}\n      [webpage ${index + 1} end]\n      `\n    })).map(item => item.snippet).join('')\n    return  JSON.stringify(result) \n}"}, {"renderTypeList": ["reference"], "valueType": "object", "canEdit": true, "key": "data", "label": "data", "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": true}, "required": true, "value": ["qk042M32Z0Ef", "httpRawResponse"]}], "outputs": [{"id": "system_rawResponse", "key": "system_rawResponse", "label": "完整响应数据", "valueType": "object", "type": "static"}, {"id": "error", "key": "error", "label": "运行错误", "description": "代码运行错误信息，成功时返回空", "valueType": "object", "type": "static"}, {"id": "system_addOutputParam", "key": "system_addOutputParam", "type": "dynamic", "valueType": "dynamic", "label": "", "customFieldConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": false}, "description": "将代码中 return 的对象作为输出，传递给后续的节点。变量名需要对应 return 的 key"}]}], "edges": [{"source": "pluginInput", "target": "qk042M32Z0Ef", "sourceHandle": "pluginInput-source-right", "targetHandle": "qk042M32Z0Ef-target-left"}, {"source": "qk042M32Z0Ef", "target": "y1MbiAaNf5Fv", "sourceHandle": "qk042M32Z0Ef-source-right", "targetHandle": "y1MbiAaNf5Fv-target-left"}, {"source": "y1MbiAaNf5Fv", "target": "pluginOutput", "sourceHandle": "y1MbiAaNf5Fv-source-right", "targetHandle": "pluginOutput-target-left"}]}}