{"author": "", "version": "486", "name": "Webpage Screenshot", "avatar": "/imgs/plugins/fetchUrlScreenshot.png", "intro": "Generate a screenshot of the provided webpage URL and output the image link", "showStatus": false, "weight": 10, "isTool": true, "templateType": "tools", "workflow": {"nodes": [{"nodeId": "vIJwGZBuK4F3", "name": "插件输入", "intro": "可以配置插件需要哪些输入，利用这些输入来运行插件", "avatar": "core/workflow/template/workflowStart", "flowNodeType": "pluginInput", "showStatus": false, "position": {"x": 473.55206291900333, "y": -154.80262272017}, "version": "481", "inputs": [{"key": "url", "valueType": "string", "label": "url", "renderTypeList": ["reference"], "required": true, "description": "网页的地址 URL", "toolDescription": "网页的地址 URL", "canEdit": true}, {"renderTypeList": ["reference"], "selectedTypeIndex": 0, "valueType": "string", "canEdit": true, "key": "viewport", "label": "viewport", "description": "参数用于指定页面的可视区域大小，以便模拟不同设备的显示效果。该参数支持以下枚举值：\n\niphone: 模拟 iPhone 设备，适合移动端布局。\nipad: 模拟 iPad 设备，适合检查平板电脑上的用户体验。\npc: 模拟常见的桌面电脑分辨率，确保应用在大屏幕上的表现。\nqhd: 模拟高清显示器，适合高分辨率设备的测试。\nsquare: 模拟正方形布局，适合社交媒体内容的展示。\nlandscape: 模拟横向布局，适合宽屏设备的使用场景。\nportrait: 模拟纵向布局，适合手机应用和阅读体验。", "required": false, "toolDescription": "参数用于指定页面的可视区域大小，以便模拟不同设备的显示效果。该参数支持以下枚举值：\n\niphone: 模拟 iPhone 设备，适合移动端布局。\nipad: 模拟 iPad 设备，适合检查平板电脑上的用户体验。\npc: 模拟常见的桌面电脑分辨率，确保应用在大屏幕上的表现。\nqhd: 模拟高清显示器，适合高分辨率设备的测试。\nsquare: 模拟正方形布局，适合社交媒体内容的展示。\nlandscape: 模拟横向布局，适合宽屏设备的使用场景。\nportrait: 模拟纵向布局，适合手机应用和阅读体验。"}], "outputs": [{"id": "q480wMRWGse5", "key": "url", "valueType": "string", "label": "url", "type": "source", "edit": true}, {"id": "viewport", "valueType": "string", "key": "viewport", "label": "viewport", "type": "hidden"}]}, {"nodeId": "n8ML7PFNcGcC", "name": "自定义插件输出", "intro": "自定义配置外部输出，使用插件时，仅暴露自定义配置的输出", "avatar": "core/workflow/template/pluginOutput", "flowNodeType": "pluginOutput", "showStatus": false, "position": {"x": 2337.580222654046, "y": -193.84529156794372}, "version": "481", "inputs": [{"key": "url", "valueType": "string", "label": "url", "renderTypeList": ["reference"], "required": false, "description": "图片的链接", "canEdit": true, "value": ["lV1281Zk9Jwd", "b4R48FOIp1ys"]}, {"renderTypeList": ["reference"], "valueType": "string", "canEdit": true, "key": "markdown", "label": "markdown", "description": "图片的链接，按 markdown 格式", "value": ["vVQtJVHePgH9", "system_text"]}], "outputs": [{"id": "n8ML7PFNcGcC", "key": "result", "valueType": "string", "label": "result", "type": "static"}]}, {"nodeId": "lV1281Zk9Jwd", "name": "HTTP 请求", "intro": "可以发出一个 HTTP 请求，实现更为复杂的操作（联网搜索、数据库查询等）", "avatar": "core/workflow/template/httpRequest", "flowNodeType": "httpRequest468", "showStatus": true, "position": {"x": 1184.158949511485, "y": -473.52694296182904}, "version": "481", "inputs": [{"key": "system_addInputParam", "renderTypeList": ["addInputParam"], "valueType": "dynamic", "label": "", "required": false, "description": "core.module.input.description.HTTP Dynamic Input", "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": true}}, {"key": "system_httpMethod", "renderTypeList": ["custom"], "valueType": "string", "label": "", "value": "GET", "required": true}, {"key": "system_httpReqUrl", "renderTypeList": ["hidden"], "valueType": "string", "label": "", "description": "core.module.input.description.Http Request Url", "placeholder": "https://api.ai.com/getInventory", "required": false, "value": "https://towa.fofinvesting.com/api/pluginApi/url_screenshot"}, {"key": "system_httpHeader", "renderTypeList": ["custom"], "valueType": "any", "value": [], "label": "", "description": "core.module.input.description.Http Request Header", "placeholder": "core.module.input.description.Http Request Header", "required": false}, {"key": "system_httpParams", "renderTypeList": ["hidden"], "valueType": "any", "value": [{"key": "url", "type": "string", "value": "{{url}}"}, {"key": "viewport", "type": "string", "value": "{{viewport}}"}], "label": "", "required": false}, {"key": "system_httpJsonBody", "renderTypeList": ["hidden"], "valueType": "any", "value": "{\n  \"content\": \"{{text}}\"\n}", "label": "", "required": false}, {"key": "url", "valueType": "string", "label": "url", "renderTypeList": ["reference"], "canEdit": true, "value": ["vIJwGZBuK4F3", "q480wMRWGse5"], "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": true}}, {"renderTypeList": ["reference"], "valueType": "string", "canEdit": true, "key": "viewport", "label": "viewport", "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": true}, "required": true, "value": ["vIJwGZBuK4F3", "viewport"]}], "outputs": [{"id": "error", "key": "error", "label": "请求错误", "description": "HTTP请求错误信息，成功时返回空", "valueType": "object", "type": "static"}, {"id": "httpRawResponse", "key": "httpRawResponse", "label": "原始响应", "required": true, "description": "HTTP请求的原始响应。只能接受字符串或JSON类型响应数据。", "valueType": "any", "type": "static"}, {"id": "system_addOutputParam", "key": "system_addOutputParam", "type": "dynamic", "valueType": "dynamic", "label": "", "customFieldConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": false}}, {"id": "b4R48FOIp1ys", "valueType": "string", "type": "dynamic", "key": "data", "label": "data"}]}, {"nodeId": "vVQtJVHePgH9", "name": "文本拼接", "intro": "可对固定或传入的文本进行加工后输出，非字符串类型数据最终会转成字符串类型。", "avatar": "core/workflow/template/textConcat", "flowNodeType": "textEditor", "position": {"x": 1771.9264690912464, "y": 15.11814715159744}, "version": "486", "inputs": [{"key": "system_addInputParam", "renderTypeList": ["addInputParam"], "valueType": "dynamic", "label": "", "required": false, "description": "可以引用其他节点的输出，作为文本拼接的变量，通过 {{字段名}} 来引用变量", "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": false}}, {"key": "system_textareaInput", "renderTypeList": ["textarea"], "valueType": "string", "required": true, "label": "拼接文本", "placeholder": "可通过 {{字段名}} 来引用变量", "value": "![网页快照]({{$lV1281Zk9Jwd.b4R48FOIp1ys$}})"}], "outputs": [{"id": "system_text", "key": "system_text", "label": "拼接结果", "type": "static", "valueType": "string"}]}], "edges": [{"source": "vIJwGZBuK4F3", "target": "lV1281Zk9Jwd", "sourceHandle": "vIJwGZBuK4F3-source-right", "targetHandle": "lV1281Zk9Jwd-target-left"}, {"source": "lV1281Zk9Jwd", "target": "n8ML7PFNcGcC", "sourceHandle": "lV1281Zk9Jwd-source-right", "targetHandle": "n8ML7PFNcGcC-target-left"}, {"source": "lV1281Zk9Jwd", "target": "vVQtJVHePgH9", "sourceHandle": "lV1281Zk9Jwd-source-right", "targetHandle": "vVQtJVHePgH9-target-left"}, {"source": "vVQtJVHePgH9", "target": "n8ML7PFNcGcC", "sourceHandle": "vVQtJVHePgH9-source-right", "targetHandle": "n8ML7PFNcGcC-target-left"}]}}