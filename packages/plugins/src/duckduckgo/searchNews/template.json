{"author": "", "version": "486", "name": "DuckDuckGo 新闻检索", "avatar": "core/workflow/template/duckduckgo", "intro": "使用 DuckDuckGo 进行新闻检索", "showStatus": true, "weight": 10, "isTool": true, "templateType": "search", "workflow": {"nodes": [{"nodeId": "pluginInput", "name": "自定义插件输入", "intro": "可以配置插件需要哪些输入，利用这些输入来运行插件", "avatar": "/imgs/workflow/input.png", "flowNodeType": "pluginInput", "showStatus": false, "position": {"x": 393.68844551739926, "y": -58.80666875994541}, "version": "481", "inputs": [{"renderTypeList": ["reference"], "selectedTypeIndex": 0, "valueType": "string", "canEdit": true, "key": "query", "label": "query", "description": "检索词", "required": true, "toolDescription": "检索词"}], "outputs": [{"id": "query", "valueType": "string", "key": "query", "label": "query", "type": "hidden"}]}, {"nodeId": "pluginOutput", "name": "自定义插件输出", "intro": "自定义配置外部输出，使用插件时，仅暴露自定义配置的输出", "avatar": "/imgs/workflow/output.png", "flowNodeType": "pluginOutput", "showStatus": false, "position": {"x": 1795.6509902691012, "y": -47.04550785550961}, "version": "481", "inputs": [{"renderTypeList": ["reference"], "valueType": "string", "canEdit": true, "key": "result", "label": "result", "description": " 检索结果", "value": ["hjnVuJAOwyXV", "lEyy5QqyIBrK"]}], "outputs": []}, {"nodeId": "hjnVuJAOwyXV", "name": "HTTP 请求", "intro": "可以发出一个 HTTP 请求，实现更为复杂的操作（联网搜索、数据库查询等）", "avatar": "/imgs/workflow/http.png", "flowNodeType": "httpRequest468", "showStatus": true, "position": {"x": 1054.6774638324207, "y": -403.06127656499825}, "version": "481", "inputs": [{"key": "system_addInputParam", "renderTypeList": ["addInputParam"], "valueType": "dynamic", "label": "", "required": false, "description": "core.module.input.description.HTTP Dynamic Input", "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": true}}, {"key": "system_httpMethod", "renderTypeList": ["custom"], "valueType": "string", "label": "", "value": "POST", "required": true}, {"key": "system_httpReqUrl", "renderTypeList": ["hidden"], "valueType": "string", "label": "", "description": "core.module.input.description.Http Request Url", "placeholder": "https://api.ai.com/getInventory", "required": false, "value": "duckduckgo/searchNews"}, {"key": "system_httpHeader", "renderTypeList": ["custom"], "valueType": "any", "value": [], "label": "", "description": "core.module.input.description.Http Request Header", "placeholder": "core.module.input.description.Http Request Header", "required": false}, {"key": "system_httpParams", "renderTypeList": ["hidden"], "valueType": "any", "value": [], "label": "", "required": false}, {"key": "system_httpJsonBody", "renderTypeList": ["hidden"], "valueType": "any", "value": "{\n  \"query\": \"{{query}}\"\n}", "label": "", "required": false}, {"renderTypeList": ["reference"], "valueType": "string", "canEdit": true, "key": "query", "label": "query", "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": true}, "required": true, "value": ["pluginInput", "query"]}], "outputs": [{"id": "system_addOutputParam", "key": "system_addOutputParam", "type": "dynamic", "valueType": "dynamic", "label": "", "customFieldConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": false}}, {"id": "error", "key": "error", "label": "请求错误", "description": "HTTP请求错误信息，成功时返回空", "valueType": "object", "type": "static"}, {"id": "httpRawResponse", "key": "httpRawResponse", "label": "原始响应", "required": true, "description": "HTTP请求的原始响应。只能接受字符串或JSON类型响应数据。", "valueType": "any", "type": "static"}, {"id": "lEyy5QqyIBrK", "valueType": "string", "type": "dynamic", "key": "result", "label": "result"}]}], "edges": [{"source": "pluginInput", "target": "hjnVuJAOwyXV", "sourceHandle": "pluginInput-source-right", "targetHandle": "hjnVuJAOwyXV-target-left"}, {"source": "hjnVuJAOwyXV", "target": "pluginOutput", "sourceHandle": "hjnVuJAOwyXV-source-right", "targetHandle": "pluginOutput-target-left"}]}}