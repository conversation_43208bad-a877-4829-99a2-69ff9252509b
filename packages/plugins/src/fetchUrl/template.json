{"author": "", "version": "486", "name": "网页内容抓取", "avatar": "core/workflow/template/fetchUrl", "intro": "可获取一个网页链接内容，并以 Markdown 格式输出，仅支持获取静态网站。", "showStatus": true, "weight": 10, "isTool": true, "templateType": "tools", "workflow": {"nodes": [{"nodeId": "lmpb9v2lo2lk", "name": "自定义插件输入", "intro": "自定义配置外部输入，使用插件时，仅暴露自定义配置的输入", "avatar": "/imgs/workflow/input.png", "flowNodeType": "pluginInput", "showStatus": false, "position": {"x": 487.2485939481787, "y": -159.13661665265613}, "version": "481", "inputs": [{"renderTypeList": ["reference"], "selectedTypeIndex": 0, "valueType": "string", "key": "url", "label": "url", "description": "需要读取的网页链接", "required": true, "toolDescription": "需要读取的网页链接"}], "outputs": [{"id": "url", "valueType": "string", "key": "url", "label": "url", "type": "hidden"}]}, {"nodeId": "i7uow4wj2wdp", "name": "自定义插件输出", "intro": "自定义配置外部输出，使用插件时，仅暴露自定义配置的输出", "avatar": "/imgs/workflow/output.png", "flowNodeType": "pluginOutput", "showStatus": false, "position": {"x": 1607.7142331269129, "y": -150.8808596935447}, "version": "481", "inputs": [{"key": "result", "valueType": "string", "label": "result", "renderTypeList": ["reference"], "required": false, "description": "", "canEdit": true, "editField": {"key": true, "description": true, "valueType": true}, "value": ["ebLCxU43hHuZ", "rH4tMV02robs"]}], "outputs": []}, {"nodeId": "ebLCxU43hHuZ", "name": "HTTP 请求", "intro": "可以发出一个 HTTP 请求，实现更为复杂的操作（联网搜索、数据库查询等）", "avatar": "/imgs/workflow/http.png", "flowNodeType": "httpRequest468", "showStatus": true, "position": {"x": 1050.9890727421412, "y": -415.2085119990912}, "version": "481", "inputs": [{"key": "system_addInputParam", "renderTypeList": ["addInputParam"], "valueType": "dynamic", "label": "", "required": false, "description": "core.module.input.description.HTTP Dynamic Input", "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": true}}, {"key": "system_httpMethod", "renderTypeList": ["custom"], "valueType": "string", "label": "", "value": "POST", "required": true}, {"key": "system_httpReqUrl", "renderTypeList": ["hidden"], "valueType": "string", "label": "", "description": "core.module.input.description.Http Request Url", "placeholder": "https://api.ai.com/getInventory", "required": false, "value": "fetchUrl"}, {"key": "system_httpHeader", "renderTypeList": ["custom"], "valueType": "any", "value": [], "label": "", "description": "core.module.input.description.Http Request Header", "placeholder": "core.module.input.description.Http Request Header", "required": false}, {"key": "system_httpParams", "renderTypeList": ["hidden"], "valueType": "any", "value": [], "label": "", "required": false}, {"key": "system_httpJsonBody", "renderTypeList": ["hidden"], "valueType": "any", "value": "{\n  \"url\": \"{{url}}\"\n}", "label": "", "required": false}, {"renderTypeList": ["reference"], "valueType": "string", "canEdit": true, "key": "url", "label": "url", "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": true}, "required": true, "value": ["lmpb9v2lo2lk", "url"]}], "outputs": [{"id": "system_addOutputParam", "key": "system_addOutputParam", "type": "dynamic", "valueType": "dynamic", "label": "", "customFieldConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": true}}, {"id": "error", "key": "error", "label": "请求错误", "description": "HTTP请求错误信息，成功时返回空", "valueType": "object", "type": "static"}, {"id": "httpRawResponse", "key": "httpRawResponse", "label": "原始响应", "required": true, "description": "HTTP请求的原始响应。只能接受字符串或JSON类型响应数据。", "valueType": "any", "type": "static"}, {"id": "rH4tMV02robs", "valueType": "string", "type": "dynamic", "key": "result", "label": "result"}]}], "edges": [{"source": "lmpb9v2lo2lk", "target": "ebLCxU43hHuZ", "sourceHandle": "lmpb9v2lo2lk-source-right", "targetHandle": "ebLCxU43hHuZ-target-left"}, {"source": "ebLCxU43hHuZ", "target": "i7uow4wj2wdp", "sourceHandle": "ebLCxU43hHuZ-source-right", "targetHandle": "i7uow4wj2wdp-target-left"}]}}