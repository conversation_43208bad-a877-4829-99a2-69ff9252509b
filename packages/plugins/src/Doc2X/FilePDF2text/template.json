{"author": "Menghuan1918", "version": "488", "name": "Doc2X PDF文件(文件)识别", "avatar": "plugins/doc2x", "intro": "将上传的PDF文件发送至Doc2X进行解析，返回带LaTeX公式的markdown格式的文本", "inputExplanationUrl": "https://fael3z0zfze.feishu.cn/wiki/Rkc5witXWiJoi5kORd2cofh6nDg?fromScene=spaceOverview", "showStatus": true, "weight": 10, "isTool": true, "templateType": "tools", "workflow": {"nodes": [{"nodeId": "pluginInput", "name": "自定义插件输入", "intro": "可以配置插件需要哪些输入，利用这些输入来运行插件", "avatar": "core/workflow/template/workflowStart", "flowNodeType": "pluginInput", "showStatus": false, "position": {"x": 388.243055058894, "y": -75.09744210499466}, "version": "481", "inputs": [{"renderTypeList": ["input"], "selectedTypeIndex": 0, "valueType": "string", "canEdit": true, "key": "apikey", "label": "apikey", "description": "Doc2X的验证密匙，对于个人用户可以从Doc2X官网 - 个人信息 - 身份令牌获得", "required": true, "toolDescription": "", "defaultValue": ""}, {"renderTypeList": ["reference"], "selectedTypeIndex": 0, "valueType": "arrayString", "canEdit": true, "key": "files", "label": "files", "description": "待处理的PDF文件", "required": true, "toolDescription": "待处理的PDF文件"}, {"renderTypeList": ["switch"], "selectedTypeIndex": 0, "valueType": "boolean", "canEdit": true, "key": "ocr", "label": "ocr", "description": "是否开启对PDF文件内图片的OCR识别，建议开启", "required": true, "toolDescription": "", "defaultValue": true}], "outputs": [{"id": "apikey", "valueType": "string", "key": "apikey", "label": "apikey", "type": "hidden"}, {"id": "url", "valueType": "arrayString", "key": "files", "label": "files", "type": "hidden"}, {"id": "formula", "valueType": "boolean", "key": "ocr", "label": "ocr", "type": "hidden"}]}, {"nodeId": "pluginOutput", "name": "自定义插件输出", "intro": "自定义配置外部输出，使用插件时，仅暴露自定义配置的输出", "avatar": "core/workflow/template/pluginOutput", "flowNodeType": "pluginOutput", "showStatus": false, "position": {"x": 1649.7796447278438, "y": -96.05331527115042}, "version": "481", "inputs": [{"renderTypeList": ["reference"], "valueType": "string", "canEdit": true, "key": "result", "label": "result", "description": "处理结果，由文件名以及文档内容组成，多个文件之间由横线分隔开", "value": ["zHG5jJBkXmjB", "xWQuEf50F3mr"]}, {"renderTypeList": ["reference"], "valueType": "string", "canEdit": true, "key": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>", "description": "文件处理失败原因，由文件名以及报错组成，多个文件之间由横线分隔开", "value": ["zHG5jJBkXmjB", "yDxzW5CFalGw"]}, {"renderTypeList": ["reference"], "valueType": "boolean", "canEdit": true, "key": "success", "label": "success", "description": "是否全部文件都处理成功，如有没有处理成功的文件，失败原因将会输出在failreason中", "value": ["zHG5jJBkXmjB", "m6CJJj7GFud5"]}], "outputs": []}, {"nodeId": "zHG5jJBkXmjB", "name": "HTTP 请求", "intro": "可以发出一个 HTTP 请求，实现更为复杂的操作（联网搜索、数据库查询等）", "avatar": "core/workflow/template/httpRequest", "flowNodeType": "httpRequest468", "showStatus": true, "position": {"x": 1077.7986740892777, "y": -496.9521622173004}, "version": "481", "inputs": [{"key": "system_addInputParam", "renderTypeList": ["addInputParam"], "valueType": "dynamic", "label": "", "required": false, "description": "core.module.input.description.HTTP Dynamic Input", "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": true}}, {"key": "system_httpMethod", "renderTypeList": ["custom"], "valueType": "string", "label": "", "value": "POST", "required": true}, {"key": "system_httpReqUrl", "renderTypeList": ["hidden"], "valueType": "string", "label": "", "description": "core.module.input.description.Http Request Url", "placeholder": "https://api.ai.com/getInventory", "required": false, "value": "Doc2X/FilePDF2text"}, {"key": "system_httpHeader", "renderTypeList": ["custom"], "valueType": "any", "value": [], "label": "", "description": "core.module.input.description.Http Request Header", "placeholder": "core.module.input.description.Http Request Header", "required": false}, {"key": "system_httpParams", "renderTypeList": ["hidden"], "valueType": "any", "value": [], "label": "", "required": false}, {"key": "system_httpJsonBody", "renderTypeList": ["hidden"], "valueType": "any", "value": "{\n  \"apikey\": \"{{apikey}}\",\n  \"files\": {{files}},\n  \"ocr\": {{ocr}}\n}", "label": "", "required": false}, {"renderTypeList": ["reference"], "valueType": "string", "canEdit": true, "key": "apikey", "label": "apikey", "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": true}, "required": true, "value": ["pluginInput", "apikey"]}, {"renderTypeList": ["reference"], "valueType": "arrayString", "canEdit": true, "key": "files", "label": "files", "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": true}, "required": true, "value": ["pluginInput", "url"]}, {"renderTypeList": ["reference"], "valueType": "boolean", "canEdit": true, "key": "ocr", "label": "ocr", "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": true}, "required": true, "value": ["pluginInput", "formula"]}], "outputs": [{"id": "error", "key": "error", "label": "请求错误", "description": "HTTP请求错误信息，成功时返回空", "valueType": "object", "type": "static"}, {"id": "httpRawResponse", "key": "httpRawResponse", "label": "原始响应", "required": true, "description": "HTTP请求的原始响应。只能接受字符串或JSON类型响应数据。", "valueType": "any", "type": "static"}, {"id": "system_addOutputParam", "key": "system_addOutputParam", "type": "dynamic", "valueType": "dynamic", "label": "", "customFieldConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": false}}, {"id": "xWQuEf50F3mr", "valueType": "string", "type": "dynamic", "key": "result", "label": "result"}, {"id": "m6CJJj7GFud5", "valueType": "boolean", "type": "dynamic", "key": "success", "label": "success"}, {"id": "yDxzW5CFalGw", "valueType": "string", "type": "dynamic", "key": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}]}], "edges": [{"source": "pluginInput", "target": "zHG5jJBkXmjB", "sourceHandle": "pluginInput-source-right", "targetHandle": "zHG5jJBkXmjB-target-left"}, {"source": "zHG5jJBkXmjB", "target": "pluginOutput", "sourceHandle": "zHG5jJBkXmjB-source-right", "targetHandle": "pluginOutput-target-left"}]}}