{"author": "", "version": "481", "templateType": "tools", "name": "生成HTML页面", "avatar": "/imgs/plugins/generateWebpage.png", "intro": "根据提供的HTML代码生成一个页面的访问链接", "showStatus": false, "isTool": true, "weight": 10, "workflow": {"nodes": [{"nodeId": "vIJwGZBuK4F3", "name": "插件输入", "intro": "可以配置插件需要哪些输入，利用这些输入来运行插件", "avatar": "core/workflow/template/workflowStart", "flowNodeType": "pluginInput", "showStatus": false, "position": {"x": 473.55206291900333, "y": -153.47907306500267}, "version": "481", "inputs": [{"key": "content", "valueType": "string", "label": "content", "renderTypeList": ["reference"], "required": false, "description": "HTML 代码的内容，严格填写 HTML 的代码格式", "toolDescription": "HTML 代码的内容，严格填写 HTML 的代码格式", "canEdit": true}], "outputs": [{"id": "q480wMRWGse5", "key": "content", "valueType": "string", "label": "content", "type": "source", "edit": true}]}, {"nodeId": "n8ML7PFNcGcC", "name": "自定义插件输出", "intro": "自定义配置外部输出，使用插件时，仅暴露自定义配置的输出", "avatar": "core/workflow/template/pluginOutput", "flowNodeType": "pluginOutput", "showStatus": false, "position": {"x": 1912.8312252945116, "y": -147.61187877864498}, "version": "481", "inputs": [{"key": "url", "valueType": "string", "label": "url", "renderTypeList": ["reference"], "required": false, "description": "", "canEdit": true, "value": ["lV1281Zk9Jwd", "b4R48FOIp1ys"]}], "outputs": [{"id": "n8ML7PFNcGcC", "key": "result", "valueType": "string", "label": "result", "type": "static"}]}, {"nodeId": "lV1281Zk9Jwd", "name": "HTTP 请求", "intro": "可以发出一个 HTTP 请求，实现更为复杂的操作（联网搜索、数据库查询等）", "avatar": "core/workflow/template/httpRequest", "flowNodeType": "httpRequest468", "showStatus": true, "position": {"x": 1188.947986995841, "y": -473.52694296182904}, "version": "481", "inputs": [{"key": "system_addInputParam", "renderTypeList": ["addInputParam"], "valueType": "dynamic", "label": "", "required": false, "description": "core.module.input.description.HTTP Dynamic Input", "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": true}}, {"key": "system_httpMethod", "renderTypeList": ["custom"], "valueType": "string", "label": "", "value": "POST", "required": true}, {"key": "system_httpReqUrl", "renderTypeList": ["hidden"], "valueType": "string", "label": "", "description": "core.module.input.description.Http Request Url", "placeholder": "https://api.ai.com/getInventory", "required": false, "value": "{{TOWA_HOST}}/api/pluginApi/genHtmlPageLink"}, {"key": "system_httpHeader", "renderTypeList": ["custom"], "valueType": "any", "value": [{"key": "towapluginapitoken", "type": "string", "value": "{{towaPluginApiToken}}"}, {"key": "token", "type": "string", "value": "{{token}}"}, {"key": "appId", "type": "string", "value": "{{appId}}"}, {"key": "chatId", "type": "string", "value": "{{chatId}}"}], "label": "", "description": "core.module.input.description.Http Request Header", "placeholder": "core.module.input.description.Http Request Header", "required": false}, {"key": "system_httpParams", "renderTypeList": ["hidden"], "valueType": "any", "value": [], "label": "", "required": false}, {"key": "system_httpJsonBody", "renderTypeList": ["hidden"], "valueType": "any", "value": "{\n  \"content\": \"{{text}}\"\n}", "label": "", "required": false}, {"key": "text", "valueType": "string", "label": "text", "renderTypeList": ["reference"], "canEdit": true, "value": ["vIJwGZBuK4F3", "q480wMRWGse5"], "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": true}}], "outputs": [{"id": "error", "key": "error", "label": "请求错误", "description": "HTTP请求错误信息，成功时返回空", "valueType": "object", "type": "static"}, {"id": "httpRawResponse", "key": "httpRawResponse", "label": "原始响应", "required": true, "description": "HTTP请求的原始响应。只能接受字符串或JSON类型响应数据。", "valueType": "any", "type": "static"}, {"id": "system_addOutputParam", "key": "system_addOutputParam", "type": "dynamic", "valueType": "dynamic", "label": "", "customFieldConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": false}}, {"id": "b4R48FOIp1ys", "valueType": "string", "type": "dynamic", "key": "data", "label": "data"}]}], "edges": [{"source": "vIJwGZBuK4F3", "target": "lV1281Zk9Jwd", "sourceHandle": "vIJwGZBuK4F3-source-right", "targetHandle": "lV1281Zk9Jwd-target-left"}, {"source": "lV1281Zk9Jwd", "target": "n8ML7PFNcGcC", "sourceHandle": "lV1281Zk9Jwd-source-right", "targetHandle": "n8ML7PFNcGcC-target-left"}]}}