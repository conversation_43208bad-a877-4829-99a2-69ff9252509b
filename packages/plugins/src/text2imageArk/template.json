{"author": "", "version": "481", "templateType": "tools", "name": "文本生成图片", "avatar": "/imgs/plugins/text2image.png", "intro": "通过文本描述生成图片", "showStatus": false, "isTool": true, "weight": 10, "workflow": {"nodes": [{"nodeId": "kIMOvHp1SHww", "name": "插件输入", "intro": "可以配置插件需要哪些输入，利用这些输入来运行插件", "avatar": "core/workflow/template/workflowStart", "flowNodeType": "pluginInput", "showStatus": false, "position": {"x": 549.4696678617796, "y": -345.75339620198645}, "version": "481", "inputs": [{"key": "text", "valueType": "string", "label": "text", "renderTypeList": ["reference"], "description": "待生成图片的描述信息", "toolDescription": "待生成图片的描述信息", "canEdit": true, "required": true}], "outputs": [{"id": "aUzOWVjZv8sF", "key": "text", "valueType": "string", "label": "text", "type": "source"}]}, {"nodeId": "wDExQ6oe4gia", "name": "自定义插件输出", "intro": "自定义配置外部输出，使用插件时，仅暴露自定义配置的输出", "avatar": "core/workflow/template/pluginOutput", "flowNodeType": "pluginOutput", "showStatus": false, "position": {"x": 2257.167070829617, "y": -878.7930774767368}, "version": "481", "inputs": [{"key": "image_url", "valueType": "string", "label": "image_url", "renderTypeList": ["reference"], "required": false, "description": "", "canEdit": true, "value": ["pp8VICJIKofD", "rKwkjPwJokcZ"]}, {"renderTypeList": ["reference"], "valueType": "string", "canEdit": true, "key": "markdown", "label": "markdown", "description": "", "value": ["y9mxzxhmtFEx", "system_text"]}], "outputs": [{"id": "wDExQ6oe4gia", "key": "result", "valueType": "string", "label": "result", "type": "static"}]}, {"nodeId": "pp8VICJIKofD", "name": "HTTP 请求", "intro": "可以发出一个 HTTP 请求，实现更为复杂的操作（联网搜索、数据库查询等）", "avatar": "core/workflow/template/httpRequest", "flowNodeType": "httpRequest468", "showStatus": true, "position": {"x": 1050.510943468858, "y": -1066.4817274186873}, "version": "481", "inputs": [{"key": "system_addInputParam", "renderTypeList": ["addInputParam"], "valueType": "dynamic", "label": "", "required": false, "description": "core.module.input.description.HTTP Dynamic Input", "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": true}}, {"key": "system_httpMethod", "renderTypeList": ["custom"], "valueType": "string", "label": "", "value": "POST", "required": true}, {"key": "system_httpReqUrl", "renderTypeList": ["hidden"], "valueType": "string", "label": "", "description": "core.module.input.description.Http Request Url", "placeholder": "https://api.ai.com/getInventory", "required": false, "value": "https://pro.fofinvesting.com/api/ai/chat/visual/text2image"}, {"key": "system_httpHeader", "renderTypeList": ["custom"], "valueType": "any", "value": [{"key": "Authorization", "type": "string", "value": "aigle-prod"}], "label": "", "description": "core.module.input.description.Http Request Header", "placeholder": "core.module.input.description.Http Request Header", "required": false}, {"key": "system_httpParams", "renderTypeList": ["hidden"], "valueType": "any", "value": [], "label": "", "required": false}, {"key": "system_httpJsonBody", "renderTypeList": ["hidden"], "valueType": "any", "value": "{\n  \"prompt\": \"{{text}}\"\n}", "label": "", "required": false}, {"renderTypeList": ["reference"], "valueType": "string", "canEdit": true, "key": "text", "label": "text", "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": true}, "required": true, "value": ["kIMOvHp1SHww", "aUzOWVjZv8sF"]}], "outputs": [{"id": "error", "key": "error", "label": "请求错误", "description": "HTTP请求错误信息，成功时返回空", "valueType": "object", "type": "static"}, {"id": "httpRawResponse", "key": "httpRawResponse", "label": "原始响应", "required": true, "description": "HTTP请求的原始响应。只能接受字符串或JSON类型响应数据。", "valueType": "any", "type": "static"}, {"id": "system_addOutputParam", "key": "system_addOutputParam", "type": "dynamic", "valueType": "dynamic", "label": "", "customFieldConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": false}}, {"id": "rKwkjPwJokcZ", "valueType": "string", "type": "dynamic", "key": "data.image_urls[0]", "label": "data.image_urls[0]"}]}, {"nodeId": "y9mxzxhmtFEx", "name": "文本拼接", "intro": "可对固定或传入的文本进行加工后输出，非字符串类型数据最终会转成字符串类型。", "avatar": "core/workflow/template/textConcat", "flowNodeType": "textEditor", "position": {"x": 1648.0446820669933, "y": -126.42590790916432}, "version": "486", "inputs": [{"key": "system_addInputParam", "renderTypeList": ["addInputParam"], "valueType": "dynamic", "label": "", "required": false, "description": "可以引用其他节点的输出，作为文本拼接的变量，通过 {{字段名}} 来引用变量", "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": false}}, {"key": "system_textareaInput", "renderTypeList": ["textarea"], "valueType": "string", "required": true, "label": "拼接文本", "placeholder": "可通过 {{字段名}} 来引用变量", "value": "![图片]({{$pp8VICJIKofD.rKwkjPwJokcZ$}})"}], "outputs": [{"id": "system_text", "key": "system_text", "label": "拼接结果", "type": "static", "valueType": "string"}]}], "edges": [{"source": "kIMOvHp1SHww", "target": "pp8VICJIKofD", "sourceHandle": "kIMOvHp1SHww-source-right", "targetHandle": "pp8VICJIKofD-target-left"}, {"source": "pp8VICJIKofD", "target": "wDExQ6oe4gia", "sourceHandle": "pp8VICJIKofD-source-right", "targetHandle": "wDExQ6oe4gia-target-left"}, {"source": "pp8VICJIKofD", "target": "y9mxzxhmtFEx", "sourceHandle": "pp8VICJIKofD-source-right", "targetHandle": "y9mxzxhmtFEx-target-left"}, {"source": "y9mxzxhmtFEx", "target": "wDExQ6oe4gia", "sourceHandle": "y9mxzxhmtFEx-source-right", "targetHandle": "wDExQ6oe4gia-target-left"}]}}