import { addLog } from '../../common/system/log';
import mongoose, { Model } from 'mongoose';

export default mongoose;
export * from 'mongoose';

export const connectionMongo = (() => {
  if (!global.mongodb) {
    global.mongodb = mongoose;
  }

  return global.mongodb;
})();

const addCommonMiddleware = (schema: mongoose.Schema) => {
  const operations = [
    /^find/,
    'save',
    'create',
    /^update/,
    /^delete/,
    'aggregate',
    'count',
    'countDocuments',
    'estimatedDocumentCount',
    'distinct',
    'insertMany'
  ];

  operations.forEach((op: any) => {
    schema.pre(op, function (this: any, next) {
      this._startTime = Date.now();
      this._query = this.getQuery ? this.getQuery() : null;

      next();
    });

    schema.post(op, function (this: any, result: any, next) {
      if (this._startTime) {
        const duration = Date.now() - this._startTime;

        const warnLogData = {
          query: this._query,
          op,
          duration
        };

        if (duration > 1000) {
          addLog.warn(`Slow operation ${duration}ms`, warnLogData);
        }
      }
      next();
    });
  });

  return schema;
};

export const getMongoModel = <T>(name: string, schema: mongoose.Schema) => {
  if (connectionMongo.models[name]) return connectionMongo.models[name] as Model<T>;
  addCommonMiddleware(schema);

  const model = connectionMongo.model<T>(name, schema);

  if (process.env.SYNC_INDEX !== '0') {
    try {
      model.syncIndexes({ background: true });
    } catch (error) {
      addLog.error('Create index error', error);
    }
  }

  return model;
};

export const ReadPreference = connectionMongo.mongo.ReadPreference;
