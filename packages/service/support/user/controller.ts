import { UserType } from '@fastgpt/global/support/user/type';
import { MongoUser } from './schema';
import { getTmbInfoByTmbId, getUserDefaultTeam, createDefaultTeam } from './team/controller';
import { ERROR_ENUM } from '@fastgpt/global/common/error/errorCode';
import { PublishedErrEnum } from '@fastgpt/global/common/error/code/published';
import { mongoSessionRun } from '../../common/mongo/sessionRun';
import { PRICE_SCALE } from '@fastgpt/global/support/wallet/constants';
import axios from 'axios';

export async function getFofProUserData({ token }: { token: string }): Promise<{
  userId: number;
  username: string;
  nickName: string;
  fofproApps: Array<string>;
}> {
  const options = {
    method: 'GET',
    headers: {
      'content-type': 'application/json',
      Authorization: token
    },
    url: `${process.env.FOFPRO_HOST}/api/papillon/pro/user/0`
  };
  const { data: result } = await axios(options);
  if (result.code !== 200) {
    return Promise.reject(new Error(PublishedErrEnum.fofProTokenInvalid));
  }
  const proUser = result.data.proUser || result.data;
  let fofproApps = [];
  try {
    fofproApps = JSON.parse(proUser.fofProApp);
  } catch (error) {}
  return {
    userId: proUser.userId,
    nickName: proUser.nickName,
    username: proUser.userName,
    fofproApps
  };
}

export async function createFofproUser(userData: {
  username: string;
  nickName: string;
  fofproUserId: number;
}): Promise<string> {
  const result = await mongoSessionRun(async (session) => {
    const [user] = await MongoUser.create([userData], { session });
    const rootId = user._id;
    await createDefaultTeam({ userId: rootId, balance: 9999 * PRICE_SCALE, session });
    return user;
  });
  return String(result._id);
}

export async function authUserExist({ userId, username }: { userId?: string; username?: string }) {
  if (userId) {
    return MongoUser.findOne({ _id: userId });
  }
  if (username) {
    return MongoUser.findOne({ username });
  }
  return null;
}

export async function getUserDetail({
  tmbId,
  userId
}: {
  tmbId?: string;
  userId?: string;
}): Promise<UserType> {
  const tmb = await (async () => {
    if (tmbId) {
      try {
        const result = await getTmbInfoByTmbId({ tmbId });
        return result;
      } catch (error) {}
    }
    if (userId) {
      return getUserDefaultTeam({ userId });
    }
    return Promise.reject(ERROR_ENUM.unAuthorization);
  })();
  const user = await MongoUser.findById(tmb.userId);

  if (!user) {
    return Promise.reject(ERROR_ENUM.unAuthorization);
  }

  return {
    _id: user._id,
    username: user.username,
    avatar: user.avatar,
    timezone: user.timezone,
    promotionRate: user.promotionRate,
    openaiAccount: user.openaiAccount,
    team: tmb,
    notificationAccount: tmb.notificationAccount,
    permission: tmb.permission
  };
}
