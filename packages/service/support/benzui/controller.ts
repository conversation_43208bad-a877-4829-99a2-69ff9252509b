import axios from 'axios';

// 创建axios实例
const benzuiApi = axios.create({
  baseURL: `${process.env.BENZUICHAT_HOST}/bbs`,
  headers: {
    'Content-Type': 'application/json'
  }
});

export async function updateAppInfoBenzui(params: {
  agentId: string;
  avatar: string;
  intro: string;
  name: string;
}): Promise<boolean> {
  try {
    const { data } = await benzuiApi.post('/app/towa/save', params);

    if (data.code === 0) {
      console.log('更新benzui成功');
      return data.result;
    } else {
      //   throw new Error(data.msg || '更新失败');
      console.error('更新benzui失败:', data.msg);
      return false;
    }
  } catch (error) {
    if (axios.isAxiosError(error)) {
      console.error('更新benzui失败:', error.response?.data || error.message);
    } else {
      console.error('更新benzui失败:', error);
    }
    throw error;
  }
}

/**
 * 发布Agent到benzui平台
 */
export async function publishToBenzui(params: {
  agentId: string;
  apiKey: string;
  avatar: string;
  intro: string;
  name: string;
  unionid: string;
}): Promise<boolean> {
  try {
    const { data } = await benzuiApi.post('/app/towa/save', params);

    if (data.code === 0) {
      console.log('发布到benzui成功');
      return data.result;
    } else {
      throw new Error(data.msg || '发布失败');
    }
  } catch (error) {
    if (axios.isAxiosError(error)) {
      console.error('发布到benzui失败:', error.response?.data || error.message);
    } else {
      console.error('发布到benzui失败:', error);
    }
    throw error;
  }
}

/**
 * 从benzui平台删除已发布的Agent
 */
export async function removeFromBenzui(agentId: string): Promise<boolean> {
  try {
    const { data } = await benzuiApi.delete('/app/towa/remove', {
      params: { agentId }
    });

    if (data.code === 0) {
      console.log('从benzui删除成功');
      return data.result;
    } else {
      throw new Error(data.msg || '删除失败');
    }
  } catch (error) {
    if (axios.isAxiosError(error)) {
      console.error('从benzui删除失败:', error.response?.data || error.message);
    } else {
      console.error('从benzui删除失败:', error);
    }
    throw error;
  }
}
