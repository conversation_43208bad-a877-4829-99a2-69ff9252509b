import { AppCollectionName } from '../../../core/app/schema';
import { connectionMongo, getMongoModel, type Model } from '../../../common/mongo';
import { MongoHtmlPageSchemaType } from '@fastgpt/global/support/pluginData/html/type';
const { Schema, model, models } = connectionMongo;

const HtmlPageSchema = new Schema({
  appId: {
    type: Schema.Types.ObjectId,
    ref: AppCollectionName,
    required: true
  },
  createTime: {
    type: Date,
    default: () => new Date()
  },
  expiredTime: {
    type: Date
  },
  content: {
    type: String
  }
});

try {
  // tts expired
  HtmlPageSchema.index({ createTime: 1 });
} catch (error) {
  console.log(error);
}

export const MongoHtmlPage = getMongoModel<MongoHtmlPageSchemaType>('htmlpage', HtmlPageSchema);
