import { GenHtmlPageProps } from '@fastgpt/global/support/pluginData/html/type';
import { MongoHtmlPage } from './schema';

export function getHtmlPageUrl(id: string) {
  return `${process.env.TOWA_HOST}/api/system/htmlpage/${id}`;
}

export const maxImgSize = 1024 * 1024 * 12;

export async function genHtmlPage({
  appId,
  content,
  expiredTime
}: GenHtmlPageProps & {
  appId?: string;
}) {
  if (content.length > maxImgSize) {
    return Promise.reject('Content too large');
  }

  const { _id } = await MongoHtmlPage.create({
    appId,
    content,
    expiredTime
  });

  return getHtmlPageUrl(String(_id));
}

export async function readMongoHtmlPageData({ id }: { id: string }) {
  const formatId = id.replace(/\.[^/.]+$/, '');

  const data = await MongoHtmlPage.findById(formatId);
  if (!data) {
    return Promise.reject('Page not found');
  }

  return {
    content: data.content
  };
}
