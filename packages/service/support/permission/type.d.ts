import { Permission } from '@fastgpt/global/support/permission/controller';
import { ApiRequestProps } from '../../type/next';
import type { PermissionValueType } from '@fastgpt/global/support/permission/type';

export type ReqHeaderAuthType = {
  cookie?: string;
  token?: string;
  apikey?: string; // abandon
  rootkey?: string;
  userid?: string;
  authorization?: string;
  fofprorootkey?: string;
  fofprotoken?: string;
  towapluginapitoken?: string;
  appid?: string;
};

type RequireAtLeastOne<T, Keys extends keyof T = keyof T> = Omit<T, Keys> &
  {
    [K in Keys]-?: Required<Pick<T, K>> & Partial<Omit<T, K>>;
  }[Keys];

type authModeType = {
  req: ApiRequestProps;
  authToken?: boolean;
  authRoot?: boolean;
  authApiKey?: boolean;
  // per?: PermissionValueType | 'r' | 'w' | 'owner'; // this is for compatibility
  authFofproRoot?: boolean;
  authFofProToken?: boolean;
  authPluginApiToken?: boolean;
  per?: PermissionValueType;
};

export type AuthModeType = RequireAtLeastOne<
  authModeType,
  'authApiKey' | 'authRoot' | 'authToken' | 'authFofproRoot' | 'authPluginApiToken'
>;

export type AuthResponseType<T extends Permission = Permission> = {
  teamId: string;
  tmbId: string;
  authType?: `${AuthUserTypeEnum}`;
  appId?: string;
  apikey?: string;
  permission: T;
  userId?: string;
};
