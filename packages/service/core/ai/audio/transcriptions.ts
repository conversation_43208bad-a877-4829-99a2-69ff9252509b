import fs from 'fs';
import { getAxiosConfig } from '../config';
import axios from 'axios';
import FormData from 'form-data';

export const aiTranscriptions = async ({
  model,
  fileStream
}: {
  model: string;
  fileStream: fs.ReadStream;
}) => {
  const data = new FormData();
  data.append('model', model);
  data.append('file', fileStream);
  // data.append(
  //   'prompt',
  //   '请将音频中的内容翻译成目标语言，并保持音频的原始内容，不要添加任何解释。如果识别到语言为中文，请使用简体中文，不要使用繁体中文。'
  // );

  const aiAxiosConfig = getAxiosConfig();
  const { data: result } = await axios<{ text: string }>({
    method: 'post',
    baseURL: aiAxiosConfig.baseUrl,
    url: '/audio/transcriptions',
    headers: {
      Authorization: aiAxiosConfig.authorization,
      ...data.getHeaders()
    },
    data: data
  });

  return result;
};
