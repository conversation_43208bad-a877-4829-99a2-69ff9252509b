export type PromptVariableType = 'string' | 'number' | 'boolean';

export interface PromptVariable {
  name: string;
  description?: string;
  type: PromptVariableType;
  required: boolean;
}

export interface CreatePromptTemplateParams {
  userId: string;
  teamId?: string;
  title: string;
  description?: string;
  content: string;
  tags?: string[];
  variables?: PromptVariable[];
  isPublic?: boolean;
}

export interface UpdatePromptTemplateParams {
  title?: string;
  description?: string;
  content?: string;
  tags?: string[];
  variables?: PromptVariable[];
  isPublic?: boolean;
}

export interface QueryPromptTemplateParams {
  userId?: string;
  teamId?: string;
  tags?: string[];
  isPublic?: boolean;
  pageSize?: number;
  pageNum?: number;
}
