import { getMongoModel, Schema } from '../../common/mongo';
import {
  TeamCollectionName,
  TeamMemberCollectionName
} from '@fastgpt/global/support/user/team/constant';
import { ReadPermissionVal, WritePermissionVal } from '@fastgpt/global/support/permission/constant';
import { getPermissionSchema } from '@fastgpt/global/support/permission/utils';

export const PromptTemplateCollectionName = 'prompt_templates';
export const PromptItemCollectionName = 'prompt_items';

// 提示词模板 Schema
const PromptTemplateSchema = new Schema(
  {
    teamId: {
      type: Schema.Types.ObjectId,
      ref: TeamCollectionName,
      required: true
    },
    tmbId: {
      type: Schema.Types.ObjectId,
      ref: TeamMemberCollectionName,
      required: true
    },
    title: {
      type: String,
      required: true
    },
    description: {
      type: String,
      default: ''
    },
    ...getPermissionSchema(ReadPermissionVal)
  },
  {
    timestamps: true
  }
);

// 提示词项 Schema
const PromptItemSchema = new Schema(
  {
    templateId: {
      type: Schema.Types.ObjectId,
      ref: PromptTemplateCollectionName,
      required: true
    },
    teamId: {
      type: Schema.Types.ObjectId,
      ref: TeamCollectionName,
      required: true
    },
    tmbId: {
      type: Schema.Types.ObjectId,
      ref: TeamMemberCollectionName,
      required: true
    },
    title: {
      type: String,
      required: true
    },
    content: {
      type: String,
      required: true
    },
    ...getPermissionSchema(WritePermissionVal)
  },
  {
    timestamps: true
  }
);

try {
  PromptTemplateSchema.index({ teamId: 1, tmbId: 1 });
  PromptTemplateSchema.index({ title: 1 });

  PromptItemSchema.index({ templateId: 1 });
  PromptItemSchema.index({ teamId: 1, tmbId: 1 });
  PromptItemSchema.index({ title: 1 });
} catch (error) {
  console.log(error);
}

export const MongoPromptTemplate = getMongoModel(
  PromptTemplateCollectionName,
  PromptTemplateSchema
);

export const MongoPromptItem = getMongoModel(PromptItemCollectionName, PromptItemSchema);
