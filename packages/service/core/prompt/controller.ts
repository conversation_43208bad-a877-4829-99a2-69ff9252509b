import { PromptTemplateModel, PromptContentModel, PromptTemplate, PromptContent } from './schema';
import { Types } from 'mongoose';

export class PromptTemplateController {
  /**
   * 创建提示词模版
   */
  async create({
    template,
    contents
  }: {
    template: Partial<PromptTemplate>;
    contents: Omit<PromptContent, 'templateId' | 'createTime' | 'updateTime'>[];
  }) {
    const session = await PromptTemplateModel.startSession();
    session.startTransaction();

    try {
      // 创建模版
      const newTemplate = await PromptTemplateModel.create([template], { session });

      // 创建提示词内容
      const contentsToCreate = contents.map((content) => ({
        ...content,
        templateId: newTemplate[0]._id
      }));
      await PromptContentModel.create(contentsToCreate, { session });

      await session.commitTransaction();
      return newTemplate[0];
    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  }

  /**
   * 更新提示词模版
   */
  async update(
    id: string,
    {
      template,
      contents
    }: {
      template?: Partial<PromptTemplate>;
      contents?: {
        id?: string;
        data: Partial<PromptContent>;
      }[];
    }
  ) {
    const session = await PromptTemplateModel.startSession();
    session.startTransaction();

    try {
      // 更新模版基本信息
      if (template) {
        await PromptTemplateModel.findByIdAndUpdate(
          id,
          { ...template, updateTime: new Date() },
          { session }
        );
      }

      // 更新提示词内容
      if (contents) {
        for (const content of contents) {
          if (content.id) {
            // 更新已有内容
            await PromptContentModel.findByIdAndUpdate(
              content.id,
              { ...content.data, updateTime: new Date() },
              { session }
            );
          } else {
            // 创建新内容
            await PromptContentModel.create(
              [
                {
                  ...content.data,
                  templateId: id
                }
              ],
              { session }
            );
          }
        }
      }

      await session.commitTransaction();
      return this.getById(id);
    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  }

  /**
   * 删除提示词模版
   */
  async delete(id: string) {
    const session = await PromptTemplateModel.startSession();
    session.startTransaction();

    try {
      // 删除模版
      await PromptTemplateModel.findByIdAndDelete(id, { session });
      // 删除相关的提示词内容
      await PromptContentModel.deleteMany({ templateId: id }, { session });

      await session.commitTransaction();
      return true;
    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  }

  /**
   * 获取提示词模版列表
   */
  async getList({
    userId,
    teamId,
    tags,
    type,
    isPublic,
    pageSize = 10,
    pageNum = 1
  }: {
    userId?: string;
    teamId?: string;
    tags?: string[];
    type?: 'simple' | 'workflow';
    isPublic?: boolean;
    pageSize?: number;
    pageNum?: number;
  }) {
    const query: any = {};

    if (userId) query.userId = userId;
    if (teamId) query.teamId = teamId;
    if (tags?.length) query.tags = { $in: tags };
    if (type) query.type = type;
    if (typeof isPublic === 'boolean') query.isPublic = isPublic;

    const total = await PromptTemplateModel.countDocuments(query);
    const list = await PromptTemplateModel.find(query)
      .sort({ updateTime: -1 })
      .skip((pageNum - 1) * pageSize)
      .limit(pageSize);

    return {
      pageNum,
      pageSize,
      total,
      list
    };
  }

  /**
   * 获取提示词模版详情
   */
  async getById(id: string) {
    const template = await PromptTemplateModel.findById(id);
    if (!template) return null;

    const contents = await PromptContentModel.find({ templateId: id }).sort({ order: 1 });

    return {
      ...template.toObject(),
      contents
    };
  }
}

export const promptTemplateController = new PromptTemplateController();
