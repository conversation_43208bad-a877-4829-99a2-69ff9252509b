{"name": "@fastgpt/service", "version": "1.0.0", "dependencies": {"@fastgpt/global": "workspace:*", "@node-rs/jieba": "1.10.0", "@xmldom/xmldom": "^0.8.10", "@zilliz/milvus2-sdk-node": "2.4.2", "axios": "^1.5.1", "chalk": "^5.3.0", "cheerio": "1.0.0-rc.12", "cookie": "^0.5.0", "date-fns": "2.30.0", "dayjs": "^1.11.7", "decompress": "^4.2.1", "domino-ext": "^2.1.4", "encoding": "^0.1.13", "file-type": "^19.0.0", "form-data": "^4.0.0", "iconv-lite": "^0.6.3", "joplin-turndown-plugin-gfm": "^1.0.12", "json5": "^2.2.3", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "mammoth": "^1.6.0", "mongoose": "^7.0.2", "multer": "1.4.5-lts.1", "next": "14.2.5", "nextjs-cors": "^2.2.0", "node-cron": "^3.0.3", "node-xlsx": "^0.24.0", "papaparse": "5.4.1", "pdfjs-dist": "4.4.168", "pg": "^8.10.0", "tiktoken": "^1.0.15", "tunnel": "^0.0.6", "turndown": "^7.1.2"}, "devDependencies": {"@types/cookie": "^0.5.2", "@types/decompress": "^4.2.7", "@types/jsonwebtoken": "^9.0.3", "@types/lodash": "^4.14.191", "@types/multer": "^1.4.10", "@types/node-cron": "^3.0.11", "@types/papaparse": "5.3.7", "@types/pg": "^8.6.6", "@types/tunnel": "^0.0.4", "@types/turndown": "^5.0.4"}}