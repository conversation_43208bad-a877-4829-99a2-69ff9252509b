import { PromptTemplateItem } from '../type.d';

export const Prompt_QuoteTemplateList: PromptTemplateItem[] = [
  {
    title: 'Standard Template',
    desc: 'Standard prompt template for knowledge bases with flexible structure.',
    value: `{{q}}
{{a}}`
  },
  {
    title: 'Q&A Template',
    desc: 'Suitable for QA-structured knowledge bases, allows AI to answer more strictly according to preset content',
    value: `<Question>
{{q}}
</Question>
<Answer>
{{a}}
</Answer>`
  },
  {
    title: 'Strict Standard Template',
    desc: 'Based on the standard template, with stricter requirements for model responses.',
    value: `{{q}}
{{a}}`
  },
  {
    title: 'Strict Q&A Template',
    desc: 'Based on the Q&A template, with stricter requirements for model responses.',
    value: `<Question>
{{q}}
</Question>
<Answer>
{{a}}
</Answer>`
  }
];

export const Prompt_QuotePromptList: PromptTemplateItem[] = [
  {
    title: 'Standard Template',
    desc: '',
    value: `Use the content within <Data></Data> tags as your knowledge:

<Data>
{{quote}}
</Data>

Answer requirements:
- If you are not clear about the answer, you need to clarify.
- Avoid mentioning that you obtained knowledge from <Data></Data>.
- Keep answers consistent with what is described in <Data></Data>.
- Use Markdown syntax to optimize answer format.
- Answer in the same language as the question.

Question:"""{{question}}"""`
  },
  {
    title: 'Q&A Template',
    desc: '',
    value: `Use the Q&A pairs within <QA></QA> tags to answer.

<QA>
{{quote}}
</QA>

Answer requirements:
- Select one or more Q&A pairs to answer.
- The answer content should be as consistent as possible with the content in <Answer></Answer>.
- If there are no relevant Q&A pairs, you need to clarify.
- Avoid mentioning that you obtained knowledge from QA, just reply with the answer.

Question:"""{{question}}"""`
  },
  {
    title: 'Strict Standard Template',
    desc: '',
    value: `Forget your existing knowledge and only use the content within <Data></Data> tags as your knowledge:

<Data>
{{quote}}
</Data>

Thinking process:
1. Determine if the question is related to the content in <Data></Data> tags.
2. If related, answer according to the requirements below.
3. If unrelated, directly refuse to answer this question.

Answer requirements:
- Avoid mentioning that you obtained knowledge from <Data></Data>.
- Keep answers consistent with what is described in <Data></Data>.
- Use Markdown syntax to optimize answer format.
- Answer in the same language as the question.

Question:"""{{question}}"""`
  },
  {
    title: 'Strict Q&A Template',
    desc: '',
    value: `Forget your existing knowledge and only use the Q&A pairs within <QA></QA> tags to answer.

<QA>
{{quote}}
</QA>

Thinking process:
1. Determine if the question is related to the content in <QA></QA> tags.
2. If unrelated, directly refuse to answer this question.
3. Determine if there are similar or identical questions.
4. If there are identical questions, directly output the corresponding answer.
5. If there are only similar questions, please output the similar questions and answers together.

Answer requirements:
- If there are no relevant Q&A pairs, you need to clarify.
- The answer content should be as consistent as possible with the content in <QA></QA> tags.
- Avoid mentioning that you obtained knowledge from QA, just reply with the answer.
- Use Markdown syntax to optimize answer format.
- Answer in the same language as the question.

Question:"""{{question}}"""`
  }
];

// Document quote prompt
export const Prompt_DocumentQuote = `Use the content within <Quote></Quote> tags as your knowledge:
<Quote>
{{quote}}
</Quote>
`;
