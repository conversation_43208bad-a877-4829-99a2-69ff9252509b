import {
  FlowNodeInputTypeEnum,
  FlowNodeOutputTypeEnum,
  FlowNodeTypeEnum
} from '../../../node/constant';
import { FlowNodeTemplateType } from '../../../type/node.d';
import {
  WorkflowIOValueTypeEnum,
  NodeInputKeyEnum,
  NodeOutputKeyEnum,
  FlowNodeTemplateTypeEnum
} from '../../../constants';
import { Input_Template_History, Input_Template_UserChatInput } from '../../input';
import { getHandleConfig } from '../../utils';

export const RunAppModule: FlowNodeTemplateType = {
  id: FlowNodeTypeEnum.runApp,
  templateType: FlowNodeTemplateTypeEnum.tools,
  flowNodeType: FlowNodeTypeEnum.runApp,
  sourceHandle: getHandleConfig(true, true, true, true),
  targetHandle: getHandleConfig(true, true, true, true),
  avatar: 'core/workflow/template/runApp',
  name: 'workflow:template.runApp.label',
  intro: 'workflow:template.runApp.description',
  showStatus: true,
  version: '481',
  isTool: true,
  inputs: [
    {
      key: NodeInputKeyEnum.runAppSelectApp,
      renderTypeList: [FlowNodeInputTypeEnum.selectApp, FlowNodeInputTypeEnum.reference],
      valueType: WorkflowIOValueTypeEnum.selectApp,
      label: 'workflow:template.runApp.runAppSelectApp.label',
      description: 'workflow:template.runApp.runAppSelectApp.description',
      required: true
    },
    Input_Template_History,
    {
      ...Input_Template_UserChatInput,
      toolDescription: 'workflow:template.runApp.userChatInput.description'
    }
  ],
  outputs: [
    {
      id: NodeOutputKeyEnum.history,
      key: NodeOutputKeyEnum.history,
      label: 'workflow:template.runApp.history.label',
      description: 'workflow:template.runApp.history.description',
      valueType: WorkflowIOValueTypeEnum.chatHistory,
      required: true,
      type: FlowNodeOutputTypeEnum.static
    },
    {
      id: NodeOutputKeyEnum.answerText,
      key: NodeOutputKeyEnum.answerText,
      label: 'workflow:template.runApp.answerText.label',
      description: 'workflow:template.runApp.answerText.description',
      valueType: WorkflowIOValueTypeEnum.string,
      type: FlowNodeOutputTypeEnum.static
    }
  ]
};
