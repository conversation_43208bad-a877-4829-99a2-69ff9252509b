import {
  FlowNodeTemplateTypeEnum,
  NodeInputKeyEnum,
  NodeOutputKeyEnum,
  WorkflowIOValueTypeEnum
} from '../../../constants';
import {
  FlowNodeInputTypeEnum,
  FlowNodeOutputTypeEnum,
  FlowNodeTypeEnum
} from '../../../node/constant';
import { FlowNodeTemplateType } from '../../../type/node';
import { getHandleConfig } from '../../utils';
import { Input_Template_DynamicInput } from '../../input';
import { Output_Template_AddOutput } from '../../output';
import { JS_TEMPLATE } from './constants';

export const CodeNode: FlowNodeTemplateType = {
  id: FlowNodeTypeEnum.code,
  templateType: FlowNodeTemplateTypeEnum.tools,
  flowNodeType: FlowNodeTypeEnum.code,
  sourceHandle: getHandleConfig(true, true, true, true),
  targetHandle: getHandleConfig(true, true, true, true),
  avatar: 'core/workflow/template/codeRun',
  name: 'workflow:template.codeNode.label',
  intro: 'workflow:template.codeNode.description',
  showStatus: true,
  version: '482',
  inputs: [
    {
      ...Input_Template_DynamicInput,
      description: 'workflow:template.codeNode.addInputParam.description',
      customInputConfig: {
        selectValueTypeList: Object.values(WorkflowIOValueTypeEnum),
        showDescription: false,
        showDefaultValue: true
      }
    },
    {
      renderTypeList: [FlowNodeInputTypeEnum.reference],
      valueType: WorkflowIOValueTypeEnum.string,
      canEdit: true,
      key: 'data1',
      label: 'data1',
      customInputConfig: {
        selectValueTypeList: Object.values(WorkflowIOValueTypeEnum),
        showDescription: false,
        showDefaultValue: true
      },
      required: true
    },
    {
      renderTypeList: [FlowNodeInputTypeEnum.reference],
      valueType: WorkflowIOValueTypeEnum.string,
      canEdit: true,
      key: 'data2',
      label: 'data2',
      customInputConfig: {
        selectValueTypeList: Object.values(WorkflowIOValueTypeEnum),
        showDescription: false,
        showDefaultValue: true
      },
      required: true
    },
    {
      key: NodeInputKeyEnum.codeType,
      renderTypeList: [FlowNodeInputTypeEnum.hidden],
      label: '',
      value: 'js'
    },
    {
      key: NodeInputKeyEnum.code,
      renderTypeList: [FlowNodeInputTypeEnum.custom],
      label: '',
      value: JS_TEMPLATE
    }
  ],
  outputs: [
    {
      ...Output_Template_AddOutput,
      description: 'workflow:template.codeNode.addOutputParam.description'
    },
    {
      id: NodeOutputKeyEnum.rawResponse,
      key: NodeOutputKeyEnum.rawResponse,
      label: 'workflow:template.codeNode.rawResponse.label',
      valueType: WorkflowIOValueTypeEnum.object,
      type: FlowNodeOutputTypeEnum.static
    },
    {
      id: NodeOutputKeyEnum.error,
      key: NodeOutputKeyEnum.error,
      label: 'workflow:template.codeNode.error.label',
      description: 'workflow:template.codeNode.error.description',
      valueType: WorkflowIOValueTypeEnum.object,
      type: FlowNodeOutputTypeEnum.static
    },
    {
      id: 'qLUQfhG0ILRX',
      type: FlowNodeOutputTypeEnum.dynamic,
      key: 'result',
      valueType: WorkflowIOValueTypeEnum.string,
      label: 'result'
    },
    {
      id: 'gR0mkQpJ4Og8',
      type: FlowNodeOutputTypeEnum.dynamic,
      key: 'data2',
      valueType: WorkflowIOValueTypeEnum.string,
      label: 'data2'
    }
  ]
};
