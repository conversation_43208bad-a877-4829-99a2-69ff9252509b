import {
  FlowNodeInputTypeEnum,
  FlowNodeOutputTypeEnum,
  FlowNodeTypeEnum
} from '../../../node/constant';
import { FlowNodeTemplateType } from '../../../type/node';
import {
  WorkflowIOValueTypeEnum,
  NodeInputKeyEnum,
  FlowNodeTemplateTypeEnum,
  NodeOutputKeyEnum
} from '../../../constants';
import {
  Input_Template_SelectAIModel,
  Input_Template_History,
  Input_Template_UserChatInput
} from '../../input';
import { Input_Template_System_Prompt } from '../../input';
import { LLMModelTypeEnum } from '../../../../ai/constants';
import { getHandleConfig } from '../../utils';

export const ClassifyQuestionModule: FlowNodeTemplateType = {
  id: FlowNodeTypeEnum.classifyQuestion,
  templateType: FlowNodeTemplateTypeEnum.ai,
  flowNodeType: FlowNodeTypeEnum.classifyQuestion,
  sourceHandle: getHandleConfig(false, false, false, false),
  targetHandle: getHandleConfig(true, false, true, true),
  avatar: 'core/workflow/template/questionClassify',
  name: 'workflow:template.classify_question',
  intro: 'workflow:template.classify_question_intro',
  showStatus: true,
  version: '481',
  inputs: [
    {
      ...Input_Template_SelectAIModel,
      llmModelType: LLMModelTypeEnum.all
    },
    {
      ...Input_Template_System_Prompt,
      label: 'core.module.input.label.Background',
      description: 'core.module.input.description.Background',
      placeholder: 'core.module.input.placeholder.Classify background'
    },
    Input_Template_History,
    Input_Template_UserChatInput,
    {
      key: NodeInputKeyEnum.agents,
      renderTypeList: [FlowNodeInputTypeEnum.custom],
      valueType: WorkflowIOValueTypeEnum.any,
      label: '',
      value: [
        {
          value: 'workflow:template.classify_question_option1',
          key: 'wqre'
        },
        {
          value: 'workflow:template.classify_question_option2',
          key: 'sdfa'
        },
        {
          value: 'workflow:template.classify_question_option3',
          key: 'poiu'
        },
        {
          value: 'workflow:template.classify_question_option4',
          key: 'agex'
        }
      ]
    }
  ],
  outputs: [
    {
      id: NodeOutputKeyEnum.cqResult,
      key: NodeOutputKeyEnum.cqResult,
      required: true,
      label: 'workflow:template.classify_question_result',
      valueType: WorkflowIOValueTypeEnum.string,
      type: FlowNodeOutputTypeEnum.static
    }
  ]
};
