import { ErrType } from '../errorCode';

/* dataset: 510000 */
export enum PublishedErrEnum {
  fofProTokenInvalid = 'fofProTokenInvalid',
  notFofProUser = 'notFofProUser'
}
const errList = [
  {
    statusText: PublishedErrEnum.fofProTokenInvalid,
    message: 'FOFPro Token 无效'
  },
  {
    statusText: PublishedErrEnum.notFofProUser,
    message: '没有权限登录 FOFPro'
  }
];
export default errList.reduce((acc, cur, index) => {
  return {
    ...acc,
    [cur.statusText]: {
      code: 510000 + index,
      statusText: cur.statusText,
      message: cur.message,
      data: null
    }
  };
}, {} as ErrType<`${PublishedErrEnum}`>);
