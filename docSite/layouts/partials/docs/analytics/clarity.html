<!-- Microsoft Clarity -->
<script defer>
  function loadClarityScript() {
    (function(c,l,a,r,i,t,y){
      c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
      t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
      y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
    })(window, document, "clarity", "script", "mjgug8s1pf");

    window.removeEventListener('scroll', onScrollLoadClarity);
  }

  function onScrollLoadClarity() {
    if (window.scrollY > 100) {
      loadClarityScript();
    }
  }

  window.addEventListener('scroll', onScrollLoadClarity);
</script>