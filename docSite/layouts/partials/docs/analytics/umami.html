{{ if isset site.Params.umamiAnalytics "domain" }}
<script data-id="umami-script" async src="https://{{ site.Params.umamiAnalytics.domain }}/{{ site.Params.umamiAnalytics.scriptName }}" data-website-id="{{ site.Params.umamiAnalytics.websiteid }}">
</script>
{{ else }}
<script data-id="umami-script" async src="https://analytics.umami.is/script.js" data-website-id="{{ site.Params.umamiAnalytics.websiteid }}">
</script>
{{ end }}

<script type="text/javascript">
    document.querySelector('script[data-id="umami-script"]').addEventListener('load', function () {
        const type = document.head.querySelector('meta[property = "og:type"]').getAttribute('content');
        let title = document.head.querySelector('meta[property = "og:title"]').getAttribute('content');
        let url = document.head.querySelector('meta[property = "og:url"]').getAttribute('content');
        umami.track(type + ':' + title, {'url': url});
    });
</script>