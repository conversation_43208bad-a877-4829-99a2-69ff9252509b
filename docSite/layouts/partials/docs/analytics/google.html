<!-- Google tag (gtag.js) -->
<script defer>
  function loadAnalyticsScript() {
    var script = document.createElement('script');
    script.async = true;
    script.src = 'https://gaxy.fastgpt.in/gtag/js?id=G-KGYF44Y7TT';
    document.head.appendChild(script);

    window.dataLayer = window.dataLayer || [];
    function gtag() {
      dataLayer.push(arguments);
    }
    gtag('js', new Date());
    gtag('config', 'G-KGYF44Y7TT');

    window.removeEventListener('scroll', onScrollLoadAnalytics);
  }

  function onScrollLoadAnalytics() {
    if (window.scrollY > 100) {
      loadAnalyticsScript();
    }
  }

  window.addEventListener('scroll', onScrollLoadAnalytics);
</script>