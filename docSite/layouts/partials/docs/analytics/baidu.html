<!-- baid<PERSON>ngji -->
<script defer>
  function loadScriptOnScroll() {
    var scrollPosition = window.scrollY || window.pageYOffset;

    if (scrollPosition > 100) {
      var _hmt = _hmt || [];
      var hm = document.createElement("script");
      hm.src = "https://hm.baidu.com/hm.js?537c7867df8f9ae2e7147284ec0dbfdd";
      var s = document.getElementsByTagName("script")[0]; 
      s.parentNode.insertBefore(hm, s);

      window.removeEventListener('scroll', loadScriptOnScroll);
    }
  }

  window.addEventListener('scroll', loadScriptOnScroll);
</script>