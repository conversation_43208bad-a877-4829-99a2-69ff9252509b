baseURL = "https://doc.fastgpt.in"
languageCode = "en-GB"
contentDir = "content"
enableEmoji = true
enableGitInfo = false # N.B. .GitInfo does not currently function with git submodule content directories

defaultContentLanguage = 'zh-cn'
defaultContentLanguageInSubdir = false
[languages]
  [languages.zh-cn]
    title = "FastGPT"
    languageName = "简体中文"
    contentDir = "content/zh-cn"
    weight = 10
  [languages.en]
    title = "FastGPT Docs"
    languageName = "English"
    contentDir = "content/en"
    weight = 10
    disabled = true

[build]
  noJSConfigInAssets = true

[module]
  [module.hugoVersion]
    extended = true
    min = "0.100.0"
  [[module.imports]]
    path = "github.com/colinwilson/lotusdocs"
    disable = false
  [[module.imports]]
    path = "github.com/gohugoio/hugo-mod-bootstrap-scss/v5"
    disable = false

[markup]
  defaultMarkdownHandler = "goldmark"
  [markup.tableOfContents]
      endLevel = 3
      startLevel = 1
  [markup.goldmark]
    [markup.goldmark.renderer]
      unsafe = true # https://jdhao.github.io/2019/12/29/hugo_html_not_shown/
  # [markup.highlight]
  #   codeFences = false # disables Hugo's default syntax highlighting
  # [markup.goldmark.parser]
  #   [markup.goldmark.parser.attribute]
  #     block = true
  #     title = true

[params]

  google_fonts = [
    ["Inter", "300, 400, 600, 700"],
    ["Fira Code", "500, 700"],
    ["JetBrains Mono", "500, 700"]
  ]

  #sans_serif_font = "LXGW WenKai Screen"     # Default is System font
  #secondary_font  = "LXGW WenKai Screen"     # Default is System font
  mono_font       = "JetBrains Mono" # Default is System font

    [params.footer]
        copyright = "© :YEAR: the FastGPT Authors."
        version = false # includes git commit info

    [params.social]
        github = "labring/FastGPT"        # YOUR_GITHUB_ID or YOUR_GITHUB_URL
        # twitter = ""       # YOUR_TWITTER_ID
        # instagram = "colinwilson"     # YOUR_INSTAGRAM_ID
        # rss = true                    # show rss icon with link
        wechat = "https://oss.laf.run/htr4n1-images/fastgpt-qr-code.jpg"

    [params.docs] # Parameters for the /docs 'template'
        title           = ""           # default html title for documentation pages/sections

        # pathName        = "docs"                            # path name for documentation site | default "docs"

        # themeColor      = "blue"                            # (optional) - Set theme accent colour. Options include: blue (default), green, red, yellow, emerald, cardinal, magenta, cyan

        darkMode        = true                                # enable dark mode option? default false

        prism           = true                                # enable syntax highlighting via Prism

        prismTheme      = "lotusdocs"                           # (optional) - Set theme for PrismJS. Options include: lotusdocs (default), solarized-light, twilight, lucario

        # gitinfo
        repoURL         = "https://github.com/labring/FastGPT"  # Git repository URL for your site
        repoBranch      = "main"                           # Name of your Git repository branch
        editPage        = true                                # enable 'Edit this page' feature - default false
        lastMod         = false                                # enable 'Last modified' date on pages - default false
        lastModRelative = true                                # format 'Last modified' time as relative - default true

        sidebarIcons    = true                                # enable sidebar icons? default false
        breadcrumbs     = true                                # default is true
        backToTop       = true                                # enable back-to-top button? default true

        # ToC
        toc             = true                                # enable table of contents? default is true
        tocMobile       = true                                # enable table of contents in mobile view? default is true
        scrollSpy       = true                                # enable scrollspy on ToC? default is true

        # front matter
        descriptions    = true                                # enable front matter descriptions under content title?
        titleIcon       = true                                # enable front matter icon title prefix? default is false

        # content navigation
        navDesc         = true                                # include front matter descriptions in Prev/Next navigation cards
        navDescTrunc    = 30                                  # Number of characters by which to truncate the Prev/Next descriptions

        listDescTrunc   = 100                                 # Number of characters by which to truncate the list card description

        # Link behaviour
        intLinkTooltip  = true                                # Enable a tooltip for internal links that displays info about the destination? default false
        # extLinkNewTab   = false                             # Open external links in a new Tab? default true
        logoLinkURL = "https://fastgpt.in/"                                    # Set a custom URL destination for the top header logo link.

    [params.flexsearch] # Parameters for FlexSearch
        # enabled             = true
        # tokenize            = "full"
        # optimize            = true
        # cache               = 100
        # minQueryChar        = 3 # default is 0 (disabled)
        # maxResult           = 5 # default is 5
        # searchSectionsIndex = []

    [params.docsearch] # Parameters for DocSearch
        appID     = "5BEWEMH0YA" # Algolia Application ID
        apiKey    = "********************************" # Algolia Search-Only API (Public) Key
        indexName = "fastgpt" # Index Name to perform search on (or set env variable HUGO_PARAM_DOCSEARCH_indexName)

    [params.analytics] # Parameters for Analytics (Google, Plausible)
        # plausibleURL    = "/docs/s" # (or set via env variable HUGO_PARAM_ANALYTICS_plausibleURL)
        # plausibleAPI    = "/docs/s" # optional - (or set via env variable HUGO_PARAM_ANALYTICS_plausibleAPI)
        # plausibleDomain = "lotusdocs.dev"      # (or set via env variable HUGO_PARAM_ANALYTICS_plausibleDomain)

    [params.feedback]
        # enabled = true
        # analytics = "plausible"
        # positiveEventName = "Positive Feedback"
        # negativeEventName = "Negative Feedback"
        # positiveFormTitle = "What did you like?"
        # negativeFormTitle = "What went wrong?"
        # successMsg = "Thank you for helping to improve Lotus Docs' documentation!"
        # errorMsg = "Sorry! There was an error while attempting to submit your feedback!"
        # positiveForm = [
        #   ["Accurate", "Accurately describes the feature or option."],
        #   ["Solved my problem", "Helped me resolve an issue."],
        #   ["Easy to understand", "Easy to follow and comprehend."],
        #   ["Something else"]
        # ]
        # negativeForm = [
        #   ["Inaccurate", "Doesn't accurately describe the feature or option."],
        #   ["Couldn't find what I was looking for", "Missing important information."],
        #   ["Hard to understand", "Too complicated or unclear."],
        #   ["Code sample errors", "One or more code samples are incorrect."],
        #   ["Something else"]
        # ]

    [params.umamiAnalytics]
      websiteid = "69b3f2c6-39a5-4937-b92d-6fdd5c8f5958"
      domain = "umami.fastgpt.in"
      scriptName = "fastgpt"

[menu]
[[menu.primary]]
    name  = "Docs"
    url = "docs/"
    identifier = "docs"
    weight = 10

# [[menu.primary]]
#     name  = "Showcase"
#     url = "/showcase"
#     identifier = "showcase"
#     weight = 20

# [[menu.primary]]
#     name  = "Community"
#     url = "https://github.com/colinwilson/lotusdocs/discussions"
#     identifier = "community"
#     weight = 30
