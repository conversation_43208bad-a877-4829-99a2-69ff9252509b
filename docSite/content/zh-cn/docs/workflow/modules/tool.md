---
title: "工具调用"
description: "FastGPT 工具调用模块介绍"
icon: "build"
draft: false
toc: true
weight: 356
---

![](/imgs/flow-tool1.png)

## 什么是工具

工具可以是一个系统模块，例如：AI对话、知识库搜索、HTTP模块等。也可以是一个插件。

工具调用可以让 LLM 更动态的决策流程，而不都是固定的流程。（当然，缺点就是费tokens）

## 工具的组成

1. 工具介绍。通常是模块的介绍或插件的介绍，这个介绍会告诉LLM，这个工具的作用是什么。
2. 工具参数。对于系统模块来说，工具参数已经是固定的，无需额外配置。对于插件来说，工具参数是一个可配置项。

## 工具是如何运行的

要了解工具如何运行的，首先需要知道它的运行条件。

1. 需要工具的介绍（或者叫描述）。这个介绍会告诉LLM，这个工具的作用是什么，LLM会根据上下文语义，决定是否需要调用这个工具。
2. 工具的参数。有些工具调用时，可能需要一些特殊的参数。参数中有2个关键的值：`参数介绍`和`是否必须`。

结合工具的介绍、参数介绍和参数是否必须，LLM会决定是否调用这个工具。有以下几种情况：


1. 无参数的工具：直接根据工具介绍，决定是否需要执行。例如：获取当前时间。
2. 有参数的工具：
   1. 无必须的参数：尽管上下文中，没有适合的参数，也可以调用该工具。但有时候，LLM会自己伪造一个参数。
   2. 有必须的参数：如果没有适合的参数，LLM可能不会调用该工具。可以通过提示词，引导用户提供参数。

### 工具调用逻辑

在支持`函数调用`的模型中，可以一次性调用多个工具，调用逻辑如下：

![](/imgs/flow-tool2.png)

## 怎么用

| 有工具调用模块 | 无工具调用模块 |
| --- | --- |
| ![](/imgs/flow-tool3.png) | ![](/imgs/flow-tool4.png) |

高级编排中，托动工具调用的连接点，可用的工具头部会出现一个菱形，可以将它与工具调用模块底部的菱形相连接。

被连接的工具，会自动分离工具输入与普通的输入，并且可以编辑`介绍`，可以通过调整介绍，使得该工具调用时机更加精确。

关于工具调用，如何调试仍然是一个玄学，所以建议，不要一次性增加太多工具，选择少量工具调优后再进一步尝试。

## 相关示例

- [谷歌搜索](/docs/workflow/examples/google_search/)
- [发送飞书webhook](/docs/workflow/examples/feishu_webhook/)