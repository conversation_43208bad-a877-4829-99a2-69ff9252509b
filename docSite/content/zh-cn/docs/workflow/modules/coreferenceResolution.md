---
title: "问题优化（已合并到知识库搜索）"
description: "问题优化模块介绍和使用"
icon: "input"
draft: false
toc: true
weight: 364
---

## 特点

- 可重复添加
- 有外部输入
- 触发执行

![](/imgs/coreferenceResolution1.jpg)

## 背景

在 RAG 中，我们需要根据输入的问题去数据库里执行 embedding 搜索，查找相关的内容，从而查找到相似的内容（简称知识库搜索）。

在搜索的过程中，尤其是连续对话的搜索，我们通常会发现后续的问题难以搜索到合适的内容，其中一个原因是知识库搜索只会使用“当前”的问题去执行。看下面的例子：

![](/imgs/coreferenceResolution2.webp)

用户在提问“第二点是什么”的时候，只会去知识库里查找“第二点是什么”，压根查不到内容。实际上需要查询的是“QA结构是什么”。因此我们需要引入一个【问题优化】模块，来对用户当前的问题进行补全，从而使得知识库搜索能够搜索到合适的内容。使用补全后效果如下：

![](/imgs/coreferenceResolution3.webp)


## 功能

调用 AI 去对用户当前的问题进行补全。目前主要是补全“指代”词，使得检索词更加的完善可靠，从而增强上下文连续对话的知识库搜索能力。

遇到最大的难题在于：模型对于【补全】的概念可能不清晰，且对于长上下文往往无法准确的知道应该如何补全。

## 示例

- [接入谷歌搜索](/docs/workflow/examples/google_search/)