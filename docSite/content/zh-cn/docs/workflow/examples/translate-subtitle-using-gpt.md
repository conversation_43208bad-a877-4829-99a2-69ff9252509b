---
title: '长字幕翻译'
description: '利用 AI 自我反思提升翻译质量，同时循环迭代执行 AI 工作流来突破 LLM tokens 限制，实现一个高效的长字幕翻译机器人。'
icon: 'translate'
draft: false
toc: true
weight: 401
---

直接使用 LLM 来翻译长字幕会遇到很多难点，这些难点也正是直接使用 AI 无法有效处理的问题：

1. **Tokens 限制**：这是最明显的障碍。大语言模型 (LLM) 通常有输出 tokens 的限制，这意味着**对于长文本，如果不使用特殊的工作流，可能需要手动将文本分段，逐段输入 AI 进行翻译，然后再手动拼接结果**。这个过程不仅繁琐，还容易出错。

2. **字幕格式的保持**：对于字幕来说，时间轴信息至关重要。然而，AI 模型有时会产生 “幻觉”，即无中生有地修改或生成不存在的信息。在字幕翻译中，这可能导致 AI 错误地修改时间轴，使字幕与音频不同步。

3. **翻译质量**：简单的机器翻译往往无法满足观众的需求。即使是大语言模型，单轮翻译的质量也常常不尽如人意。对于字幕来说，翻译质量直接影响观看体验，糟糕的翻译会严重影响观众的沉浸感。

本案例将展示如何利用 FastGPT 工作流代码结合 LLM 来有效解决这些问题。我们的方法不仅能克服技术限制，还能显著提升翻译质量。

## 提取字幕信息

工作流的一大优势在于可以结合额外的操作，使 AI 能更精准地处理信息。在字幕翻译中，我们可以**先分离 SRT 字幕文件的各个组成部分，然后只让 LLM 翻译文本部分**。这种方法既节约了 token 使用，又确保了时间轴信息不被误改。

具体实现如下：

1. 使用代码执行模块，对输入的原始字幕文本进行解析。
2. 将字幕信息分类为三部分：时间信息、序号信息和文本信息。
3. 只保留文本信息用于后续的 AI 翻译。

![](/imgs/extract-subtitle.png)

这种预处理步骤大大提高了整个翻译过程的效率和准确性。

## 切分文本

为了进一步优化翻译过程，我们需要将提取出的文本信息重新组织。这一步的目的是**将文本分割成适合 LLM 处理的大小，同时保持上下文的连贯性**。

在本例中，我们采用以下策略：

1. 将文本按照每 40 句为一组进行切分。这个数字是经过多次测试后得出的平衡点，既能保证翻译质量，又不会超出 LLM 的处理能力。
2. 使用 <T> 标签分割每句文本。这种标记方法便于后续的重新组装，同时也为 AI 模型提供了清晰的句子边界。

![](/imgs/cut-text.png)

这种切分方法既考虑了 AI 模型的能力限制，又保证了翻译的连贯性。通过保持适当的上下文，我们可以得到更加准确和自然的翻译结果。

## 格式化原文本

在这一步，我们构建了最终输入给 LLM 的原文本。这个步骤的关键在于如何在控制 tokens 数量的同时，为 AI 提供足够的上下文信息。我们采用了以下策略：

1. **传入所有文本作为背景上下文。这确保 AI 能理解整段对话的语境**。
2. 使用<TRANSLATE_THIS>标签明确指出当前需要翻译的片段。这种方法既能控制 AI 的输出范围，又不会丢失整体语境。

![](/imgs/format-original-text.png)

这种格式化方法使得 AI 能在理解全局的基础上，专注于翻译特定部分，从而提高翻译的准确性和连贯性。

## LLM 翻译

这是整个过程中最关键的一步。我们利用 LLM 的强大能力来实现高质量翻译。在这一步中，我们将之前提到的 “初始翻译 -> 反思 -> 提升翻译” 的过程整合到了同一个提示词中。

这个过程包括以下几个阶段：

1. 第一轮直译：要求 AI 严格按照 <T> 标签逐句翻译，保证准确性。

   ![](/imgs/ai-translate-1.png)

2. 第二轮意译：允许 AI 自主发挥，对第一轮的结果进行修改和优化。

3. 第三轮反思：AI 对自己的翻译进行评价，从多个角度提出改进建议。

   ![](/imgs/ai-translate-2.png)

4. 最后一轮修改：根据反思阶段的建议，AI 对翻译进行最后的调整和优化。

   ![](/imgs/ai-translate-3.png)

这种多轮翻译和反思的方法显著提高了翻译质量。它不仅能捕捉原文的准确含义，还能使翻译更加流畅自然。

值得注意的是，这种方法的效果与直接分步执行相当，但工作流更加简洁高效。

## 整合字幕

完成翻译后，我们需要将所有信息重新组合成完整的字幕文件。这一步骤包括：

1. 整合之前分离的序号信息和时间信息。
2. 将翻译好的文本与原文本对应。
3. 使用代码执行模块自动完成组装过程。

![](/imgs/combine-subtitle.png)

这个过程不仅提高了效率，还最大限度地减少了人为错误的可能性。

## 循环执行

为了处理整个长字幕文件，我们需要一个循环执行机制。这是通过一个简单但有效的判断模块实现的：

1. 检查当前翻译的文本块是否为最后一个。
2. 如果不是，则将工作流重定向到格式化原文本块节点。
3. 取出下一段需要翻译的文本，重新开始翻译和拼接过程。

![](/imgs/loop-execution.png)

这种循环机制确保了整个长字幕文件能被完整处理，无论字幕有多长。

## 实际应用示例

为了验证这个方法的有效性，我们选取了一段《权力的游戏》的英文字幕，将其翻译成简体中文。可以看出我们的方法不仅能准确翻译内容，还能保持字幕的格式和时间轴信息。

![](/imgs/gpt-translate-example.png)


## 附件

本工作流完整配置如下，可直接复制，导入到 FastGPT 中。

{{% details title="工作流编排配置" closed="true" %}}

```json
{
  "nodes": [
    {
      "nodeId": "userGuide",
      "name": "系统配置",
      "intro": "可以配置应用的系统参数",
      "avatar": "core/workflow/template/systemConfig",
      "flowNodeType": "userGuide",
      "position": {
        "x": -1453.0815298642474,
        "y": 269.10239463914263
      },
      "version": "481",
      "inputs": [
        {
          "key": "welcomeText",
          "renderTypeList": [
            "hidden"
          ],
          "valueType": "string",
          "label": "core.app.Welcome Text",
          "value": ""
        },
        {
          "key": "variables",
          "renderTypeList": [
            "hidden"
          ],
          "valueType": "any",
          "label": "core.app.Chat Variable",
          "value": []
        },
        {
          "key": "questionGuide",
          "valueType": "boolean",
          "renderTypeList": [
            "hidden"
          ],
          "label": "core.app.Question Guide",
          "value": false
        },
        {
          "key": "tts",
          "renderTypeList": [
            "hidden"
          ],
          "valueType": "any",
          "label": "",
          "value": {
            "type": "web"
          }
        },
        {
          "key": "whisper",
          "renderTypeList": [
            "hidden"
          ],
          "valueType": "any",
          "label": "",
          "value": {
            "open": false,
            "autoSend": false,
            "autoTTSResponse": false
          }
        },
        {
          "key": "scheduleTrigger",
          "renderTypeList": [
            "hidden"
          ],
          "valueType": "any",
          "label": "",
          "value": null
        }
      ],
      "outputs": []
    },
    {
      "nodeId": "448745",
      "name": "流程开始",
      "intro": "",
      "avatar": "core/workflow/template/workflowStart",
      "flowNodeType": "workflowStart",
      "position": {
        "x": -1458.2511936623089,
        "y": 1218.2790943636066
      },
      "version": "481",
      "inputs": [
        {
          "key": "userChatInput",
          "renderTypeList": [
            "reference",
            "textarea"
          ],
          "valueType": "string",
          "label": "用户问题",
          "required": true,
          "toolDescription": "用户问题"
        }
      ],
      "outputs": [
        {
          "id": "userChatInput",
          "key": "userChatInput",
          "label": "core.module.input.label.user question",
          "type": "static",
          "valueType": "string"
        }
      ]
    },
    {
      "nodeId": "yjFO3YcM7KG2",
      "name": "LLM 翻译",
      "intro": "AI 大模型对话",
      "avatar": "core/workflow/template/aiChat",
      "flowNodeType": "chatNode",
      "showStatus": true,
      "position": {
        "x": 2569.420973631976,
        "y": 909.4127366971411
      },
      "version": "481",
      "inputs": [
        {
          "key": "model",
          "renderTypeList": [
            "settingLLMModel",
            "reference"
          ],
          "label": "core.module.input.label.aiModel",
          "valueType": "string",
          "selectedTypeIndex": 0,
          "value": "claude-3-5-sonnet-20240620"
        },
        {
          "key": "temperature",
          "renderTypeList": [
            "hidden"
          ],
          "label": "",
          "value": 3,
          "valueType": "number",
          "min": 0,
          "max": 10,
          "step": 1
        },
        {
          "key": "maxToken",
          "renderTypeList": [
            "hidden"
          ],
          "label": "",
          "value": 4000,
          "valueType": "number",
          "min": 100,
          "max": 4000,
          "step": 50
        },
        {
          "key": "isResponseAnswerText",
          "renderTypeList": [
            "hidden"
          ],
          "label": "",
          "value": false,
          "valueType": "boolean"
        },
        {
          "key": "quoteTemplate",
          "renderTypeList": [
            "hidden"
          ],
          "label": "",
          "valueType": "string"
        },
        {
          "key": "quotePrompt",
          "renderTypeList": [
            "hidden"
          ],
          "label": "",
          "valueType": "string"
        },
        {
          "key": "systemPrompt",
          "renderTypeList": [
            "textarea",
            "reference"
          ],
          "max": 3000,
          "valueType": "string",
          "label": "core.ai.Prompt",
          "description": "core.app.tip.chatNodeSystemPromptTip",
          "placeholder": "core.app.tip.chatNodeSystemPromptTip",
          "value": "# Role: 资深字幕翻译专家\n\n## Background:\n你是一位经验丰富的{{source_lang}}和{{target_lang}}字幕翻译专家,精通{{source_lang}}和{{target_lang}}互译,尤其擅长将{{source_lang}}字幕译成流畅易懂的{{target_lang}}字幕。你曾多次带领团队完成大型商业电影的字幕翻译项目,所翻译的字幕广受好评。\n\n## Attention:\n- 翻译过程中要始终坚持\"信、达、雅\"的原则,但\"达\"尤为重要\n- 翻译的字幕要符合{{target_lang}}的表达习惯,通俗易懂,连贯流畅\n- 避免使用过于文绉绉的表达和晦涩难懂的典故引用 \n- 诗词歌词等内容需按原文换行和节奏分行,不破坏原排列格式  \n- 翻译对象是字幕，请进入整段文本的语境中对需要翻译的文本段进行翻译\n- <T>是标识每一帧字幕的标签,请严格按照<T>对文本的分割逐帧翻译，每一帧字幕末尾不要加 \\n 回车标识，且第一帧字幕开头不需要加<T>标识\n\n## Constraints:\n- 必须严格遵循四轮翻译流程:直译、意译、反思、提升\n- 译文要忠实原文,准确无误,不能遗漏或曲解原意\n- 最终译文使用Markdown的代码块呈现,但是不用输出markdown这个单词\n- <T>是标识每一帧字幕的标签,请严格按照<T>对文本的分割逐帧翻译，每一帧字幕末尾不要加 \\n 回车标识，且第一帧字幕开头不需要加<T>标识\n\n## Goals:\n- 通过四轮翻译流程,将{{source_lang}}字幕译成高质量的{{target_lang}}字幕\n- 翻译的字幕要准确传达原字幕意思,语言表达力求浅显易懂,朗朗上口  \n\n## Workflow:\n1. 第一轮直译:严格按照<T>逐句翻译,不遗漏任何信息\n2. 第二轮意译:在直译的基础上用通俗流畅的{{target_lang}}意译原文,逐句翻译,保留<T>标识标签\n3. 第三轮反思:仔细审视译文,分点列出一份建设性的批评和有用的建议清单以改进翻译，对每一句话提出建议，从以下四个角度展开\n    (i) 准确性（纠正添加、误译、遗漏或未翻译的文本错误），\n    (ii) 流畅性（应用{{target_lang}}的语法、拼写和标点规则，并确保没有不必要的重复），\n    (iii) 风格（确保翻译反映源文本的风格并考虑其文化背景），\n    (iv) 术语（确保术语使用一致且反映源文本所在领域，注意确保使用{{target_lang}}中的等效习语）\n4. 第四轮提升:严格遵循第三轮提出的建议对翻译修改,定稿出一个简洁畅达、符合大众观影习惯的字幕译文,保留<T>标识标签\n\n## OutputFormat:\n- 每一轮前用【思考】说明该轮要点\n- 第一轮和第二轮翻译后用【翻译】呈现译文\n- 第三轮输出建议清单，分点列出，在每一点前用*xxx*标识这条建议对应的要点，如*风格*;建议前用【思考】说明该轮要点，建议后用【建议】呈现建议\n- 第四轮在\\`\\`\\`代码块中展示最终{{target_lang}}字幕文件内容，如\\`\\`\\`xxx\\`\\`\\`\n\n## Suggestions:\n- 直译时力求忠实原文,但注意控制每帧字幕的字数,必要时进行精简压缩\n- 意译时在准确表达原意的基础上,用最朴实无华的{{target_lang}}来表达\n- 反思环节重点关注译文是否符合{{target_lang}}表达习惯,是否通俗易懂,是否准确流畅,是否术语一致\n- 提升环节采用反思环节的建议对意译环节的翻译进行修改，适度采用一些口语化的表达、网络流行语等,增强字幕的亲和力\n- 注意<T>是很重要的标识标签，请确保标签能在正确位置输出"
        },
        {
          "key": "history",
          "renderTypeList": [
            "numberInput",
            "reference"
          ],
          "valueType": "chatHistory",
          "label": "core.module.input.label.chat history",
          "description": "最多携带多少轮对话记录",
          "required": true,
          "min": 0,
          "max": 50,
          "value": 6
        },
        {
          "key": "userChatInput",
          "renderTypeList": [
            "reference",
            "textarea"
          ],
          "valueType": "string",
          "label": "用户问题",
          "required": true,
          "toolDescription": "用户问题",
          "value": [
            "bxz97Vg4Omux",
            "system_text"
          ]
        },
        {
          "key": "quoteQA",
          "renderTypeList": [
            "settingDatasetQuotePrompt"
          ],
          "label": "",
          "debugLabel": "知识库引用",
          "description": "",
          "valueType": "datasetQuote"
        }
      ],
      "outputs": [
        {
          "id": "history",
          "key": "history",
          "required": true,
          "label": "core.module.output.label.New context",
          "description": "core.module.output.description.New context",
          "valueType": "chatHistory",
          "type": "static"
        },
        {
          "id": "answerText",
          "key": "answerText",
          "required": true,
          "label": "core.module.output.label.Ai response content",
          "description": "core.module.output.description.Ai response content",
          "valueType": "string",
          "type": "static"
        }
      ]
    },
    {
      "nodeId": "bxz97Vg4Omux",
      "name": "LLM 翻译提示词",
      "intro": "可对固定或传入的文本进行加工后输出，非字符串类型数据最终会转成字符串类型。",
      "avatar": "core/workflow/template/textConcat",
      "flowNodeType": "textEditor",
      "position": {
        "x": 1893.11421220213,
        "y": 1065.1299598362698
      },
      "version": "486",
      "inputs": [
        {
          "key": "system_addInputParam",
          "renderTypeList": [
            "addInputParam"
          ],
          "valueType": "dynamic",
          "label": "",
          "required": false,
          "description": "可以引用其他节点的输出，作为文本拼接的变量，通过 {{字段名}} 来引用变量",
          "customInputConfig": {
            "selectValueTypeList": [
              "string",
              "number",
              "boolean",
              "object",
              "arrayString",
              "arrayNumber",
              "arrayBoolean",
              "arrayObject",
              "any",
              "chatHistory",
              "datasetQuote",
              "dynamic",
              "selectApp",
              "selectDataset"
            ],
            "showDescription": false,
            "showDefaultValue": false
          }
        },
        {
          "key": "system_textareaInput",
          "renderTypeList": [
            "textarea"
          ],
          "valueType": "string",
          "required": true,
          "label": "拼接文本",
          "placeholder": "可通过 {{字段名}} 来引用变量",
          "value": "你的任务是将文本从{{source_lang}}翻译成{{target_lang}}\n\n源文本如下,由XML标签<SOURCE_TEXT>和</SOURCE_TEXT>分隔:\n\n<SOURCE_TEXT>\n\n{{tagged_text}}\n\n</SOURCE_TEXT>\n\n仅翻译源文本中由<TRANSLATE_THIS>和</TRANSLATE_THIS>分隔的部分,将其余的源文本作为上下文\n\n重申一下,你应该只翻译文本的这一部分,这里再次显示在<TRANSLATE_THIS>和</TRANSLATE_THIS>之间:\n\n<TRANSLATE_THIS>\n\n{{chunk_to_translate}}\n\n</TRANSLATE_THIS>"
        },
        {
          "renderTypeList": [
            "reference"
          ],
          "valueType": "string",
          "canEdit": true,
          "key": "tagged_text",
          "label": "tagged_text",
          "customInputConfig": {
            "selectValueTypeList": [
              "string",
              "number",
              "boolean",
              "object",
              "arrayString",
              "arrayNumber",
              "arrayBoolean",
              "arrayObject",
              "any",
              "chatHistory",
              "datasetQuote",
              "dynamic",
              "selectApp",
              "selectDataset"
            ],
            "showDescription": false,
            "showDefaultValue": false
          },
          "required": true,
          "value": [
            "quYZgsW32ApA",
            "xhXu6sdEWBnF"
          ]
        },
        {
          "renderTypeList": [
            "reference"
          ],
          "valueType": "string",
          "canEdit": true,
          "key": "chunk_to_translate",
          "label": "chunk_to_translate",
          "customInputConfig": {
            "selectValueTypeList": [
              "string",
              "number",
              "boolean",
              "object",
              "arrayString",
              "arrayNumber",
              "arrayBoolean",
              "arrayObject",
              "any",
              "chatHistory",
              "datasetQuote",
              "dynamic",
              "selectApp",
              "selectDataset"
            ],
            "showDescription": false,
            "showDefaultValue": false
          },
          "required": true,
          "value": [
            "quYZgsW32ApA",
            "eCp73lztAEGK"
          ]
        }
      ],
      "outputs": [
        {
          "id": "system_text",
          "key": "system_text",
          "label": "拼接结果",
          "type": "static",
          "valueType": "string"
        }
      ]
    },
    {
      "nodeId": "w4heEpNflz59",
      "name": "判断是否执行结束",
      "intro": "根据一定的条件，执行不同的分支。",
      "avatar": "core/workflow/template/ifelse",
      "flowNodeType": "ifElseNode",
      "showStatus": true,
      "position": {
        "x": 5625.495682697096,
        "y": 1199.9313115831496
      },
      "version": "481",
      "inputs": [
        {
          "key": "ifElseList",
          "renderTypeList": [
            "hidden"
          ],
          "valueType": "any",
          "label": "",
          "value": [
            {
              "condition": "AND",
              "list": [
                {
                  "variable": [
                    "a2lqxASWi1vb",
                    "nmBmGaARbKkl"
                  ],
                  "condition": "equalTo",
                  "value": "true"
                }
              ]
            }
          ]
        }
      ],
      "outputs": [
        {
          "id": "ifElseResult",
          "key": "ifElseResult",
          "label": "判断结果",
          "valueType": "string",
          "type": "static"
        }
      ]
    },
    {
      "nodeId": "a2lqxASWi1vb",
      "name": "判断是否执行结束",
      "intro": "执行一段简单的脚本代码，通常用于进行复杂的数据处理。",
      "avatar": "core/workflow/template/codeRun",
      "flowNodeType": "code",
      "showStatus": true,
      "position": {
        "x": 5099.256084679105,
        "y": 1102.1518590433243
      },
      "version": "482",
      "inputs": [
        {
          "key": "system_addInputParam",
          "renderTypeList": [
            "addInputParam"
          ],
          "valueType": "dynamic",
          "label": "",
          "required": false,
          "description": "这些变量会作为代码的运行的输入参数",
          "customInputConfig": {
            "selectValueTypeList": [
              "string",
              "number",
              "boolean",
              "object",
              "arrayString",
              "arrayNumber",
              "arrayBoolean",
              "arrayObject",
              "any",
              "chatHistory",
              "datasetQuote",
              "dynamic",
              "selectApp",
              "selectDataset"
            ],
            "showDescription": false,
            "showDefaultValue": true
          }
        },
        {
          "key": "codeType",
          "renderTypeList": [
            "hidden"
          ],
          "label": "",
          "value": "js"
        },
        {
          "key": "code",
          "renderTypeList": [
            "custom"
          ],
          "label": "",
          "value": "function main({chunks, currentChunk}){\n    const findIndex = chunks.findIndex((item) => item === currentChunk)\n\n    return {\n        isEnd: chunks.length-1 === findIndex,\n        i: findIndex + 1,\n    }\n}"
        },
        {
          "renderTypeList": [
            "reference"
          ],
          "valueType": "arrayString",
          "canEdit": true,
          "key": "chunks",
          "label": "chunks",
          "customInputConfig": {
            "selectValueTypeList": [
              "string",
              "number",
              "boolean",
              "object",
              "arrayString",
              "arrayNumber",
              "arrayBoolean",
              "arrayObject",
              "any",
              "chatHistory",
              "datasetQuote",
              "dynamic",
              "selectApp",
              "selectDataset"
            ],
            "showDescription": false,
            "showDefaultValue": true
          },
          "required": true,
          "value": [
            "y3WEYOQ09CGC",
            "qLUQfhG0ILRX"
          ]
        },
        {
          "renderTypeList": [
            "reference"
          ],
          "valueType": "string",
          "canEdit": true,
          "key": "currentChunk",
          "label": "currentChunk",
          "customInputConfig": {
            "selectValueTypeList": [
              "string",
              "number",
              "boolean",
              "object",
              "arrayString",
              "arrayNumber",
              "arrayBoolean",
              "arrayObject",
              "any",
              "chatHistory",
              "datasetQuote",
              "dynamic",
              "selectApp",
              "selectDataset"
            ],
            "showDescription": false,
            "showDefaultValue": true
          },
          "required": true,
          "value": [
            "quYZgsW32ApA",
            "eCp73lztAEGK"
          ]
        }
      ],
      "outputs": [
        {
          "id": "system_rawResponse",
          "key": "system_rawResponse",
          "label": "完整响应数据",
          "valueType": "object",
          "type": "static"
        },
        {
          "id": "error",
          "key": "error",
          "label": "运行错误",
          "description": "代码运行错误信息，成功时返回空",
          "valueType": "object",
          "type": "static"
        },
        {
          "id": "system_addOutputParam",
          "key": "system_addOutputParam",
          "type": "dynamic",
          "valueType": "dynamic",
          "label": "",
          "customFieldConfig": {
            "selectValueTypeList": [
              "string",
              "number",
              "boolean",
              "object",
              "arrayString",
              "arrayNumber",
              "arrayBoolean",
              "arrayObject",
              "any",
              "chatHistory",
              "datasetQuote",
              "dynamic",
              "selectApp",
              "selectDataset"
            ],
            "showDescription": false,
            "showDefaultValue": false
          },
          "description": "将代码中 return 的对象作为输出，传递给后续的节点。变量名需要对应 return 的 key"
        },
        {
          "id": "nmBmGaARbKkl",
          "valueType": "boolean",
          "type": "dynamic",
          "key": "isEnd",
          "label": "isEnd"
        },
        {
          "id": "nqB98uKpq6Ig",
          "valueType": "number",
          "type": "dynamic",
          "key": "i",
          "label": "i"
        }
      ]
    },
    {
      "nodeId": "quYZgsW32ApA",
      "name": "格式化源文本块",
      "intro": "执行一段简单的脚本代码，通常用于进行复杂的数据处理。",
      "avatar": "core/workflow/template/codeRun",
      "flowNodeType": "code",
      "showStatus": true,
      "position": {
        "x": 1251.2839737092052,
        "y": 991.619268503857
      },
      "version": "482",
      "inputs": [
        {
          "key": "system_addInputParam",
          "renderTypeList": [
            "addInputParam"
          ],
          "valueType": "dynamic",
          "label": "",
          "required": false,
          "description": "这些变量会作为代码的运行的输入参数",
          "customInputConfig": {
            "selectValueTypeList": [
              "string",
              "number",
              "boolean",
              "object",
              "arrayString",
              "arrayNumber",
              "arrayBoolean",
              "arrayObject",
              "any",
              "chatHistory",
              "datasetQuote",
              "dynamic",
              "selectApp",
              "selectDataset"
            ],
            "showDescription": false,
            "showDefaultValue": true
          }
        },
        {
          "key": "codeType",
          "renderTypeList": [
            "hidden"
          ],
          "label": "",
          "value": "js"
        },
        {
          "key": "code",
          "renderTypeList": [
            "custom"
          ],
          "label": "",
          "value": "function main({source_text_chunks, i=0}){\n    let before = source_text_chunks.slice(0, i).join(\"\");\n    let current = \" <TRANSLATE_THIS>\" + source_text_chunks[i] + \"</TRANSLATE_THIS>\";\n    let after = source_text_chunks.slice(i + 1).join(\"\");\n    let tagged_text = before + current + after;\n\n    return {\n        tagged_text,\n        chunk_to_translate: source_text_chunks[i],\n    }\n}"
        },
        {
          "renderTypeList": [
            "reference"
          ],
          "valueType": "number",
          "canEdit": true,
          "key": "i",
          "label": "i",
          "customInputConfig": {
            "selectValueTypeList": [
              "string",
              "number",
              "boolean",
              "object",
              "arrayString",
              "arrayNumber",
              "arrayBoolean",
              "arrayObject",
              "any",
              "chatHistory",
              "datasetQuote",
              "dynamic",
              "selectApp",
              "selectDataset"
            ],
            "showDescription": false,
            "showDefaultValue": true
          },
          "required": true,
          "value": [
            "a2lqxASWi1vb",
            "nqB98uKpq6Ig"
          ]
        },
        {
          "renderTypeList": [
            "reference"
          ],
          "valueType": "arrayString",
          "canEdit": true,
          "key": "source_text_chunks",
          "label": "source_text_chunks",
          "customInputConfig": {
            "selectValueTypeList": [
              "string",
              "number",
              "boolean",
              "object",
              "arrayString",
              "arrayNumber",
              "arrayBoolean",
              "arrayObject",
              "any",
              "chatHistory",
              "datasetQuote",
              "dynamic",
              "selectApp",
              "selectDataset"
            ],
            "showDescription": false,
            "showDefaultValue": true
          },
          "required": true,
          "value": [
            "y3WEYOQ09CGC",
            "qLUQfhG0ILRX"
          ]
        }
      ],
      "outputs": [
        {
          "id": "system_rawResponse",
          "key": "system_rawResponse",
          "label": "完整响应数据",
          "valueType": "object",
          "type": "static"
        },
        {
          "id": "error",
          "key": "error",
          "label": "运行错误",
          "description": "代码运行错误信息，成功时返回空",
          "valueType": "object",
          "type": "static"
        },
        {
          "id": "system_addOutputParam",
          "key": "system_addOutputParam",
          "type": "dynamic",
          "valueType": "dynamic",
          "label": "",
          "customFieldConfig": {
            "selectValueTypeList": [
              "string",
              "number",
              "boolean",
              "object",
              "arrayString",
              "arrayNumber",
              "arrayBoolean",
              "arrayObject",
              "any",
              "chatHistory",
              "datasetQuote",
              "dynamic",
              "selectApp",
              "selectDataset"
            ],
            "showDescription": false,
            "showDefaultValue": false
          },
          "description": "将代码中 return 的对象作为输出，传递给后续的节点。变量名需要对应 return 的 key"
        },
        {
          "id": "xhXu6sdEWBnF",
          "valueType": "string",
          "type": "dynamic",
          "key": "tagged_text",
          "label": "tagged_text"
        },
        {
          "id": "eCp73lztAEGK",
          "valueType": "string",
          "type": "dynamic",
          "key": "chunk_to_translate",
          "label": "chunk_to_translate"
        }
      ]
    },
    {
      "nodeId": "izsNX8FXGD1t",
      "name": "指定回复",
      "intro": "该模块可以直接回复一段指定的内容。常用于引导、提示。非字符串内容传入时，会转成字符串进行输出。",
      "avatar": "core/workflow/template/reply",
      "flowNodeType": "answerNode",
      "position": {
        "x": 6399.439691374053,
        "y": 1204.4024103331792
      },
      "version": "481",
      "inputs": [
        {
          "key": "text",
          "renderTypeList": [
            "textarea",
            "reference"
          ],
          "valueType": "any",
          "required": true,
          "label": "core.module.input.label.Response content",
          "description": "core.module.input.description.Response content",
          "placeholder": "core.module.input.description.Response content",
          "value": "\n\n*** 字幕反思翻译完成！***"
        }
      ],
      "outputs": []
    },
    {
      "nodeId": "vlNHndpNuFXB",
      "name": "取出 LLM 翻译第四轮文本",
      "intro": "执行一段简单的脚本代码，通常用于进行复杂的数据处理。",
      "avatar": "core/workflow/template/codeRun",
      "flowNodeType": "code",
      "showStatus": true,
      "position": {
        "x": 3284.6375352131763,
        "y": 950.1100995985583
      },
      "version": "482",
      "inputs": [
        {
          "key": "system_addInputParam",
          "renderTypeList": [
            "addInputParam"
          ],
          "valueType": "dynamic",
          "label": "",
          "required": false,
          "description": "这些变量会作为代码的运行的输入参数",
          "editField": {
            "key": true,
            "valueType": true
          },
          "customInputConfig": {
            "selectValueTypeList": [
              "string",
              "number",
              "boolean",
              "object",
              "arrayString",
              "arrayNumber",
              "arrayBoolean",
              "arrayObject",
              "any",
              "chatHistory",
              "datasetQuote",
              "dynamic",
              "selectApp",
              "selectDataset"
            ],
            "showDescription": false,
            "showDefaultValue": true
          }
        },
        {
          "key": "codeType",
          "renderTypeList": [
            "hidden"
          ],
          "label": "",
          "value": "js"
        },
        {
          "key": "code",
          "renderTypeList": [
            "custom"
          ],
          "label": "",
          "value": "function main({data1}){\n    const result = data1.split(\"```\").filter(item => !!item.trim())\n\n    if(result[result.length-1]) {\n        return {\n            result: result[result.length-1].trim() \n        }\n    }\n\n    return {\n        result: '未截取到翻译内容'\n    }\n}"
        },
        {
          "key": "data1",
          "valueType": "string",
          "label": "data1",
          "renderTypeList": [
            "reference"
          ],
          "description": "",
          "canEdit": true,
          "editField": {
            "key": true,
            "valueType": true
          },
          "value": [
            "yjFO3YcM7KG2",
            "answerText"
          ],
          "customInputConfig": {
            "selectValueTypeList": [
              "string",
              "number",
              "boolean",
              "object",
              "arrayString",
              "arrayNumber",
              "arrayBoolean",
              "arrayObject",
              "any",
              "chatHistory",
              "datasetQuote",
              "dynamic",
              "selectApp",
              "selectDataset"
            ],
            "showDescription": false,
            "showDefaultValue": true
          }
        }
      ],
      "outputs": [
        {
          "id": "system_rawResponse",
          "key": "system_rawResponse",
          "label": "完整响应数据",
          "valueType": "object",
          "type": "static"
        },
        {
          "id": "error",
          "key": "error",
          "label": "运行错误",
          "description": "代码运行错误信息，成功时返回空",
          "valueType": "object",
          "type": "static"
        },
        {
          "id": "system_addOutputParam",
          "key": "system_addOutputParam",
          "type": "dynamic",
          "valueType": "dynamic",
          "label": "",
          "customFieldConfig": {
            "selectValueTypeList": [
              "string",
              "number",
              "boolean",
              "object",
              "arrayString",
              "arrayNumber",
              "arrayBoolean",
              "arrayObject",
              "any",
              "chatHistory",
              "datasetQuote",
              "dynamic",
              "selectApp",
              "selectDataset"
            ],
            "showDescription": false,
            "showDefaultValue": false
          },
          "description": "将代码中 return 的对象作为输出，传递给后续的节点。变量名需要对应 return 的 key"
        },
        {
          "id": "qLUQfhG0ILRX",
          "type": "dynamic",
          "key": "result",
          "valueType": "string",
          "label": "result"
        },
        {
          "id": "gR0mkQpJ4Og8",
          "type": "dynamic",
          "key": "data2",
          "valueType": "string",
          "label": "data2"
        }
      ]
    },
    {
      "nodeId": "qlt9KJbbS9yJ",
      "name": "判断源语言和目标语言是否相同",
      "intro": "根据一定的条件，执行不同的分支。",
      "avatar": "core/workflow/template/ifelse",
      "flowNodeType": "ifElseNode",
      "showStatus": true,
      "position": {
        "x": -648.2730659546055,
        "y": 1295.3336516652123
      },
      "version": "481",
      "inputs": [
        {
          "key": "ifElseList",
          "renderTypeList": [
            "hidden"
          ],
          "valueType": "any",
          "label": "",
          "value": [
            {
              "condition": "AND",
              "list": [
                {
                  "variable": [
                    "frjbsrlnJJsR",
                    "qLUQfhG0ILRX"
                  ],
                  "condition": "equalTo",
                  "value": "false"
                }
              ]
            }
          ]
        }
      ],
      "outputs": [
        {
          "id": "ifElseResult",
          "key": "ifElseResult",
          "label": "判断结果",
          "valueType": "string",
          "type": "static"
        }
      ]
    },
    {
      "nodeId": "frjbsrlnJJsR",
      "name": "判断源语言和目标语言是否相同",
      "intro": "执行一段简单的脚本代码，通常用于进行复杂的数据处理。",
      "avatar": "core/workflow/template/codeRun",
      "flowNodeType": "code",
      "showStatus": true,
      "position": {
        "x": -1142.9562352499165,
        "y": 1031.4486788585432
      },
      "version": "482",
      "inputs": [
        {
          "key": "system_addInputParam",
          "renderTypeList": [
            "addInputParam"
          ],
          "valueType": "dynamic",
          "label": "",
          "required": false,
          "description": "这些变量会作为代码的运行的输入参数",
          "customInputConfig": {
            "selectValueTypeList": [
              "string",
              "number",
              "boolean",
              "object",
              "arrayString",
              "arrayNumber",
              "arrayBoolean",
              "arrayObject",
              "any",
              "chatHistory",
              "datasetQuote",
              "dynamic",
              "selectApp",
              "selectDataset"
            ],
            "showDescription": false,
            "showDefaultValue": true
          }
        },
        {
          "key": "codeType",
          "renderTypeList": [
            "hidden"
          ],
          "label": "",
          "value": "js"
        },
        {
          "key": "code",
          "renderTypeList": [
            "custom"
          ],
          "label": "",
          "value": "function main({source_lang, target_lang}){\n    \n    return {\n        result: source_lang === target_lang\n    }\n}"
        },
        {
          "renderTypeList": [
            "reference"
          ],
          "valueType": "string",
          "canEdit": true,
          "key": "source_lang",
          "label": "source_lang",
          "customInputConfig": {
            "selectValueTypeList": [
              "string",
              "number",
              "boolean",
              "object",
              "arrayString",
              "arrayNumber",
              "arrayBoolean",
              "arrayObject",
              "any",
              "chatHistory",
              "datasetQuote",
              "dynamic",
              "selectApp",
              "selectDataset"
            ],
            "showDescription": false,
            "showDefaultValue": true
          },
          "required": true,
          "value": [
            "VARIABLE_NODE_ID",
            "source_lang"
          ]
        },
        {
          "renderTypeList": [
            "reference"
          ],
          "valueType": "string",
          "canEdit": true,
          "key": "target_lang",
          "label": "target_lang",
          "customInputConfig": {
            "selectValueTypeList": [
              "string",
              "number",
              "boolean",
              "object",
              "arrayString",
              "arrayNumber",
              "arrayBoolean",
              "arrayObject",
              "any",
              "chatHistory",
              "datasetQuote",
              "dynamic",
              "selectApp",
              "selectDataset"
            ],
            "showDescription": false,
            "showDefaultValue": true
          },
          "required": true,
          "value": [
            "VARIABLE_NODE_ID",
            "target_lang"
          ]
        }
      ],
      "outputs": [
        {
          "id": "system_rawResponse",
          "key": "system_rawResponse",
          "label": "完整响应数据",
          "valueType": "object",
          "type": "static"
        },
        {
          "id": "error",
          "key": "error",
          "label": "运行错误",
          "description": "代码运行错误信息，成功时返回空",
          "valueType": "object",
          "type": "static"
        },
        {
          "id": "system_addOutputParam",
          "key": "system_addOutputParam",
          "type": "dynamic",
          "valueType": "dynamic",
          "label": "",
          "customFieldConfig": {
            "selectValueTypeList": [
              "string",
              "number",
              "boolean",
              "object",
              "arrayString",
              "arrayNumber",
              "arrayBoolean",
              "arrayObject",
              "any",
              "chatHistory",
              "datasetQuote",
              "dynamic",
              "selectApp",
              "selectDataset"
            ],
            "showDescription": false,
            "showDefaultValue": false
          },
          "description": "将代码中 return 的对象作为输出，传递给后续的节点。变量名需要对应 return 的 key"
        },
        {
          "id": "qLUQfhG0ILRX",
          "type": "dynamic",
          "key": "result",
          "valueType": "boolean",
          "label": "result"
        }
      ]
    },
    {
      "nodeId": "dFxrGZS3Wmnz",
      "name": "提示源语言与目标语言相同",
      "intro": "该模块可以直接回复一段指定的内容。常用于引导、提示。非字符串内容传入时，会转成字符串进行输出。",
      "avatar": "core/workflow/template/reply",
      "flowNodeType": "answerNode",
      "position": {
        "x": -554.7555863373991,
        "y": 1727.175384457058
      },
      "version": "481",
      "inputs": [
        {
          "key": "text",
          "renderTypeList": [
            "textarea",
            "reference"
          ],
          "valueType": "any",
          "required": true,
          "label": "core.module.input.label.Response content",
          "description": "core.module.input.description.Response content",
          "placeholder": "core.module.input.description.Response content",
          "selectedTypeIndex": 0,
          "value": "{{source_lang}} 无需再次翻译为 {{target_lang}} ~"
        }
      ],
      "outputs": []
    },
    {
      "nodeId": "tqzmK5oPR9BA",
      "name": "输出翻译",
      "intro": "该模块可以直接回复一段指定的内容。常用于引导、提示。非字符串内容传入时，会转成字符串进行输出。",
      "avatar": "core/workflow/template/reply",
      "flowNodeType": "answerNode",
      "position": {
        "x": 4378.294585712487,
        "y": 1268.975092230105
      },
      "version": "481",
      "inputs": [
        {
          "key": "text",
          "renderTypeList": [
            "textarea",
            "reference"
          ],
          "valueType": "any",
          "required": true,
          "label": "core.module.input.label.Response content",
          "description": "core.module.input.description.Response content",
          "placeholder": "core.module.input.description.Response content",
          "selectedTypeIndex": 1,
          "value": [
            "ppPP6o7YYSTJ",
            "dYalXmYJ60bj"
          ]
        }
      ],
      "outputs": []
    },
    {
      "nodeId": "kbr342XlxSZR",
      "name": "提取字幕信息",
      "intro": "执行一段简单的脚本代码，通常用于进行复杂的数据处理。",
      "avatar": "core/workflow/template/codeRun",
      "flowNodeType": "code",
      "showStatus": true,
      "position": {
        "x": 185.35869756392378,
        "y": 1004.6884026918935
      },
      "version": "482",
      "inputs": [
        {
          "key": "system_addInputParam",
          "renderTypeList": [
            "addInputParam"
          ],
          "valueType": "dynamic",
          "label": "",
          "required": false,
          "description": "这些变量会作为代码的运行的输入参数",
          "customInputConfig": {
            "selectValueTypeList": [
              "string",
              "number",
              "boolean",
              "object",
              "arrayString",
              "arrayNumber",
              "arrayBoolean",
              "arrayObject",
              "any",
              "chatHistory",
              "datasetQuote",
              "dynamic",
              "selectApp",
              "selectDataset"
            ],
            "showDescription": false,
            "showDefaultValue": true
          }
        },
        {
          "key": "codeType",
          "renderTypeList": [
            "hidden"
          ],
          "label": "",
          "value": "js"
        },
        {
          "key": "code",
          "renderTypeList": [
            "custom"
          ],
          "label": "",
          "value": "function main({text}){\n  const lines = text.split('\\n');\n  const timePattern = /\\d{2}:\\d{2}:\\d{2},\\d{3} --> \\d{2}:\\d{2}:\\d{2},\\d{3}/;\n  const numberInfo = [];\n  const timeInfo = [];\n  const textInfo = [];\n  let currentText = [];\n\n  // 提取序号、时间戳和文本信息\n  lines.forEach(line => {\n    if (/^\\d+$/.test(line.trim())) {\n      numberInfo.push(line.trim());\n    } else if (timePattern.test(line)) {\n      timeInfo.push(line);\n      if (currentText.length > 0) {\n        textInfo.push(currentText.join(' '));\n        currentText = [];\n      }\n    } else if (line.trim() === '') {\n      // Skip empty lines\n    } else {\n      currentText.push(line.trim());\n    }\n  });\n\n  if (currentText.length > 0) {\n    textInfo.push(currentText.join(' '));\n  }\n\n  return { numberInfo, timeInfo, textInfo };\n}"
        },
        {
          "renderTypeList": [
            "reference"
          ],
          "valueType": "string",
          "canEdit": true,
          "key": "text",
          "label": "text",
          "customInputConfig": {
            "selectValueTypeList": [
              "string",
              "number",
              "boolean",
              "object",
              "arrayString",
              "arrayNumber",
              "arrayBoolean",
              "arrayObject",
              "any",
              "chatHistory",
              "datasetQuote",
              "dynamic",
              "selectApp",
              "selectDataset"
            ],
            "showDescription": false,
            "showDefaultValue": true
          },
          "required": true,
          "value": [
            "448745",
            "userChatInput"
          ]
        }
      ],
      "outputs": [
        {
          "id": "system_rawResponse",
          "key": "system_rawResponse",
          "label": "完整响应数据",
          "valueType": "object",
          "type": "static"
        },
        {
          "id": "error",
          "key": "error",
          "label": "运行错误",
          "description": "代码运行错误信息，成功时返回空",
          "valueType": "object",
          "type": "static"
        },
        {
          "id": "system_addOutputParam",
          "key": "system_addOutputParam",
          "type": "dynamic",
          "valueType": "dynamic",
          "label": "",
          "customFieldConfig": {
            "selectValueTypeList": [
              "string",
              "number",
              "boolean",
              "object",
              "arrayString",
              "arrayNumber",
              "arrayBoolean",
              "arrayObject",
              "any",
              "chatHistory",
              "datasetQuote",
              "dynamic",
              "selectApp",
              "selectDataset"
            ],
            "showDescription": false,
            "showDefaultValue": false
          },
          "description": "将代码中 return 的对象作为输出，传递给后续的节点。变量名需要对应 return 的 key"
        },
        {
          "id": "h3qVuGhV9HNm",
          "valueType": "arrayString",
          "type": "dynamic",
          "key": "timeInfo",
          "label": "timeInfo"
        },
        {
          "id": "zGYRMNA9xGuI",
          "valueType": "arrayString",
          "type": "dynamic",
          "key": "textInfo",
          "label": "textInfo"
        },
        {
          "id": "dhzTt6Riz8Dp",
          "valueType": "arrayString",
          "type": "dynamic",
          "key": "numberInfo",
          "label": "numberInfo"
        }
      ]
    },
    {
      "nodeId": "ppPP6o7YYSTJ",
      "name": "格式化字幕文件",
      "intro": "执行一段简单的脚本代码，通常用于进行复杂的数据处理。",
      "avatar": "core/workflow/template/codeRun",
      "flowNodeType": "code",
      "showStatus": true,
      "position": {
        "x": 3825.553384884565,
        "y": 956.4575651844932
      },
      "version": "482",
      "inputs": [
        {
          "key": "system_addInputParam",
          "renderTypeList": [
            "addInputParam"
          ],
          "valueType": "dynamic",
          "label": "",
          "required": false,
          "description": "这些变量会作为代码的运行的输入参数",
          "customInputConfig": {
            "selectValueTypeList": [
              "string",
              "number",
              "boolean",
              "object",
              "arrayString",
              "arrayNumber",
              "arrayBoolean",
              "arrayObject",
              "any",
              "chatHistory",
              "datasetQuote",
              "dynamic",
              "selectApp",
              "selectDataset"
            ],
            "showDescription": false,
            "showDefaultValue": true
          }
        },
        {
          "key": "codeType",
          "renderTypeList": [
            "hidden"
          ],
          "label": "",
          "value": "js"
        },
        {
          "key": "code",
          "renderTypeList": [
            "custom"
          ],
          "label": "",
          "value": "function main({combinedText, transedText, timeInfo, currentIndex=0,numberInfo}){\n  const textLines = combinedText.split('<T>');\n  const resultLines = transedText.split('<T>');\n  const combinedLines = [];\n\n  resultLines.forEach((line, index) => {\n    combinedLines.push(numberInfo[currentIndex+index]);\n    combinedLines.push(timeInfo[currentIndex+index]);\n    combinedLines.push(line)\n    combinedLines.push(textLines[index]);\n    combinedLines.push('');\n  });\n\n  const srtContent = combinedLines.join('\\n');\n  \n\n    return {\n        srtContent,\n        currentIndex: currentIndex+textLines.length\n    }\n}"
        },
        {
          "renderTypeList": [
            "reference"
          ],
          "valueType": "string",
          "canEdit": true,
          "key": "combinedText",
          "label": "combinedText",
          "customInputConfig": {
            "selectValueTypeList": [
              "string",
              "number",
              "boolean",
              "object",
              "arrayString",
              "arrayNumber",
              "arrayBoolean",
              "arrayObject",
              "any",
              "chatHistory",
              "datasetQuote",
              "dynamic",
              "selectApp",
              "selectDataset"
            ],
            "showDescription": false,
            "showDefaultValue": true
          },
          "required": true,
          "value": [
            "quYZgsW32ApA",
            "eCp73lztAEGK"
          ]
        },
        {
          "renderTypeList": [
            "reference"
          ],
          "valueType": "string",
          "canEdit": true,
          "key": "transedText",
          "label": "transedText",
          "customInputConfig": {
            "selectValueTypeList": [
              "string",
              "number",
              "boolean",
              "object",
              "arrayString",
              "arrayNumber",
              "arrayBoolean",
              "arrayObject",
              "any",
              "chatHistory",
              "datasetQuote",
              "dynamic",
              "selectApp",
              "selectDataset"
            ],
            "showDescription": false,
            "showDefaultValue": true
          },
          "required": true,
          "value": [
            "vlNHndpNuFXB",
            "qLUQfhG0ILRX"
          ]
        },
        {
          "renderTypeList": [
            "reference"
          ],
          "valueType": "arrayString",
          "canEdit": true,
          "key": "timeInfo",
          "label": "timeInfo",
          "customInputConfig": {
            "selectValueTypeList": [
              "string",
              "number",
              "boolean",
              "object",
              "arrayString",
              "arrayNumber",
              "arrayBoolean",
              "arrayObject",
              "any",
              "chatHistory",
              "datasetQuote",
              "dynamic",
              "selectApp",
              "selectDataset"
            ],
            "showDescription": false,
            "showDefaultValue": true
          },
          "required": true,
          "value": [
            "kbr342XlxSZR",
            "h3qVuGhV9HNm"
          ]
        },
        {
          "renderTypeList": [
            "reference"
          ],
          "valueType": "number",
          "canEdit": true,
          "key": "currentIndex",
          "label": "currentIndex",
          "customInputConfig": {
            "selectValueTypeList": [
              "string",
              "number",
              "boolean",
              "object",
              "arrayString",
              "arrayNumber",
              "arrayBoolean",
              "arrayObject",
              "any",
              "chatHistory",
              "datasetQuote",
              "dynamic",
              "selectApp",
              "selectDataset"
            ],
            "showDescription": false,
            "showDefaultValue": true
          },
          "required": true,
          "value": [
            "ppPP6o7YYSTJ",
            "u6eqeC0pEPe0"
          ]
        },
        {
          "renderTypeList": [
            "reference"
          ],
          "valueType": "arrayString",
          "canEdit": true,
          "key": "numberInfo",
          "label": "numberInfo",
          "customInputConfig": {
            "selectValueTypeList": [
              "string",
              "number",
              "boolean",
              "object",
              "arrayString",
              "arrayNumber",
              "arrayBoolean",
              "arrayObject",
              "any",
              "chatHistory",
              "datasetQuote",
              "dynamic",
              "selectApp",
              "selectDataset"
            ],
            "showDescription": false,
            "showDefaultValue": true
          },
          "required": true,
          "value": [
            "kbr342XlxSZR",
            "dhzTt6Riz8Dp"
          ]
        }
      ],
      "outputs": [
        {
          "id": "system_rawResponse",
          "key": "system_rawResponse",
          "label": "完整响应数据",
          "valueType": "object",
          "type": "static"
        },
        {
          "id": "error",
          "key": "error",
          "label": "运行错误",
          "description": "代码运行错误信息，成功时返回空",
          "valueType": "object",
          "type": "static"
        },
        {
          "id": "system_addOutputParam",
          "key": "system_addOutputParam",
          "type": "dynamic",
          "valueType": "dynamic",
          "label": "",
          "customFieldConfig": {
            "selectValueTypeList": [
              "string",
              "number",
              "boolean",
              "object",
              "arrayString",
              "arrayNumber",
              "arrayBoolean",
              "arrayObject",
              "any",
              "chatHistory",
              "datasetQuote",
              "dynamic",
              "selectApp",
              "selectDataset"
            ],
            "showDescription": false,
            "showDefaultValue": false
          },
          "description": "将代码中 return 的对象作为输出，传递给后续的节点。变量名需要对应 return 的 key"
        },
        {
          "id": "dYalXmYJ60bj",
          "valueType": "string",
          "type": "dynamic",
          "key": "srtContent",
          "label": "srtContent"
        },
        {
          "id": "u6eqeC0pEPe0",
          "valueType": "number",
          "type": "dynamic",
          "key": "currentIndex",
          "label": "currentIndex"
        }
      ]
    },
    {
      "nodeId": "y3WEYOQ09CGC",
      "name": "切分文本",
      "intro": "执行一段简单的脚本代码，通常用于进行复杂的数据处理。",
      "avatar": "core/workflow/template/codeRun",
      "flowNodeType": "code",
      "showStatus": true,
      "position": {
        "x": 742.138506499589,
        "y": 1011.2409789066801
      },
      "version": "482",
      "inputs": [
        {
          "key": "system_addInputParam",
          "renderTypeList": [
            "addInputParam"
          ],
          "valueType": "dynamic",
          "label": "",
          "required": false,
          "description": "这些变量会作为代码的运行的输入参数",
          "customInputConfig": {
            "selectValueTypeList": [
              "string",
              "number",
              "boolean",
              "object",
              "arrayString",
              "arrayNumber",
              "arrayBoolean",
              "arrayObject",
              "any",
              "chatHistory",
              "datasetQuote",
              "dynamic",
              "selectApp",
              "selectDataset"
            ],
            "showDescription": false,
            "showDefaultValue": true
          }
        },
        {
          "key": "codeType",
          "renderTypeList": [
            "hidden"
          ],
          "label": "",
          "value": "js"
        },
        {
          "key": "code",
          "renderTypeList": [
            "custom"
          ],
          "label": "",
          "value": "function main({textArray}){\n const groupSize = 20\n const delimiter = '<T>'\n\n  const result = [];\n\n  for (let i = 0; i < textArray.length; i += groupSize) {\n    result.push(textArray.slice(i, i + groupSize).join(delimiter));\n  }\n\n  return {result};\n}"
        },
        {
          "renderTypeList": [
            "reference"
          ],
          "valueType": "arrayString",
          "canEdit": true,
          "key": "textArray",
          "label": "textArray",
          "customInputConfig": {
            "selectValueTypeList": [
              "string",
              "number",
              "boolean",
              "object",
              "arrayString",
              "arrayNumber",
              "arrayBoolean",
              "arrayObject",
              "any",
              "chatHistory",
              "datasetQuote",
              "dynamic",
              "selectApp",
              "selectDataset"
            ],
            "showDescription": false,
            "showDefaultValue": true
          },
          "required": true,
          "value": [
            "kbr342XlxSZR",
            "zGYRMNA9xGuI"
          ]
        }
      ],
      "outputs": [
        {
          "id": "system_rawResponse",
          "key": "system_rawResponse",
          "label": "完整响应数据",
          "valueType": "object",
          "type": "static"
        },
        {
          "id": "error",
          "key": "error",
          "label": "运行错误",
          "description": "代码运行错误信息，成功时返回空",
          "valueType": "object",
          "type": "static"
        },
        {
          "id": "system_addOutputParam",
          "key": "system_addOutputParam",
          "type": "dynamic",
          "valueType": "dynamic",
          "label": "",
          "customFieldConfig": {
            "selectValueTypeList": [
              "string",
              "number",
              "boolean",
              "object",
              "arrayString",
              "arrayNumber",
              "arrayBoolean",
              "arrayObject",
              "any",
              "chatHistory",
              "datasetQuote",
              "dynamic",
              "selectApp",
              "selectDataset"
            ],
            "showDescription": false,
            "showDefaultValue": false
          },
          "description": "将代码中 return 的对象作为输出，传递给后续的节点。变量名需要对应 return 的 key"
        },
        {
          "id": "qLUQfhG0ILRX",
          "type": "dynamic",
          "key": "result",
          "valueType": "arrayString",
          "label": "result"
        }
      ]
    }
  ],
  "edges": [
    {
      "source": "bxz97Vg4Omux",
      "target": "yjFO3YcM7KG2",
      "sourceHandle": "bxz97Vg4Omux-source-right",
      "targetHandle": "yjFO3YcM7KG2-target-left"
    },
    {
      "source": "a2lqxASWi1vb",
      "target": "w4heEpNflz59",
      "sourceHandle": "a2lqxASWi1vb-source-right",
      "targetHandle": "w4heEpNflz59-target-left"
    },
    {
      "source": "w4heEpNflz59",
      "target": "izsNX8FXGD1t",
      "sourceHandle": "w4heEpNflz59-source-IF",
      "targetHandle": "izsNX8FXGD1t-target-left"
    },
    {
      "source": "448745",
      "target": "frjbsrlnJJsR",
      "sourceHandle": "448745-source-right",
      "targetHandle": "frjbsrlnJJsR-target-left"
    },
    {
      "source": "frjbsrlnJJsR",
      "target": "qlt9KJbbS9yJ",
      "sourceHandle": "frjbsrlnJJsR-source-right",
      "targetHandle": "qlt9KJbbS9yJ-target-left"
    },
    {
      "source": "tqzmK5oPR9BA",
      "target": "a2lqxASWi1vb",
      "sourceHandle": "tqzmK5oPR9BA-source-right",
      "targetHandle": "a2lqxASWi1vb-target-left"
    },
    {
      "source": "yjFO3YcM7KG2",
      "target": "vlNHndpNuFXB",
      "sourceHandle": "yjFO3YcM7KG2-source-right",
      "targetHandle": "vlNHndpNuFXB-target-left"
    },
    {
      "source": "ppPP6o7YYSTJ",
      "target": "tqzmK5oPR9BA",
      "sourceHandle": "ppPP6o7YYSTJ-source-right",
      "targetHandle": "tqzmK5oPR9BA-target-left"
    },
    {
      "source": "kbr342XlxSZR",
      "target": "y3WEYOQ09CGC",
      "sourceHandle": "kbr342XlxSZR-source-right",
      "targetHandle": "y3WEYOQ09CGC-target-left"
    },
    {
      "source": "y3WEYOQ09CGC",
      "target": "quYZgsW32ApA",
      "sourceHandle": "y3WEYOQ09CGC-source-right",
      "targetHandle": "quYZgsW32ApA-target-left"
    },
    {
      "source": "quYZgsW32ApA",
      "target": "bxz97Vg4Omux",
      "sourceHandle": "quYZgsW32ApA-source-right",
      "targetHandle": "bxz97Vg4Omux-target-left"
    },
    {
      "source": "w4heEpNflz59",
      "target": "quYZgsW32ApA",
      "sourceHandle": "w4heEpNflz59-source-ELSE",
      "targetHandle": "quYZgsW32ApA-target-left"
    },
    {
      "source": "qlt9KJbbS9yJ",
      "target": "kbr342XlxSZR",
      "sourceHandle": "qlt9KJbbS9yJ-source-IF",
      "targetHandle": "kbr342XlxSZR-target-left"
    },
    {
      "source": "qlt9KJbbS9yJ",
      "target": "dFxrGZS3Wmnz",
      "sourceHandle": "qlt9KJbbS9yJ-source-ELSE",
      "targetHandle": "dFxrGZS3Wmnz-target-right"
    },
    {
      "source": "vlNHndpNuFXB",
      "target": "ppPP6o7YYSTJ",
      "sourceHandle": "vlNHndpNuFXB-source-right",
      "targetHandle": "ppPP6o7YYSTJ-target-left"
    }
  ],
  "chatConfig": {
    "welcomeText": "你好，欢迎使用长字幕反思翻译机器人。\n\n在完成下方设置后，可以直接输入需要翻译的文本",
    "variables": [
      {
        "id": "v98n5b",
        "key": "source_lang",
        "label": "源语言",
        "type": "select",
        "required": true,
        "maxLen": 50,
        "enums": [
          {
            "value": "简体中文"
          },
          {
            "value": "繁體中文"
          },
          {
            "value": "English"
          },
          {
            "value": "Español"
          },
          {
            "value": "Français"
          },
          {
            "value": "Deutsch"
          },
          {
            "value": "Italiano"
          },
          {
            "value": "日本語"
          },
          {
            "value": "한국어"
          },
          {
            "value": "Русский"
          },
          {
            "value": "العربية"
          },
          {
            "value": "Bahasa Indonesia"
          },
          {
            "value": "Polski"
          }
        ],
        "icon": "core/app/variable/select"
      },
      {
        "id": "c3tvge",
        "key": "target_lang",
        "label": "目标语言",
        "type": "select",
        "required": true,
        "maxLen": 50,
        "enums": [
          {
            "value": "简体中文"
          },
          {
            "value": "繁體中文"
          },
          {
            "value": "English"
          },
          {
            "value": "Español"
          },
          {
            "value": "Français"
          },
          {
            "value": "Deutsch"
          },
          {
            "value": "Italiano"
          },
          {
            "value": "日本語"
          },
          {
            "value": "한국어"
          },
          {
            "value": "Русский"
          },
          {
            "value": "العربية"
          },
          {
            "value": "Bahasa Indonesia"
          },
          {
            "value": "Polski"
          }
        ],
        "icon": "core/app/variable/select"
      }
    ],
    "scheduledTriggerConfig": {
      "cronString": "",
      "timezone": "Asia/Shanghai",
      "defaultPrompt": ""
    },
    "_id": "6688b45317c65410d61d58aa"
  }
}
```

{{% /details %}}