---
title: "知识库集合标签"
description: "FastGPT 知识库集合标签使用说明"
icon: "developer_guide"
draft: false
toc: true
weight: 108
---

知识库集合标签是 FastGPT 商业版特有功能。它允许你对知识库中的数据集合添加标签进行分类，更高效地管理知识库数据。

而进一步可以在问答中，搜索知识库时添加集合过滤，实现更精确的搜索。

|                       |                       | |
| --------------------- | --------------------- | --------------------- |
| ![](/imgs/collection-tags-1.png) | ![](/imgs/collection-tags-2.png) | ![](/imgs/collection-tags-3.png) |

## 标签基础操作说明

在知识库详情页面，可以对标签进行管理，可执行的操作有

- 创建标签
- 修改标签名
- 删除标签
- 将一个标签赋给多个数据集合
- 给一个数据集合添加多个标签

也可以利用标签对数据集合进行筛选

## 知识库搜索-集合过滤说明

利用标签可以在知识库搜索时，通过填写「集合过滤」这一栏来实现更精确的搜索，具体的填写示例如下

```json
{
  "tags": {
    "$and": ["标签 1","标签 2"],
    "$or": ["有 $and 标签时，and 生效，or 不生效"]
  },
  "createTime": {
      "$gte": "YYYY-MM-DD HH:mm 格式即可，集合的创建时间大于该时间",
      "$lte": "YYYY-MM-DD HH:mm 格式即可，集合的创建时间小于该时间,可和 $gte 共同使用"
  }
}
```

在填写时有两个注意的点，

- 标签值可以为 `string` 类型的标签名，也可以为 `null`，而 `null` 代表着未设置标签的数据集合
- 标签过滤有 `$and` 和 `$or` 两种条件类型，在同时设置了 `$and` 和 `$or` 的情况下，只有 `$and` 会生效
