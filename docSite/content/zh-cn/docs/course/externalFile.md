---
title: '外部文件知识库'
description: 'FastGPT 外部文件知识库功能介绍和使用方式'
icon: 'language'
draft: false
toc: true
weight: 107
---

外部文件库是 FastGPT 商业版特有功能。它允许接入你现在的文件系统，无需将文件再导入一份到 FastGPT 中。

并且，阅读权限可以通过你的文件系统进行控制。

|                       |                       | |
| --------------------- | --------------------- | --------------------- |
| ![](/imgs/external_file0.png) | ![](/imgs/external_file1.png) | ![](/imgs/external_file2.png) | 


## 导入参数说明

- 外部预览地址：用于跳转你的文件阅读地址，会携带“文件阅读ID”进行访问。
- 文件访问URL：文件可访问的地址。
- 文件阅读ID：通常情况下，文件访问URL是临时的。如果希望永久可以访问，你需要使用该文件阅读ID，并配合上“外部预览地址”，跳转至新的阅读地址进行原文件访问。
- 文件名：默认会自动解析文件访问URL上的文件名。如果你手动填写，将会以手动填写的值为准。

[点击查看API导入文档](/docs/development/openapi/dataset/#创建一个外部文件库集合商业版)