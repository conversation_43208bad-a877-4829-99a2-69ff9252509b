---
title: "AI 相关参数配置说明"
description: "FastGPT AI 相关参数配置说明"
icon: "sign_language"
draft: false
toc: true
weight: 102
---

在 FastGPT 的 AI 对话模块中，有一个 AI 高级配置，里面包含了 AI 模型的参数配置，本文详细介绍这些配置的含义。

|  |  | |
| --- | --- | --- |
| ![](/imgs/aichat0.png) | ![](/imgs/aichat02.png) | ![](/imgs/aichat2.png) |

## 返回AI内容（高级编排特有）

这是一个开关，打开的时候，当 AI 对话模块运行时，会将其输出的内容返回到浏览器（API响应）；如果关闭，AI 输出的内容不会返回到浏览器，但是生成的内容仍可以通过【AI回复】进行输出。你可以将【AI回复】连接到其他模块中。

### 最大上下文

代表模型最多容纳的文字数量。

### 函数调用

支持函数调用的模型，在使用工具时更加准确。

### 温度 

越低回答越严谨，少废话（实测下来，感觉差别不大）

### 回复上限

最大回复 token 数量。注意，是回复的Tokens！不是上下文 tokens。

### 系统提示词

被放置在上下文数组的最前面，role 为 system，用于引导模型。

## 引用模板 & 引用提示词

这两个参数与知识库问答场景相关，可以控制知识库相关的提示词。

### AI 对话消息组成

想使用明白这两个变量，首先要了解传递传递给 AI 模型的消息格式。它是一个数组，FastGPT 中这个数组的组成形式为：

```json
[
    内置提示词（config.json 配置，一般为空）
    系统提示词 （用户输入的提示词）
    历史记录
    问题（由引用提示词、引用模板和用户问题组成）
]
```

{{% alert icon="🍅" context="success" %}}
Tips: 可以通过点击上下文按键查看完整的上下文组成，便于调试。
{{% /alert %}}

### 引用模板和提示词设计

引用模板和引用提示词通常是成对出现，引用提示词依赖引用模板。

FastGPT 知识库采用 QA 对(不一定都是问答格式，仅代表两个变量)的格式存储，在转义成字符串时候会根据**引用模板**来进行格式化。知识库包含多个可用变量： q, a, sourceId（数据的ID）, index(第n个数据), source(数据的集合名、文件名)，score(距离得分，0-1) 可以通过 {{q}} {{a}} {{sourceId}} {{index}} {{source}} {{score}} 按需引入。下面一个模板例子：

可以通过 [知识库结构讲解](/docs/course/dataset_engine/) 了解详细的知识库的结构。

#### 引用模板

```
{instruction:"{{q}}",output:"{{a}}",source:"{{source}}"}
```

搜索到的知识库，会自动将 q,a,source 替换成对应的内容。每条搜索到的内容，会通过 `\n` 隔开。例如：
```
{instruction:"电影《铃芽之旅》的导演是谁？",output:"电影《铃芽之旅》的导演是新海诚。",source:"手动输入"}
{instruction:"本作的主人公是谁？",output:"本作的主人公是名叫铃芽的少女。",source:""}
{instruction:"电影《铃芽之旅》男主角是谁？",output:"电影《铃芽之旅》男主角是宗像草太，由松村北斗配音。",source:""}
{instruction:"电影《铃芽之旅》的编剧是谁？22",output:"新海诚是本片的编剧。",source:"手动输入"}
```

#### 引用提示词

引用模板需要和引用提示词一起使用，提示词中可以写引用模板的格式说明以及对话的要求等。可以使用 {{quote}} 来使用 **引用模板**，使用 {{question}} 来引入问题。例如：

```
你的背景知识:
"""
{{quote}}
"""
对话要求：
1. 背景知识是最新的，其中 instruction 是相关介绍，output 是预期回答或补充。
2. 使用背景知识回答问题。
3. 背景知识无法回答问题时，你可以礼貌的的回答用户问题。
我的问题是:"{{question}}"
```

转义后则为：
```
你的背景知识:
"""
{instruction:"电影《铃芽之旅》的导演是谁？",output:"电影《铃芽之旅》的导演是新海诚。",source:"手动输入"}
{instruction:"本作的主人公是谁？",output:"本作的主人公是名叫铃芽的少女。",source:""}
{instruction:"电影《铃芽之旅》男主角是谁？",output:"电影《铃芽之旅》男主角是宗像草太，由松村北斗配音}
"""
对话要求：
1. 背景知识是最新的，其中 instruction 是相关介绍，output 是预期回答或补充。
2. 使用背景知识回答问题。
3. 背景知识无法回答问题时，你可以礼貌的的回答用户问题。
我的问题是:"{{question}}"
```

#### 总结

引用模板规定了搜索出来的内容如何组成一句话，其由 q,a,index,source 多个变量组成。

引用提示词由`引用模板`和`提示词`组成，提示词通常是对引用模板的一个描述，加上对模型的要求。

### 引用模板和提示词设计 示例

#### 通用模板与问答模板对比

我们通过一组`你是谁`的手动数据，对通用模板与问答模板的效果进行对比。此处特意打了个搞笑的答案，通用模板下 GPT35 就变得不那么听话了，而问答模板下 GPT35 依然能够回答正确。这是由于结构化的提示词，在大语言模型中具有更强的引导作用。

{{% alert icon="🍅" context="success" %}}
Tips: 建议根据不同的场景，每种知识库仅选择1类数据类型，这样有利于充分发挥提示词的作用。
{{% /alert %}}

| 通用模板配置及效果 | 问答模板配置及效果 |
| --- | --- |
| ![](/imgs/datasetprompt1.jpg) | ![](/imgs/datasetprompt2.jpg) |
| ![](/imgs/datasetprompt3.jpg) | ![](/imgs/datasetprompt5.jpg) |
| ![](/imgs/datasetprompt4.jpg) | ![](/imgs/datasetprompt6.jpg) |

#### 严格模板

使用非严格模板，我们随便询问一个不在知识库中的内容，模型通常会根据其自身知识进行回答。

| 非严格模板效果 | 选择严格模板 | 严格模板效果 |
| --- | --- | --- |
| ![](/imgs/datasetprompt7.webp) | ![](/imgs/datasetprompt8.jpg) |![](/imgs/datasetprompt9.jpg) |

#### 提示词设计思路

1. 使用序号进行不同要求描述。
2. 使用首先、然后、最后等词语进行描述。
3. 列举不同场景的要求时，尽量完整，不要遗漏。例如：背景知识完全可以回答、背景知识可以回答一部分、背景知识与问题无关，3种场景都说明清楚。
4. 巧用结构化提示，例如在问答模板中，利用了`instruction`和`output`，清楚的告诉模型，`output`是一个预期的答案。
5. 标点符号正确且完整。
