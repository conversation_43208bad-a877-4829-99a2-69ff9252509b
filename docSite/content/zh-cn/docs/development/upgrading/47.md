---
title: 'V4.7（需要初始化）'
description: 'FastGPT V4.7更新说明'
icon: 'upgrade'
draft: false
toc: true
weight: 826
---

## 1. 修改配置文件

增加一些 Boolean 值，用于决定不同功能块可以使用哪些模型，同时增加了模型的 logo：[点击查看最新的配置文件](/docs/development/configuration/)

## 2. 初始化脚本

升级完镜像后。从任意终端，发起 1 个 HTTP 请求。其中 {{rootkey}} 替换成环境变量里的 `rootkey`；{{host}} 替换成自己域名

```bash
curl --location --request POST 'https://{{host}}/api/admin/initv47' \
--header 'rootkey: {{rootkey}}' \
--header 'Content-Type: application/json'
```

脚本功能：
1. 初始化插件的 parentId

## 3. 升级 ReRank 模型

4.7对ReRank模型进行了格式变动，兼容 cohere 的格式，可以直接使用 cohere 提供的 API。如果是本地的 ReRank 模型，需要修改镜像为：`registry.cn-hangzhou.aliyuncs.com/fastgpt/bge-rerank-base:v0.1` 。

cohere的重排模型对中文不是很好，感觉不如 bge 的好用，接入教程如下：

1. 申请 Cohere 官方 Key: https://dashboard.cohere.com/api-keys
2. 修改 FastGPT 配置文件

```json
{
    "reRankModels": [
        {
            "model": "rerank-multilingual-v2.0", // 这里的 model 需要对应 cohere 的模型名
            "name": "检索重排", // 随意
            "requestUrl": "https://api.cohere.ai/v1/rerank",
            "requestAuth": "Coherer上申请的key"
        }
    ]
}
```


## V4.7 更新说明

1. 新增 - 工具调用模块，可以让LLM模型根据用户意图，动态的选择其他模型或插件执行。
2. 新增 - 分类和内容提取支持 functionCall 模式。部分模型支持 functionCall 不支持 ToolCall，也可以使用了。需要把 LLM 模型配置文件里的 `functionCall` 设置为 `true`， `toolChoice`设置为 `false`。如果 `toolChoice` 为 true，会走 tool 模式。
3. 新增 - HTTP插件，可实现OpenAPI快速生成插件。
4. 新增 - Rerank 模型兼容 [cohere的格式](https://docs.cohere.com/reference/rerank-1)，可以直接使用 cohere 的 rerank 模型。
5. 新增 - Helm 安装。
6. 优化 - 高级编排性能。
7. 优化 - 抽离 Flow controller 到 packages。
8. 优化 - AI模型选择。
9. 优化 - 手动输入知识库弹窗。
10. 优化 - 变量输入弹窗。
11. 优化 - docker 部署，自动初始化副本集。
12. 优化 - 浏览器读取文件自动推断编码，减少乱码情况。
13. 修复 - 开源版重排选不上。
14. 修复 - http 请求 body，不使用时，传入undefined。（会造成部分GET请求失败）
15. 新增 - 支持 http url 使用变量。
16. 修复 - 469 的提取的提示词容易造成幻觉。
17. 修复 - PG HNSW索引未实际生效问题，本次更新后，搜索速度大幅度提升(但是可能会出现精度损失，如果出现精度损失需要参考PgVector文档，对索引进行调整)。详细见：https://github.com/pgvector/pgvector?tab=readme-ov-file#troubleshooting
18. 修复Safari浏览器语音输入问题。
19. 修复 - 自定义分割规则可输入正则特殊字符（之前输入的话，会导致前端崩溃）
