---
title: 'V4.4.7（需执行升级脚本）'
description: 'FastGPT V4.4.7 更新（需执行升级脚本）'
icon: 'upgrade'
draft: false
toc: true
weight: 840
---

## 执行初始化 API

发起 1 个 HTTP 请求（{{rootkey}} 替换成环境变量里的`rootkey`，{{host}}替换成自己域名）

1. https://xxxxx/api/admin/initv447

```bash
curl --location --request POST 'https://{{host}}/api/admin/initv447' \
--header 'rootkey: {{rootkey}}' \
--header 'Content-Type: application/json'
```

初始化 pg 索引以及将 file_id 中空对象转成 manual 对象。如果数据多，可能需要较长时间，可以通过日志查看进度。

## 功能介绍

### Fast GPT V4.4.7

1. 优化了数据库文件 crud。
2. 兼容链接读取，作为 source。
3. 区分手动录入和标注，可追数据至某个文件。
4. 升级 openai sdk。
