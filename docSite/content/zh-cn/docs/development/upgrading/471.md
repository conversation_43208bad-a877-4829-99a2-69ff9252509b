---
title: 'V4.7.1(需要初始化)'
description: 'FastGPT V4.7.1 更新说明'
icon: 'upgrade'
draft: false
toc: true
weight: 825
---

## 初始化脚本

从任意终端，发起 1 个 HTTP 请求。其中 {{rootkey}} 替换成环境变量里的 `rootkey`；{{host}} 替换成FastGPT的域名。

```bash
curl --location --request POST 'https://{{host}}/api/admin/clearInvalidData' \
--header 'rootkey: {{rootkey}}' \
--header 'Content-Type: application/json'
```

该请求会执行脏数据清理（清理无效的文件、清理无效的图片、清理无效的知识库集合、清理无效的向量）


## 修改配置文件

增加了Laf环境配置：[点击查看最新的配置文件](/docs/development/configuration/)


## V4.7.1 更新说明

1. 新增 - 语音输入完整配置。支持选择是否打开语音输入（包括分享页面），支持语音输入后自动发送，支持语音输入后自动语音播放（流式）。
2. 新增 - pptx 和 xlsx 文件读取。但所有文件读取都放服务端，会消耗更多的服务器资源，以及无法在上传时预览更多内容。
3. 新增 - 集成 Laf 云函数，可以读取 Laf 账号中的云函数作为 HTTP 模块。
4. 新增 - 定时器，清理垃圾数据。（采用小范围清理，会清理最近n个小时的，所以请保证服务持续运行，长时间不允许，可以继续执行 clearInvalidData 的接口进行全量清理。）
5. 商业版新增 - 后台配置系统通知。
6. 优化 - 支持ip模式导出知识库。
7. 修改 - csv导入模板，取消 header 校验，自动获取前两列。
8. 修复 - 工具调用模块连线数据类型校验错误。
9. 修复 - 自定义索引输入时，解构数据失败。
10. 修复 - rerank 模型数据格式。
11. 修复 - 问题补全历史记录BUG
12. 修复 - 分享页面特殊情况下加载缓慢问题（由于ssr时候数据库不会触发连接）