---
title: 'V4.8.4(需要初始化)'
description: 'FastGPT V4.8.4 更新说明'
icon: 'upgrade'
draft: false
toc: true
weight: 820
---

## 升级指南

### 1. 修改镜像

- fastgpt 镜像 tag 修改成 v4.8.4
- fastgpt-sandbox 镜像 tag 修改成 v4.8.4 (选择性，无变更)
- 商业版镜像 tag 修改成 v4.8.4 

### 2. 商业版用户执行初始化

从任意终端，发起 1 个 HTTP 请求。其中 {{rootkey}} 替换成环境变量里的 `rootkey`；{{host}} 替换成**FastGPT 商业版的域名**。

```bash
curl --location --request POST 'https://{{host}}/api/admin/init/484' \
--header 'rootkey: {{rootkey}}' \
--header 'Content-Type: application/json'
```

## V4.8.4 更新说明

1. 新增 - 应用使用新权限系统。
2. 新增 - 应用支持文件夹。
3. 优化 - 文本分割增加连续换行、制表符清除，避免大文本性能问题。
4. 重要修复 - 修复系统插件运行池数据污染问题，由于从内存获取，会导致全局污染。
5. 修复 - Debug 模式下，相同 source 和 target 内容，导致连线显示异常。
6. 修复 - 定时执行初始化错误。
7. 修复 - 应用调用传参异常。
8. 修复 - ctrl + cv 复杂节点时，nodeId错误。 
9. 调整组件库全局theme。
