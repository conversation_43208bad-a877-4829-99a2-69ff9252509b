---
title: 'V4.6.7（需要初始化）'
description: 'FastGPT V4.6.7'
icon: 'upgrade'
draft: false
toc: true
weight: 829
---

## 1。执行初始化 API

发起 1 个 HTTP 请求 ({{rootkey}} 替换成环境变量里的 `rootkey`，{{host}} 替换成自己域名)

1. https://xxxxx/api/admin/initv467

```bash
curl --location --request POST 'https://{{host}}/api/admin/initv467' \
--header 'rootkey: {{rootkey}}' \
--header 'Content-Type: application/json'
```

初始化说明：
1. 将 images 重新关联到数据集
2. 设置 pg 表的 null 值。


## V4.6.7 更新说明

1. 修改了知识库UI及新的导入交互方式。
2. 优化知识库和对话的数据索引。
3. 知识库 openAPI，支持通过 [API 操作知识库](/docs/development/openapi/dataset)。
4. 新增 - 输入框变量提示。输入 { 号后将会获得可用变量提示。根据社区针对高级编排的反馈，我们计划于 2 月份的版本中，优化变量内容，支持模块的局部变量以及更多全局变量写入。
5. 优化 - 切换团队后会保存记录，下次登录时优先登录该团队。
6. 修复 - API 对话时，chatId 冲突问题。
7. 修复 - Iframe 嵌入网页可能导致的 window.onLoad 冲突。