---
title: 'V4.6.9(需要初始化)'
description: 'FastGPT V4.6.9更新说明'
icon: 'upgrade'
draft: false
toc: true
weight: 827
---

## 初始化脚本

从任意终端，发起 1 个 HTTP 请求。其中 {{rootkey}} 替换成环境变量里的 `rootkey`；{{host}} 替换成自己域名

```bash
curl --location --request POST 'https://{{host}}/api/admin/initv469' \
--header 'rootkey: {{rootkey}}' \
--header 'Content-Type: application/json'
```

1. 重置计量表。
2. 执行脏数据清理（清理无效的文件、清理无效的图片、清理无效的知识库集合、清理无效的向量）

## 外部接口更新

1. 由于计费系统变更，[分享链接对话上报接口](/docs/development/openapi/share/#5-编写对话结果上报接口可选)需要做一些调整，price字段被totalPoints字段取代。inputToken和outputToken不再提供，只提供`token`字段（总token数量）。

## V4.6.9 更新说明

1. 商业版新增 - 知识库新增“增强处理”训练模式，可生成更多类型索引。
2. 新增 - 完善了HTTP模块的变量提示。
3. 新增 - HTTP模块支持OpenAI单接口导入。
4. 新增 - 全局变量支持增加外部变量。可通过分享链接的Query或 API 的 variables 参数传入。
5. 新增 - 内容提取模块增加默认值。
6. 优化 - 问题补全。增加英文类型。同时可以设置为单独模块，方便复用。
7. 优化 - 重写了计量模式
8. 优化 - Token 过滤历史记录，保持偶数条，防止部分模型报错。
9. 优化 - 分享链接SEO，可直接展示应用名和头像。
10. 修复 - 标注功能。
11. 修复 - qa生成线程计数错误。
12. 修复 - 问题分类连线类型错误
