---
title: 'V4.6.4(需要初始化)'
description: 'FastGPT V4.6.4'
icon: 'upgrade'
draft: false
toc: true
weight: 832
---

## 1。执行初始化 API

发起 1 个 HTTP 请求 ({{rootkey}} 替换成环境变量里的 `rootkey`，{{host}} 替换成自己域名)

1. https://xxxxx/api/admin/initv464

```bash
curl --location --request POST 'https://{{host}}/api/admin/initv464' \
--header 'rootkey: {{rootkey}}' \
--header 'Content-Type: application/json'
```

初始化说明：
1. 初始化 PG 的createTime字段
2. 初始化 Mongo 中 chat 的 feedback 字段


## V4.6.4 功能介绍

1. 重写 - 分享链接身份逻辑，采用 localID 记录用户的ID。
2. 商业版新增 - 分享链接 SSO 方案，通过`身份鉴权`地址，仅需`3个接口`即可完全接入已有用户系统。具体参考[分享链接身份鉴权](/docs/development/openapi/share/)
3. 新增 - 分享链接更多嵌入方式提示，更多DIY方式。
4. 优化 - 历史记录模块。弃用旧的历史记录模块，直接在对应地方填写数值即可。
5. 调整 - 知识库搜索模块 topk 逻辑，采用 MaxToken 计算，兼容不同长度的文本块
6. 调整鉴权顺序，提高 apikey 的优先级，避免cookie抢占 apikey 的鉴权。
7. 链接读取支持多选择器。参考[Web 站点同步用法](/docs/course/websync)
8. 修复 - 分享链接图片上传鉴权问题
9. 修复 - Mongo 连接池未释放问题。
10. 修复 - Dataset Intro 无法更新
11. 修复 - md 代码块问题
12. 修复 - root 权限问题
13. 优化 docker file  


