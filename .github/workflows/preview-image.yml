name: Preview FastGPT images
on:
  pull_request_target:
    paths:
      - 'projects/app/**'
      - 'packages/**'
  workflow_dispatch:

jobs:
  preview-fastgpt-images:
    runs-on: ubuntu-20.04
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          ref: ${{ github.event.pull_request.head.ref }}
          repository: ${{ github.event.pull_request.head.repo.full_name }}
          submodules: recursive # Fetch submodules
          fetch-depth: 0 # Fetch all history for .GitInfo and .Lastmod
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
        with:
          driver-opts: network=host
      - name: Cache Docker layers
        uses: actions/cache@v3
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-buildx-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-buildx-
      - name: Login to GitHub Container Registry
        uses: docker/login-action@v2
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GH_PAT }}
      - name: Set DOCKER_REPO_TAGGED based on branch or tag
        run: |
          echo "DOCKER_REPO_TAGGED=ghcr.io/${{ github.repository_owner }}/fastgpt-pr:${{ github.event.pull_request.number }}" >> $GITHUB_ENV
      - name: Build image for PR
        env:
          DOCKER_REPO_TAGGED: ${{ env.DOCKER_REPO_TAGGED }}
        run: |
          docker buildx build \
          -f projects/app/Dockerfile \
          --label "org.opencontainers.image.source=https://github.com/${{ github.repository_owner }}/FastGPT" \
          --label "org.opencontainers.image.description=fastgpt-pr imae" \
          --label "org.opencontainers.image.licenses=Apache" \
          --push \
          --cache-from=type=local,src=/tmp/.buildx-cache \
          --cache-to=type=local,dest=/tmp/.buildx-cache \
          -t ${DOCKER_REPO_TAGGED} \
          .

  helm-check:
    runs-on: ubuntu-20.04
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - name: Helm Check
        run: |
          helm dependency update files/helm/fastgpt
          helm lint files/helm/fastgpt
          helm package files/helm/fastgpt
