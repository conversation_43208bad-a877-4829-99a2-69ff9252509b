name: Preview FastGPT docs

on:
  pull_request_target:
    paths:
      - 'docSite/**'
    branches:
      - 'main'
  workflow_dispatch:

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  # This workflow contains jobs "deploy-production"
  deploy-preview:
    # The environment this job references
    environment:
      name: Preview
      url: ${{ steps.vercel-action.outputs.preview-url }}

    # The type of runner that the job will run on
    runs-on: ubuntu-22.04

    # Job outputs
    outputs:
      url: ${{ steps.vercel-action.outputs.preview-url }}

    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
      # Step 1 - Checks-out your repository under $GITHUB_WORKSPACE
      - name: Checkout
        uses: actions/checkout@v3
        with:
          ref: ${{ github.event.pull_request.head.ref }}
          repository: ${{ github.event.pull_request.head.repo.full_name }}
          submodules: recursive # Fetch submodules
          fetch-depth: 0 # Fetch all history for .GitInfo and .Lastmod

      # Step 2 Detect changes to Docs Content
      - name: Detect changes in doc content
        uses: dorny/paths-filter@v2
        id: filter
        with:
          filters: |
            docs:
              - 'docSite/content/docs/**'
          base: main

      - name: Add cdn for images
        run: |
          sed -i "s#\](/imgs/#\](https://cdn.jsdelivr.net/gh/yangchuansheng/fastgpt-imgs@main/imgs/#g" $(grep -rl "\](/imgs/" docSite/content/zh-cn/docs)

      # Step 3 - Install Hugo (specific version)
      - name: Install Hugo
        uses: peaceiris/actions-hugo@v2
        with:
          hugo-version: '0.117.0'
          extended: true

      # Step 4 - Builds the site using Hugo
      - name: Build
        run: cd docSite && hugo mod get -u github.com/colinwilson/lotusdocs && hugo -v --minify

      # Step 5 - Push our generated site to Vercel
      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v25
        id: vercel-action
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }} # Required
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }} #Required
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }} #Required
          github-comment: false
          vercel-args: '--local-config ../vercel.json' # Optional
          working-directory: docSite/public
          alias-domains: | #Optional
            fastgpt-staging.vercel.app
  docsOutput:
    needs: [deploy-preview]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          ref: ${{ github.event.pull_request.head.ref }}
          repository: ${{ github.event.pull_request.head.repo.full_name }}
      - name: Write md
        run: |
          echo "# 🤖 Generated by deploy action" > report.md
          echo "[👀 Visit Preview](${{ needs.deploy-preview.outputs.url }})" >> report.md
          cat report.md
      - name: Gh Rebot for Sealos
        uses: labring/gh-rebot@v0.0.6
        if: ${{ (github.event_name == 'pull_request_target') }}
        with:
          version: v0.0.6
        env:
          GH_TOKEN: '${{ secrets.GH_PAT }}'
          SEALOS_TYPE: 'pr_comment'
          SEALOS_FILENAME: 'report.md'
          SEALOS_REPLACE_TAG: 'DEFAULT_REPLACE_DEPLOY'
