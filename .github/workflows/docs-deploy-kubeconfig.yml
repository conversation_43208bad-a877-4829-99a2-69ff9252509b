name: Deploy image by kubeconfig
on:
  workflow_dispatch:
  push:
    paths:
      - 'docSite/**'
    branches:
      - 'main'
    tags:
      - 'v*.*.*'

jobs:
  build-fastgpt-docs-images:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Change baseURL
        run: sed -i 's|^baseURL =.*|baseURL = "https://doc.fastgpt.cn"|g' ./docSite/hugo.toml

      - name: Get current date and time
        id: datetime
        run: echo "datetime=$(date +'%Y%m%d%H%M%S')" >> "$GITHUB_OUTPUT"

      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          # list of Docker images to use as base name for tags
          images: |
            ${{ secrets.DOCKER_HUB_NAME }}/fastgpt-docs
            ghcr.io/${{ github.repository_owner }}/fastgpt-docs
            registry.cn-hangzhou.aliyuncs.com/${{ secrets.ALI_HUB_USERNAME }}/fastgpt-docs
          tags: |
            ${{ steps.datetime.outputs.datetime }}
          flavor: latest=false

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_HUB_NAME }}
          password: ${{ secrets.DOCKER_HUB_PASSWORD }}

      - name: Login to ghcr.io
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GH_PAT }}

      - name: Login to Aliyun
        uses: docker/login-action@v3
        with:
          registry: registry.cn-hangzhou.aliyuncs.com
          username: ${{ secrets.ALI_HUB_USERNAME }}
          password: ${{ secrets.ALI_HUB_PASSWORD }}

      - name: Build and push Docker images to ghcr.io and DockerHub
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./docSite/Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
    outputs:
      tags: ${{ steps.datetime.outputs.datetime }}
  update-docs-image:
    needs: build-fastgpt-docs-images
    runs-on: ubuntu-20.04
    if: github.repository == 'labring/FastGPT'
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      - uses: actions-hub/kubectl@master
        env:
          KUBE_CONFIG: ${{ secrets.KUBE_CONFIG }}
        with:
          args: set image deployment/fastgpt-docs fastgpt-docs=registry.cn-hangzhou.aliyuncs.com/${{ secrets.ALI_HUB_USERNAME }}/fastgpt-docs:${{ needs.build-fastgpt-docs-images.outputs.tags }}
      - uses: actions-hub/kubectl@master
        env:
          KUBE_CONFIG: ${{ secrets.KUBE_CONFIG }}
        with:
          args: annotate deployment/fastgpt-docs originImageName="registry.cn-hangzhou.aliyuncs.com/${{ secrets.ALI_HUB_USERNAME }}/fastgpt-docs:${{ needs.build-fastgpt-docs-images.outputs.tags }}" --overwrite
