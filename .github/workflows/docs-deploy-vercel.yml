name: Deploy image to vercel

on:
  workflow_dispatch:
  push:
    paths:
      - 'docSite/**'
    branches:
      - 'main'
    tags:
      - 'v*.*.*'

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  # This workflow contains jobs "deploy-production"
  deploy-production:
    # The environment this job references
    environment:
      name: Production
      url: ${{ steps.vercel-action.outputs.preview-url }}

    # The type of runner that the job will run on
    runs-on: ubuntu-22.04

    # Job outputs
    outputs:
      docs: ${{ steps.filter.outputs.docs }}

    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
      # Step 1 - Checks-out your repository under $GITHUB_WORKSPACE
      - name: Checkout
        uses: actions/checkout@v3
        with:
          submodules: recursive # Fetch submodules
          fetch-depth: 0 # Fetch all history for .GitInfo and .Lastmod

      # Step 2 Detect changes to Docs Content
      - name: Detect changes in doc content
        uses: dorny/paths-filter@v2
        id: filter
        with:
          filters: |
            docs:
              - 'docSite/content/docs/**'
          base: main

      - name: Add cdn for images
        run: |
          sed -i "s#\](/imgs/#\](https://cdn.jsdelivr.net/gh/yangchuansheng/fastgpt-imgs@main/imgs/#g" $(grep -rl "\](/imgs/" docSite/content/zh-cn/docs)

      # Step 3 - Install Hugo (specific version)
      - name: Install Hugo
        uses: peaceiris/actions-hugo@v2
        with:
          hugo-version: '0.117.0'
          extended: true

      # Step 4 - Builds the site using Hugo
      - name: Build
        run: cd docSite && hugo mod get -u github.com/colinwilson/lotusdocs && hugo -v --minify

      # Step 5 - Push our generated site to Vercel
      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v25
        id: vercel-action
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }} # Required
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }} #Required
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }} #Required
          github-comment: false
          vercel-args: '--prod --local-config ../vercel.json' # Optional
          working-directory: docSite/public

      - name: Deploy to GitHub Pages
        uses: peaceiris/actions-gh-pages@v3
        with:
          github_token: ${{ secrets.GH_PAT }}
          publish_dir: docSite/public
