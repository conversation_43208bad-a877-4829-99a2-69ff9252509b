[{"id": "pluginInput", "templateType": "systemInput", "flowNodeType": "pluginInput", "sourceHandle": {"top": false, "right": true, "bottom": false, "left": false}, "targetHandle": {"top": false, "right": false, "bottom": false, "left": false}, "unique": true, "forbidDelete": true, "avatar": "core/workflow/template/workflowStart", "name": "插件输入", "intro": "可以配置插件需要哪些输入，利用这些输入来运行插件", "showStatus": false, "version": "481", "inputs": [], "outputs": []}, {"id": "pluginOutput", "templateType": "systemInput", "flowNodeType": "pluginOutput", "sourceHandle": {"top": false, "right": false, "bottom": false, "left": false}, "targetHandle": {"top": false, "right": false, "bottom": false, "left": true}, "unique": true, "forbidDelete": true, "avatar": "core/workflow/template/pluginOutput", "name": "自定义插件输出", "intro": "自定义配置外部输出，使用插件时，仅暴露自定义配置的输出", "showStatus": false, "version": "481", "inputs": [], "outputs": []}, {"id": "chatNode", "templateType": "ai", "flowNodeType": "chatNode", "sourceHandle": {"top": true, "right": true, "bottom": true, "left": true}, "targetHandle": {"top": true, "right": true, "bottom": true, "left": true}, "avatar": "core/workflow/template/aiChat", "name": "workflow:template.ai_chat", "intro": "workflow:template.ai_chat_intro", "showStatus": true, "isTool": true, "version": "481", "inputs": [{"key": "model", "renderTypeList": ["settingLLMModel", "reference"], "label": "core.module.input.label.aiModel", "valueType": "string"}, {"key": "temperature", "renderTypeList": ["hidden"], "label": "", "value": 0, "valueType": "number"}, {"key": "maxToken", "renderTypeList": ["hidden"], "label": "", "value": 2000, "valueType": "number"}, {"key": "isResponseAnswerText", "renderTypeList": ["hidden"], "label": "", "value": true, "valueType": "boolean"}, {"key": "quoteTemplate", "renderTypeList": ["hidden"], "label": "", "valueType": "string"}, {"key": "quotePrompt", "renderTypeList": ["hidden"], "label": "", "valueType": "string"}, {"key": "aiChatVision", "renderTypeList": ["hidden"], "label": "", "valueType": "boolean", "value": true}, {"key": "systemPrompt", "renderTypeList": ["textarea", "reference"], "max": 3000, "valueType": "string", "label": "core.ai.Prompt", "description": "core.app.tip.chatNodeSystemPromptTip", "placeholder": "core.app.tip.chatNodeSystemPromptTip"}, {"key": "history", "renderTypeList": ["numberInput", "reference"], "valueType": "chatHistory", "label": "core.module.input.label.chat history", "description": "最多携带多少轮对话记录", "required": true, "min": 0, "max": 50, "value": 6}, {"key": "quoteQA", "renderTypeList": ["settingDatasetQuotePrompt"], "label": "", "debugLabel": "core.workflow.Dataset quote", "description": "", "valueType": "datasetQuote"}, {"key": "stringQuoteText", "renderTypeList": ["reference", "textarea"], "label": "app:document_quote", "debugLabel": "app:document_quote", "description": "app:document_quote_tip", "valueType": "string"}, {"key": "userChatInput", "renderTypeList": ["reference", "textarea"], "valueType": "string", "label": "用户问题", "required": true, "toolDescription": "用户问题"}], "outputs": [{"id": "history", "key": "history", "required": true, "label": "core.module.output.label.New context", "description": "core.module.output.description.New context", "valueType": "chatHistory", "type": "static"}, {"id": "answerText", "key": "answerText", "required": true, "label": "core.module.output.label.Ai response content", "description": "core.module.output.description.Ai response content", "valueType": "string", "type": "static"}]}, {"id": "textEditor", "templateType": "tools", "flowNodeType": "textEditor", "sourceHandle": {"top": true, "right": true, "bottom": true, "left": true}, "targetHandle": {"top": true, "right": true, "bottom": true, "left": true}, "avatar": "core/workflow/template/textConcat", "name": "文本拼接", "intro": "可对固定或传入的文本进行加工后输出，非字符串类型数据最终会转成字符串类型。", "version": "486", "inputs": [{"key": "system_addInputParam", "renderTypeList": ["addInputParam"], "valueType": "dynamic", "label": "", "required": false, "description": "可以引用其他节点的输出，作为文本拼接的变量，通过 {{字段名}} 来引用变量", "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": false}}, {"key": "system_textareaInput", "renderTypeList": ["textarea"], "valueType": "string", "required": true, "label": "拼接文本", "placeholder": "可通过 {{字段名}} 来引用变量"}], "outputs": [{"id": "system_text", "key": "system_text", "label": "拼接结果", "type": "static", "valueType": "string"}]}, {"id": "answerNode", "templateType": "tools", "flowNodeType": "answerNode", "sourceHandle": {"top": true, "right": true, "bottom": true, "left": true}, "targetHandle": {"top": true, "right": true, "bottom": true, "left": true}, "avatar": "core/workflow/template/reply", "name": "指定回复", "intro": "该模块可以直接回复一段指定的内容。常用于引导、提示。非字符串内容传入时，会转成字符串进行输出。", "version": "481", "isTool": true, "inputs": [{"key": "text", "renderTypeList": ["textarea", "reference"], "valueType": "any", "required": true, "label": "core.module.input.label.Response content", "description": "core.module.input.description.Response content", "placeholder": "core.module.input.description.Response content"}], "outputs": []}, {"id": "datasetSearchNode", "templateType": "ai", "flowNodeType": "datasetSearchNode", "sourceHandle": {"top": true, "right": true, "bottom": true, "left": true}, "targetHandle": {"top": true, "right": true, "bottom": true, "left": true}, "avatar": "core/workflow/template/datasetSearch", "name": "workflow:template.dataset_search", "intro": "workflow:template.dataset_search_intro", "showStatus": true, "isTool": true, "version": "481", "inputs": [{"key": "datasets", "renderTypeList": ["selectDataset", "reference"], "label": "core.module.input.label.Select dataset", "value": [], "valueType": "selectDataset", "required": true}, {"key": "similarity", "renderTypeList": ["selectDatasetParamsModal"], "label": "", "value": 0.4, "valueType": "number"}, {"key": "limit", "renderTypeList": ["hidden"], "label": "", "value": 1500, "valueType": "number"}, {"key": "searchMode", "renderTypeList": ["hidden"], "label": "", "valueType": "string", "value": "embedding"}, {"key": "usingReRank", "renderTypeList": ["hidden"], "label": "", "valueType": "boolean", "value": false}, {"key": "datasetSearchUsingExtensionQuery", "renderTypeList": ["hidden"], "label": "", "valueType": "boolean", "value": true}, {"key": "datasetSearchExtensionModel", "renderTypeList": ["hidden"], "label": "", "valueType": "string"}, {"key": "datasetSearchExtensionBg", "renderTypeList": ["hidden"], "label": "", "valueType": "string", "value": ""}, {"key": "userChatInput", "renderTypeList": ["reference", "textarea"], "valueType": "string", "label": "用户问题", "required": true, "toolDescription": "需要检索的内容"}, {"key": "collectionFilterMatch", "renderTypeList": ["JSONEditor", "reference"], "label": "集合元数据过滤", "valueType": "object", "isPro": true, "description": "目前支持标签和创建时间过滤，需按照以下格式填写：\n{\n  \"tags\": {\n    \"$and\": [\"标签 1\",\"标签 2\"],\n    \"$or\": [\"有 $and 标签时，and 生效，or 不生效\"]\n  },\n  \"createTime\": {\n      \"$gte\": \"YYYY-MM-DD HH:mm 格式即可，集合的创建时间大于该时间\",\n      \"$lte\": \"YYYY-MM-DD HH:mm 格式即可，集合的创建时间小于该时间,可和 $gte 共同使用\"\n  }\n}\n      "}], "outputs": [{"id": "quoteQA", "key": "quoteQA", "label": "core.module.Dataset quote.label", "description": "特殊数组格式，搜索结果为空时，返回空数组。", "type": "static", "valueType": "datasetQuote"}]}, {"id": "datasetConcatNode", "flowNodeType": "datasetConcatNode", "templateType": "other", "sourceHandle": {"top": true, "right": true, "bottom": true, "left": true}, "targetHandle": {"top": true, "right": true, "bottom": true, "left": true}, "avatar": "core/workflow/template/datasetConcat", "name": "知识库搜索引用合并", "intro": "可以将多个知识库搜索结果进行合并输出。使用 RRF 的合并方式进行最终排序输出。", "showStatus": false, "version": "486", "inputs": [{"key": "limit", "renderTypeList": ["custom"], "label": "最大 Tokens", "value": 3000, "valueType": "number"}, {"key": "system_datasetQuoteList", "renderTypeList": ["custom"], "label": ""}], "outputs": [{"id": "quoteQA", "key": "quoteQA", "label": "core.module.Dataset quote.label", "type": "static", "valueType": "datasetQuote"}]}, {"id": "tools", "flowNodeType": "tools", "templateType": "ai", "sourceHandle": {"top": true, "right": true, "bottom": false, "left": true}, "targetHandle": {"top": true, "right": true, "bottom": false, "left": true}, "avatar": "core/workflow/template/toolCall", "name": "workflow:template.tool_call", "intro": "workflow:template.tool_call_intro", "showStatus": true, "version": "481", "inputs": [{"key": "model", "renderTypeList": ["settingLLMModel", "reference"], "label": "core.module.input.label.aiModel", "valueType": "string", "llmModelType": "all"}, {"key": "temperature", "renderTypeList": ["hidden"], "label": "", "value": 0, "valueType": "number"}, {"key": "maxToken", "renderTypeList": ["hidden"], "label": "", "value": 2000, "valueType": "number"}, {"key": "isResponseAnswerText", "renderTypeList": ["hidden"], "label": "", "value": true, "valueType": "boolean"}, {"key": "aiChatVision", "renderTypeList": ["hidden"], "label": "", "valueType": "boolean", "value": true}, {"key": "systemPrompt", "renderTypeList": ["textarea", "reference"], "max": 3000, "valueType": "string", "label": "core.ai.Prompt", "description": "core.app.tip.chatNodeSystemPromptTip", "placeholder": "core.app.tip.chatNodeSystemPromptTip"}, {"key": "history", "renderTypeList": ["numberInput", "reference"], "valueType": "chatHistory", "label": "core.module.input.label.chat history", "description": "最多携带多少轮对话记录", "required": true, "min": 0, "max": 50, "value": 6}, {"key": "userChatInput", "renderTypeList": ["reference", "textarea"], "valueType": "string", "label": "用户问题", "required": true}], "outputs": [{"id": "answerText", "key": "answerText", "label": "core.module.output.label.Ai response content", "description": "core.module.output.description.Ai response content", "valueType": "string", "type": "static"}]}, {"id": "stopTool", "templateType": "ai", "flowNodeType": "stopTool", "sourceHandle": {"top": false, "right": false, "bottom": false, "left": false}, "targetHandle": {"top": true, "right": true, "bottom": true, "left": true}, "avatar": "core/workflow/template/stopTool", "name": "工具调用终止", "intro": "该模块需配置工具调用使用。当该模块被执行时，本次工具调用将会强制结束，并且不再调用AI针对工具调用结果回答问题。", "version": "481", "inputs": [], "outputs": []}, {"id": "classifyQuestion", "templateType": "ai", "flowNodeType": "classifyQuestion", "sourceHandle": {"top": false, "right": false, "bottom": false, "left": false}, "targetHandle": {"top": true, "right": false, "bottom": true, "left": true}, "avatar": "core/workflow/template/questionClassify", "name": "问题分类", "intro": "根据用户的历史记录和当前问题判断该次提问的类型。可以添加多组问题类型，下面是一个模板例子：\n类型1: 打招呼\n类型2: 关于商品“使用”问题\n类型3: 关于商品“购买”问题\n类型4: 其他问题", "showStatus": true, "version": "481", "inputs": [{"key": "model", "renderTypeList": ["selectLLMModel", "reference"], "label": "core.module.input.label.aiModel", "required": true, "valueType": "string", "llmModelType": "all"}, {"key": "systemPrompt", "renderTypeList": ["textarea", "reference"], "max": 3000, "valueType": "string", "label": "core.module.input.label.Background", "description": "core.module.input.description.Background", "placeholder": "core.module.input.placeholder.Classify background"}, {"key": "history", "renderTypeList": ["numberInput", "reference"], "valueType": "chatHistory", "label": "core.module.input.label.chat history", "description": "最多携带多少轮对话记录", "required": true, "min": 0, "max": 50, "value": 6}, {"key": "userChatInput", "renderTypeList": ["reference", "textarea"], "valueType": "string", "label": "用户问题", "required": true}, {"key": "agents", "renderTypeList": ["custom"], "valueType": "any", "label": "", "value": [{"value": "打招呼", "key": "wqre"}, {"value": "关于 xxx 的问题", "key": "sdfa"}, {"value": "其他问题", "key": "agex"}]}], "outputs": [{"id": "cqResult", "key": "cqResult", "required": true, "label": "分类结果", "valueType": "string", "type": "static"}]}, {"id": "contentExtract", "templateType": "ai", "flowNodeType": "contentExtract", "sourceHandle": {"top": true, "right": true, "bottom": true, "left": true}, "targetHandle": {"top": true, "right": true, "bottom": true, "left": true}, "avatar": "core/workflow/template/extractJson", "name": "文本内容提取", "intro": "可从文本中提取指定的数据，例如：sql语句、搜索关键词、代码等", "showStatus": true, "isTool": true, "version": "481", "inputs": [{"key": "model", "renderTypeList": ["selectLLMModel", "reference"], "label": "core.module.input.label.aiModel", "required": true, "valueType": "string", "llmModelType": "extractFields"}, {"key": "description", "renderTypeList": ["textarea", "reference"], "valueType": "string", "label": "提取要求描述", "description": "给AI一些对应的背景知识或要求描述，引导AI更好的完成任务。\n该输入框可使用全局变量。", "placeholder": "例如: \n1. 当前时间为: {{cTime}}。你是一个实验室预约助手，你的任务是帮助用户预约实验室，从文本中获取对应的预约信息。\n2. 你是谷歌搜索助手，需要从文本中提取出合适的搜索词。"}, {"key": "history", "renderTypeList": ["numberInput", "reference"], "valueType": "chatHistory", "label": "core.module.input.label.chat history", "description": "最多携带多少轮对话记录", "required": true, "min": 0, "max": 50, "value": 6}, {"key": "content", "renderTypeList": ["reference", "textarea"], "label": "需要提取的文本", "required": true, "valueType": "string", "toolDescription": "需要检索的内容"}, {"key": "extractKeys", "renderTypeList": ["custom"], "label": "", "valueType": "any", "description": "由 '描述' 和 'key' 组成一个目标字段，可提取多个目标字段", "value": []}], "outputs": [{"id": "success", "key": "success", "label": "字段完全提取", "required": true, "description": "提取字段全部填充时返回 true （模型提取或使用默认值均属于成功）", "valueType": "boolean", "type": "static"}, {"id": "fields", "key": "fields", "label": "完整提取结果", "required": true, "description": "一个 JSON 字符串，例如：{\"name:\":\"YY\",\"Time\":\"2023/7/2 18:00\"}", "valueType": "string", "type": "static"}]}, {"id": "readFiles", "templateType": "tools", "flowNodeType": "readFiles", "sourceHandle": {"top": true, "right": true, "bottom": true, "left": true}, "targetHandle": {"top": true, "right": true, "bottom": true, "left": true}, "avatar": "core/workflow/template/readFiles", "name": "app:workflow.read_files", "intro": "app:workflow.read_files_tip", "showStatus": true, "version": "489", "isTool": true, "inputs": [{"key": "fileUrlList", "renderTypeList": ["reference"], "valueType": "arrayString", "label": "app:workflow.file_url", "required": true, "value": []}], "outputs": [{"id": "system_text", "key": "system_text", "label": "app:workflow.read_files_result", "description": "app:workflow.read_files_result_desc", "valueType": "string", "type": "static"}]}, {"id": "httpRequest468", "templateType": "tools", "flowNodeType": "httpRequest468", "sourceHandle": {"top": true, "right": true, "bottom": true, "left": true}, "targetHandle": {"top": true, "right": true, "bottom": true, "left": true}, "avatar": "core/workflow/template/httpRequest", "name": "HTTP 请求", "intro": "可以发出一个 HTTP 请求，实现更为复杂的操作（联网搜索、数据库查询等）", "showStatus": true, "isTool": true, "version": "481", "inputs": [{"key": "system_addInputParam", "renderTypeList": ["addInputParam"], "valueType": "dynamic", "label": "", "required": false, "description": "core.module.input.description.HTTP Dynamic Input", "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": true}}, {"key": "system_httpMethod", "renderTypeList": ["custom"], "valueType": "string", "label": "", "value": "POST", "required": true}, {"key": "system_httpReqUrl", "renderTypeList": ["hidden"], "valueType": "string", "label": "", "description": "core.module.input.description.Http Request Url", "placeholder": "https://api.ai.com/getInventory", "required": false}, {"key": "system_httpHeader", "renderTypeList": ["custom"], "valueType": "any", "value": [], "label": "", "description": "core.module.input.description.Http Request Header", "placeholder": "core.module.input.description.Http Request Header", "required": false}, {"key": "system_httpParams", "renderTypeList": ["hidden"], "valueType": "any", "value": [], "label": "", "required": false}, {"key": "system_httpJsonBody", "renderTypeList": ["hidden"], "valueType": "any", "value": "", "label": "", "required": false}], "outputs": [{"id": "system_addOutputParam", "key": "system_addOutputParam", "type": "dynamic", "valueType": "dynamic", "label": "", "customFieldConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": false}}, {"id": "error", "key": "error", "label": "请求错误", "description": "HTTP请求错误信息，成功时返回空", "valueType": "object", "type": "static"}, {"id": "httpRawResponse", "key": "httpRawResponse", "label": "原始响应", "required": true, "description": "HTTP请求的原始响应。只能接受字符串或JSON类型响应数据。", "valueType": "any", "type": "static"}]}, {"id": "cfr", "templateType": "other", "flowNodeType": "cfr", "sourceHandle": {"top": true, "right": true, "bottom": true, "left": true}, "targetHandle": {"top": true, "right": true, "bottom": true, "left": true}, "avatar": "core/workflow/template/queryExtension", "name": "问题优化", "intro": "使用问题优化功能，可以提高知识库连续对话时搜索的精度。使用该功能后，会先利用 AI 根据上下文构建一个或多个新的检索词，这些检索词更利于进行知识库搜索。该模块已内置在知识库搜索模块中，如果您仅进行一次知识库搜索，可直接使用知识库内置的补全功能。", "showStatus": true, "version": "481", "inputs": [{"key": "model", "renderTypeList": ["selectLLMModel", "reference"], "label": "core.module.input.label.aiModel", "required": true, "valueType": "string", "llmModelType": "queryExtension"}, {"key": "systemPrompt", "renderTypeList": ["textarea", "reference"], "label": "core.app.edit.Query extension background prompt", "max": 300, "valueType": "string", "description": "core.app.edit.Query extension background tip", "placeholder": "core.module.QueryExtension.placeholder"}, {"key": "history", "renderTypeList": ["numberInput", "reference"], "valueType": "chatHistory", "label": "core.module.input.label.chat history", "description": "最多携带多少轮对话记录", "required": true, "min": 0, "max": 50, "value": 6}, {"key": "userChatInput", "renderTypeList": ["reference", "textarea"], "valueType": "string", "label": "用户问题", "required": true}], "outputs": [{"id": "system_text", "key": "system_text", "label": "core.module.output.label.query extension result", "description": "core.module.output.description.query extension result", "valueType": "string", "type": "static"}]}, {"id": "lafModule", "templateType": "other", "flowNodeType": "lafModule", "sourceHandle": {"top": true, "right": true, "bottom": true, "left": true}, "targetHandle": {"top": true, "right": true, "bottom": true, "left": true}, "avatar": "core/workflow/template/lafDispatch", "name": "Laf 函数调用（测试）", "intro": "可以调用Laf账号下的云函数。", "showStatus": true, "isTool": true, "version": "481", "inputs": [{"key": "system_addInputParam", "renderTypeList": ["addInputParam"], "valueType": "dynamic", "label": "", "required": false, "description": "接收前方节点的输出值作为变量，这些变量可以被 Laf 请求参数使用。", "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": true}}, {"key": "system_httpReqUrl", "renderTypeList": ["hidden"], "valueType": "string", "label": "", "description": "core.module.input.description.Http Request Url", "placeholder": "https://api.ai.com/getInventory", "required": false}], "outputs": [{"id": "httpRawResponse", "key": "httpRawResponse", "label": "原始响应", "description": "HTTP请求的原始响应。只能接受字符串或JSON类型响应数据。", "valueType": "any", "type": "static"}, {"id": "system_addOutputParam", "key": "system_addOutputParam", "type": "dynamic", "valueType": "dynamic", "label": "", "customFieldConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": false}}]}, {"id": "ifElseNode", "templateType": "tools", "flowNodeType": "ifElseNode", "sourceHandle": {"top": false, "right": false, "bottom": false, "left": false}, "targetHandle": {"top": true, "right": false, "bottom": true, "left": true}, "avatar": "core/workflow/template/ifelse", "name": "判断器", "intro": "根据一定的条件，执行不同的分支。", "showStatus": true, "version": "481", "inputs": [{"key": "ifElseList", "renderTypeList": ["hidden"], "valueType": "any", "label": "", "value": [{"condition": "AND", "list": [{}]}]}], "outputs": [{"id": "ifElseResult", "key": "ifElseResult", "label": "判断结果", "valueType": "string", "type": "static"}]}, {"id": "variableUpdate", "templateType": "tools", "flowNodeType": "variableUpdate", "sourceHandle": {"top": true, "right": true, "bottom": true, "left": true}, "targetHandle": {"top": true, "right": true, "bottom": true, "left": true}, "avatar": "core/workflow/template/variableUpdate", "name": "变量更新", "intro": "可以更新指定节点的输出值或更新全局变量", "showStatus": false, "isTool": false, "version": "481", "inputs": [{"key": "updateList", "valueType": "any", "label": "", "renderTypeList": ["hidden"], "value": [{"variable": ["", ""], "value": ["", ""], "valueType": "string", "renderType": "input"}]}], "outputs": []}, {"id": "code", "templateType": "tools", "flowNodeType": "code", "sourceHandle": {"top": true, "right": true, "bottom": true, "left": true}, "targetHandle": {"top": true, "right": true, "bottom": true, "left": true}, "avatar": "core/workflow/template/codeRun", "name": "代码运行", "intro": "执行一段简单的脚本代码，通常用于进行复杂的数据处理。", "showStatus": true, "version": "482", "inputs": [{"key": "system_addInputParam", "renderTypeList": ["addInputParam"], "valueType": "dynamic", "label": "", "required": false, "description": "这些变量会作为代码的运行的输入参数", "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": true}}, {"renderTypeList": ["reference"], "valueType": "string", "canEdit": true, "key": "data1", "label": "data1", "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": true}, "required": true}, {"renderTypeList": ["reference"], "valueType": "string", "canEdit": true, "key": "data2", "label": "data2", "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": true}, "required": true}, {"key": "codeType", "renderTypeList": ["hidden"], "label": "", "value": "js"}, {"key": "code", "renderTypeList": ["custom"], "label": "", "value": "function main({data1, data2}){\n    \n    return {\n        result: data1,\n        data2\n    }\n}"}], "outputs": [{"id": "system_addOutputParam", "key": "system_addOutputParam", "type": "dynamic", "valueType": "dynamic", "label": "", "customFieldConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": false}, "description": "将代码中 return 的对象作为输出，传递给后续的节点。变量名需要对应 return 的 key"}, {"id": "system_rawResponse", "key": "system_rawResponse", "label": "完整响应数据", "valueType": "object", "type": "static"}, {"id": "error", "key": "error", "label": "运行错误", "description": "代码运行错误信息，成功时返回空", "valueType": "object", "type": "static"}, {"id": "qLUQfhG0ILRX", "type": "dynamic", "key": "result", "valueType": "string", "label": "result"}, {"id": "gR0mkQpJ4Og8", "type": "dynamic", "key": "data2", "valueType": "string", "label": "data2"}]}, {"id": "app", "templateType": "tools", "flowNodeType": "app", "sourceHandle": {"top": true, "right": true, "bottom": true, "left": true}, "targetHandle": {"top": true, "right": true, "bottom": true, "left": true}, "avatar": "core/workflow/template/runApp", "name": "应用调用", "intro": "可以选择一个其他应用进行调用", "showStatus": true, "version": "481", "isTool": true, "inputs": [{"key": "app", "renderTypeList": ["selectApp", "reference"], "valueType": "selectApp", "label": "选择一个应用", "description": "选择一个其他应用进行调用", "required": true}, {"key": "history", "renderTypeList": ["numberInput", "reference"], "valueType": "chatHistory", "label": "core.module.input.label.chat history", "description": "最多携带多少轮对话记录", "required": true, "min": 0, "max": 50, "value": 6}, {"key": "userChatInput", "renderTypeList": ["reference", "textarea"], "valueType": "string", "label": "User question", "required": true, "toolDescription": "User question"}], "outputs": [{"id": "history", "key": "history", "label": "新的上下文", "description": "将该应用回复内容拼接到历史记录中，作为新的上下文返回", "valueType": "chatHistory", "required": true, "type": "static"}, {"id": "answerText", "key": "answerText", "label": "回复的文本", "description": "将在应用完全结束后触发", "valueType": "string", "type": "static"}]}]