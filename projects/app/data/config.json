{"feConfigs": {"lafEnv": "", "systemTitle": "<PERSON><PERSON><PERSON>", "disableSTT": false, "disableTTS": false, "disableFileUpload": false, "disablePublishFofPro": true, "disablePublishShare": false, "disablePublishTOWAStore": true, "disablePublishApiKey": false, "disablePublishEduStore": false, "disableWorkflowImport": false, "disableWorkflowExport": false, "disablePublicPlugin": true, "disablePublishBenZui": true, "companyName": "况客科技（北京）有限公司", "icp": "京ICP备15006816号-1|京公网安备 11010502035044号", "oauth": {"wechat": false}, "platform": "SWL"}, "systemEnv": {"openapiPrefix": "towa", "vectorMaxProcess": 15, "qaMaxProcess": 15, "pgHNSWEfSearch": 100}, "llmModels": [{"model": "swl-chat", "name": "TOWA-<PERSON><PERSON>", "avatar": "/icon/logo.svg", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 100000, "maxTemperature": 1.2, "charsPointsPrice": 0.001, "censor": false, "vision": true, "datasetProcess": true, "usedInClassify": false, "usedInExtractFields": true, "usedInToolCall": false, "usedInQueryExtension": false, "toolChoice": true, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "defaultConfig": {}}, {"model": "swl-deepseek-v3", "name": "TOWA-DeepSeek-V3", "avatar": "/icon/logo.svg", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 100000, "maxTemperature": 1.2, "charsPointsPrice": 0.001, "censor": false, "vision": true, "datasetProcess": false, "usedInClassify": false, "usedInExtractFields": false, "usedInToolCall": false, "usedInQueryExtension": false, "toolChoice": true, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "defaultConfig": {}}, {"model": "swl-thinking", "name": "TOWA-Thinking", "avatar": "/icon/logo.svg", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 100000, "maxTemperature": 1.2, "charsPointsPrice": 0.001, "censor": false, "vision": true, "datasetProcess": false, "usedInClassify": false, "usedInExtractFields": true, "usedInToolCall": false, "usedInQueryExtension": false, "toolChoice": true, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "defaultConfig": {}}, {"model": "swl-coding-3-7", "name": "TOWA-Coding-3.7", "avatar": "/icon/logo.svg", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 100000, "maxTemperature": 1.2, "charsPointsPrice": 0.001, "censor": false, "vision": true, "datasetProcess": false, "usedInClassify": false, "usedInExtractFields": true, "usedInToolCall": false, "usedInQueryExtension": false, "toolChoice": true, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "defaultConfig": {}}, {"model": "swl-coding", "name": "TOWA-Coding", "avatar": "/icon/logo.svg", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 100000, "maxTemperature": 1.2, "charsPointsPrice": 0.001, "censor": false, "vision": true, "datasetProcess": false, "usedInClassify": false, "usedInExtractFields": true, "usedInToolCall": false, "usedInQueryExtension": false, "toolChoice": true, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "defaultConfig": {}}, {"model": "qwen-vl-max", "name": "TOWA-QWEN-VL-MAX(视觉理解)", "avatar": "/icon/logo.svg", "maxContext": 128000, "maxResponse": 16000, "quoteMaxToken": 100000, "maxTemperature": 1.2, "charsPointsPrice": 0.001, "censor": false, "vision": true, "datasetProcess": false, "usedInClassify": false, "usedInExtractFields": false, "usedInToolCall": false, "usedInQueryExtension": false, "toolChoice": true, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "defaultConfig": {}}, {"model": "ep-20250208183104-g676t", "name": "TOWA-Thinking-32B", "avatar": "/icon/logo.svg", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 100000, "maxTemperature": 1.2, "charsPointsPrice": 0.001, "censor": false, "vision": true, "datasetProcess": false, "usedInClassify": false, "usedInExtractFields": true, "usedInToolCall": false, "usedInQueryExtension": false, "toolChoice": true, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "defaultConfig": {}}], "vectorModels": [{"model": "embed-3L", "name": "Embedding-3L-openai", "avatar": "/icon/logo.svg", "charsPointsPrice": 0.001, "defaultToken": 512, "maxToken": 3000, "weight": 100, "dbConfig": {}, "queryConfig": {}, "defaultConfig": {"dimensions": 1024}}], "reRankModels": [], "audioSpeechModels": [{"model": "tts-1", "name": "OpenAI TTS1", "charsPointsPrice": 0.001, "voices": [{"label": "<PERSON><PERSON>", "value": "alloy", "bufferId": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"label": "Echo", "value": "echo", "bufferId": "openai-Echo"}, {"label": "Fable", "value": "fable", "bufferId": "openai-Fable"}, {"label": "Onyx", "value": "onyx", "bufferId": "openai-Onyx"}, {"label": "Nova", "value": "nova", "bufferId": "openai-Nova"}, {"label": "Shimmer", "value": "shimmer", "bufferId": "openai-Shimmer"}]}], "whisperModel": {"model": "whisper-1", "name": "Whisper1", "charsPointsPrice": 0.001}}