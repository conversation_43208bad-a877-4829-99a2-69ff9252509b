require('dotenv').config({
  path: process.env.NODE_ENV === 'production' ? ['.env.prod'] : ['.env.local']
});

import { connectMongo } from '@fastgpt/service/common/mongo/init';
import { MongoApp } from '@fastgpt/service/core/app/schema';
import { MongoTeam } from '@fastgpt/service/support/user/team/teamSchema';
import { MongoTeamMember } from '@fastgpt/service/support/user/team/teamMemberSchema';

const targetUserId = '673afa3783f7783f22018fe2';
const folderId = '66c2ab18352a931dc647dcdb';

async function moveFolderWithApps(folderId: string, userId: string) {
  const team = await MongoTeam.findOne({ ownerId: userId }).lean().exec();
  const tmb = await MongoTeamMember.findOne({
    userId: userId,
    defaultTeam: true
  });
  await MongoApp.updateOne(
    {
      _id: folderId
    },
    {
      parentId: null,
      tmbId: tmb?._id,
      teamId: team?._id
    }
  );
  await MongoApp.updateMany(
    {
      parentId: folderId
    },
    {
      tmbId: tmb?._id,
      teamId: team?._id
    }
  );
  console.log('moveFolderWithApps done!');
}

async function run() {
  try {
    await connectMongo();
    await moveFolderWithApps(folderId, targetUserId);
  } catch (error) {
    console.log(error);
  }
  process.exit();
}

setTimeout(run, 2000);
