require('dotenv').config({
  path: process.env.NODE_ENV === 'production' ? ['.env.prod'] : ['.env.local']
});

import { MongoUser } from '@fastgpt/service/support/user/schema';
import { connectMongo } from '@fastgpt/service/common/mongo/init';
import _ from 'lodash';
import fs from 'fs';

async function exportUserData() {
  const userList = await MongoUser.find(
    { createTime: { $gt: new Date('2025-02-24') } },
    { nickName: 1, createTime: 1, username: 1 }
  )
    .sort({ createTime: -1 })
    .lean()
    .exec();
  const csvData = userList
    .filter((item) => item.username.length === 11)
    .map((item) => {
      return [item.username, item.nickName];
    });
  const csvContent = csvData.map((item) => item.join(',')).join('\n');
  fs.writeFileSync('user_data.csv', csvContent);
}

async function run() {
  try {
    await connectMongo();
    await exportUserData();
  } catch (error) {
    console.log(error);
  }
  process.exit();
}

setTimeout(run, 2000);
