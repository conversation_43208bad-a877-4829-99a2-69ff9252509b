/* eslint-disable */
const axios = require('axios');

// 接口配置
const config = {
  url: 'https://api.shuyouyinli.cn/bzsq/py/agent/chat/completions',
  headers: {
    'Content-Type': 'application/json',
    Authorization: 'sk-r114Jm7GFFnD2kAPC97bE1A0769c4212B38f4570C86303D8'
  },
  data: {
    messages: [
      {
        role: 'user',
        content: '写一篇小红书文案推广况客思维链AI培训'
      }
    ],
    model: 'deepseek-reasoner-bz-pro',
    stream: true,
    temperature: 0
  }
};

// const config = {
//   url: 'https://api.bochaai.com/v1/web-search',
//   headers: {
//     'Content-Type': 'application/json',
//     Authorization: 'sk-d647d4d2930548b4b45267b7b26e2688'
//   },
//   data: {
//     query: '上海天气'
//   }
// };

// 创建单个请求函数
async function makeRequest(index) {
  const startTime = Date.now();
  try {
    const response = await axios.post(config.url, config.data, { headers: config.headers });
    const endTime = Date.now();
    console.log(`请求 ${index + 1} 完成，耗时: ${endTime - startTime}ms`);
    console.log(response.data, '===');
    return { success: true, data: response.data, time: endTime - startTime };
  } catch (error) {
    console.error(`请求 ${index + 1} 失败:`, error.message);
    return { success: false, error: error.message };
  }
}

// 并发执行10次请求
async function runConcurrentRequests() {
  const totalRequests = 1;
  const requests = Array(totalRequests)
    .fill()
    .map((_, index) => makeRequest(index));

  console.log(`开始执行 ${totalRequests} 个并发请求...`);
  const startTime = Date.now();

  const results = await Promise.allSettled(requests);
  const endTime = Date.now();

  // 统计成功和失败的请求
  const successCount = results.filter((r) => r.value && r.value.success).length;
  const failCount = totalRequests - successCount;

  console.log(`所有请求完成，总耗时: ${endTime - startTime}ms`);
  console.log(`成功请求: ${successCount}个，失败请求: ${failCount}个`);

  // 计算平均响应时间（仅考虑成功的请求）
  const successResults = results.filter((r) => r.value && r.value.success).map((r) => r.value);
  if (successResults.length > 0) {
    const avgTime =
      successResults.reduce((sum, item) => sum + item.time, 0) / successResults.length;
    console.log(`成功请求的平均响应时间: ${avgTime.toFixed(2)}ms`);
  }

  return results.map((r) => r.value);
}

// 执行并发请求
runConcurrentRequests();
