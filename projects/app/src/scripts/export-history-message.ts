require('dotenv').config({
  path: process.env.NODE_ENV === 'production' ? ['.env.prod'] : ['.env.local']
});

import { connectMongo } from '@fastgpt/service/common/mongo/init';
import { MongoChatItem } from '@fastgpt/service/core/chat/chatItemSchema';
import { saveArrayDataAsExcel } from '@fastgpt/service/common/file/utils';
import path from 'path';

const appId = '66bbf75bebee62de5fc09dfd';

async function getMessageData(appId: string) {
  const messages = await MongoChatItem.find({ appId }).lean().exec();
  return messages
    .filter((item) => item.obj === 'Human')
    .map((item) => {
      return [item.value[0]?.text?.content];
    });
}

async function run() {
  try {
    await connectMongo();
    const data = await getMessageData(appId);
    const filepath = path.join(__dirname, 'history-messages');
    saveArrayDataAsExcel(
      [
        {
          name: 'Sheet1',
          data: data
        }
      ],
      filepath
    );
    console.log(`Exported ${data.length}`);
  } catch (error) {
    console.log(error);
  }
  process.exit();
}

setTimeout(run, 2000);
