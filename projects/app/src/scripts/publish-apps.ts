require('dotenv').config({
  path: process.env.NODE_ENV === 'production' ? ['.env.prod'] : ['.env.local']
});

import { MongoUser } from '@fastgpt/service/support/user/schema';
import { connectMongo } from '@fastgpt/service/common/mongo/init';
import { MongoApp } from '@fastgpt/service/core/app/schema';
import { AppPublishStatusEnum } from '@fastgpt/global/core/app/constants';

async function getUsers() {
  const users = await MongoUser.find({}).lean().exec();
  console.log(users);
}

async function publishApps() {
  const appIds: Array<string> = [
    '66865e23c03b1a5d8e0a87c3',
    '6685245f5aee36070e7f1f9e',
    '664ab9a01cd75aa5f2d522f2',
    '66927974247e474961368f85',
    '669285ae247e474961376139',
    '66923a7dc06b90b716b75600',
    '669525024b802db6826bcfe5',
    '6699e79dc7b3712cc2fb84e5',
    '669a092dc7b3712cc2fbb721',
    '66a200c8f0d1c5e6b90961ee',
    '66a48014f0d1c5e6b90a4e04',
    '66a7121bf0d1c5e6b90c9bf8',
    '66bf6571352a931dc64741c7',
    '66b9b10d6145a57ef807fd38',
    '66d7cb3b2741090931b4a013',
    '674e721d3e45bcd2cd317dba',
    '67177068500cbc9c82e50b33',
    '677371eda3e18e1d5e37f7d6',
    '6773a40ca3e18e1d5e382db4',
    '6773a9ada3e18e1d5e38321e',
    '6773b90ba3e18e1d5e384a2d',
    '6773b90ba3e18e1d5e384a2d',
    '66ed462fa6e46495a0e41441',
    '677e183fe6ee91a49011f4bb',
    '67ab045d953a4de12e38550b'
  ];
  await MongoApp.updateMany(
    {
      _id: { $in: appIds }
    },
    {
      towaStoreStatus: AppPublishStatusEnum.published
    }
  );
  console.log(`Published ${appIds.length} apps`);
}

async function run() {
  try {
    await connectMongo();
    await publishApps();
  } catch (error) {
    console.log(error);
  }
  process.exit();
}

setTimeout(run, 2000);
