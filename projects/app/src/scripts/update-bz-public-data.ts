require('dotenv').config({
  path: process.env.NODE_ENV === 'production' ? ['.env.prod'] : ['.env.local']
});

import { connectMongo } from '@fastgpt/service/common/mongo/init';
import { MongoApp } from '@fastgpt/service/core/app/schema';
import { DatasetVisibleEnum } from '@fastgpt/global/core/dataset/constants';
import { ParentIdType } from '@fastgpt/global/common/parentFolder/type';

const publicAppFolders = ['67d12ef08ba309c7beb69754'];

async function updatePublicFolders(appIds: Array<ParentIdType>) {
  await MongoApp.updateMany(
    {
      _id: { $in: appIds }
    },
    {
      visibility: DatasetVisibleEnum.public
    }
  );
  await MongoApp.updateMany(
    {
      parentId: { $in: appIds }
    },
    {
      visibility: DatasetVisibleEnum.public
    }
  );
  console.log(`Updated ${appIds.length} folders`);
}

async function getPublicAppInfo() {
  const apps = await MongoApp.find({
    visibility: DatasetVisibleEnum.public
  })
    .lean()
    .exec();
  console.log(apps.length);
}

async function run() {
  try {
    await connectMongo();
    // await updatePublicFolders(publicAppFolders);
    await getPublicAppInfo();
  } catch (error) {
    console.log(error);
  }
  process.exit();
}

setTimeout(run, 2000);
