require('dotenv').config({
  path: process.env.NODE_ENV === 'production' ? ['.env.prod'] : ['.env.local']
});

import { PRICE_SCALE } from '@fastgpt/global/support/wallet/constants';
import { MongoUser } from '@fastgpt/service/support/user/schema';
import { hashStr } from '@fastgpt/global/common/string/tools';
import { createDefaultTeam } from '@fastgpt/service/support/user/team/controller';
import { mongoSessionRun } from '@fastgpt/service/common/mongo/sessionRun';
import { connectMongo } from '@fastgpt/service/common/mongo/init';
import { MongoApp } from '@fastgpt/service/core/app/schema';
import { MongoTeam } from '@fastgpt/service/support/user/team/teamSchema';
import _ from 'lodash';

const userNames = [
  'towa01',
  'towa02',
  'towa03',
  'towa04',
  'towa05',
  'towa06',
  'towa07',
  'towa08',
  'towa09',
  'towa10',
  'towa11',
  'towa12',
  'towa13',
  'towa14',
  'towa15',
  'towa16',
  'towa17',
  'towa18',
  'towa19',
  'towa20'
];

async function getUserList() {
  const users = await MongoUser.find(
    {
      username: { $in: userNames },
      password: null
    },
    'username password'
  )
    .lean()
    .exec();
  return users;
}

async function updatePassword() {
  const userList = await getUserList();
  const password = _.random(100000, 999999).toString();
  console.log(userList);
  for (const item of userList) {
    //   await MongoUser.updateOne({ username: item.username }, { $set: { password: hashStr(password) } });
  }
}

async function run() {
  try {
    await connectMongo();
    await updatePassword();
  } catch (error) {
    console.log(error);
  }
  process.exit();
}

setTimeout(run, 2000);
