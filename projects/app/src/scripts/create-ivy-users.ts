require('dotenv').config({
  path: process.env.NODE_ENV === 'production' ? ['.env.prod'] : ['.env.local']
});

import { PRICE_SCALE } from '@fastgpt/global/support/wallet/constants';
import { MongoUser } from '@fastgpt/service/support/user/schema';
import { hashStr } from '@fastgpt/global/common/string/tools';
import { createDefaultTeam } from '@fastgpt/service/support/user/team/controller';
import { mongoSessionRun } from '@fastgpt/service/common/mongo/sessionRun';
import { connectMongo } from '@fastgpt/service/common/mongo/init';
import { MongoApp } from '@fastgpt/service/core/app/schema';
import { MongoTeam } from '@fastgpt/service/support/user/team/teamSchema';
import _ from 'lodash';

function getUserListIvy() {
  const users = _.range(1, 41).map((item) => {
    return {
      username: `ivy_trial${_.padStart(item.toString(), 4, '0')}`,
      password: 'aitoivy1201'
    };
  });
  return users;
}

function getUserList() {
  const users = _.range(1, 20).map((item) => {
    return {
      username: `syyl_user${_.padStart(item.toString(), 4, '0')}`,
      password: 'ehIs10z0'
    };
  });
  return users;
}

function getEduV1UserList() {
  const phones = [
    '余锦贤+18059216646',
    'Jordan+18750213869',
    '陈钰+13808522280',
    'Roxine+13806031320',
    '王柏霖+13696927220',
    '岛岛+13666097191',
    '朱古力+15260140504',
    '乐乐+13959248082',
    '铱铱+15859575115',
    '安东尼+13918390620',
    '乖乖+13023913369',
    '小Ai+15960788888'
  ];
  const users = phones.map((phoneStr) => {
    const splits = phoneStr.split('+');
    const phone = splits[1];
    const password = phone.slice(-6);
    const nickname = splits[0];
    // const password = _.random(100000, 999999).toString();
    return {
      username: `${phone}`,
      password: password,
      nickName: nickname
    };
  });
  return users;
}

async function createUsers() {
  const userList = getEduV1UserList();
  for (const item of userList) {
    const user = await MongoUser.findOne({ username: item.username }).lean().exec();
    if (!user) {
      const userData = {
        username: item.username,
        password: hashStr(item.password),
        nickName: item.nickName
      };
      await mongoSessionRun(async (session) => {
        const [user] = await MongoUser.create([userData], { session });
        const rootId = user._id;
        await createDefaultTeam({ userId: rootId, balance: 9999 * PRICE_SCALE, session });
      });
      console.log(`${item.username}: ${item.password}`);
    } else {
      console.log(`User ${item.username} already existed`);
    }
  }
}

async function run() {
  try {
    await connectMongo();
    await createUsers();
  } catch (error) {
    console.log(error);
  }
  process.exit();
}

setTimeout(run, 2000);
