require('dotenv').config({
  path: process.env.NODE_ENV === 'production' ? ['.env.prod'] : ['.env.local']
});

import { connectMongo } from '@fastgpt/service/common/mongo/init';
import { MongoDataset } from '@fastgpt/service/core/dataset/schema';
import { MongoApp } from '@fastgpt/service/core/app/schema';
import { DatasetDefaultPermissionVal } from '@fastgpt/global/support/permission/dataset/constant';
import { MongoOutLink } from '@fastgpt/service/support/outLink/schema';
import { MongoOpenApi } from '@fastgpt/service/support/openapi/schema';
import { MongoUsage } from '@fastgpt/service/support/wallet/usage/schema';
import { UsageSourceEnum } from '@fastgpt/global/support/wallet/usage/constants';

import { pluginSystemModuleTemplates } from '@fastgpt/global/core/workflow/template/constants';

async function fixDatasetPermission() {
  const result = await MongoDataset.updateMany(
    { defaultPermission: null },
    { defaultPermission: DatasetDefaultPermissionVal }
  );
  const result1 = await MongoDataset.find({ defaultPermission: null });
  console.log(result, result1.length);
}

async function updateVisible() {
  const ids = ['66966e624b802db6827686bc', '66966e4f4b802db682768657', '66a47f13f0d1c5e6b90a4ce9'];
  await MongoApp.updateMany({ _id: { $in: ids } }, { visibility: 'private' });
}

async function getUsageInfo() {
  const data = await MongoOpenApi.find({ usagePoints: { $gt: 0 } })
    .lean()
    .exec();
  console.log(data);
  const outlinkData = await MongoOutLink.find({ usagePoints: { $gt: 0 } })
    .lean()
    .exec();
  console.log(outlinkData);
  const usageData = await MongoUsage.find({ source: { $ne: UsageSourceEnum.training } })
    .lean()
    .exec();
  console.log(usageData);
}

async function run() {
  try {
    // await connectMongo();
    // await fixDatasetPermission();
    // await updateVisible()
    // await getUsageInfo();

    require('fs').writeFileSync(
      'node-list.json',
      JSON.stringify(pluginSystemModuleTemplates, null, 2)
    );
  } catch (error) {
    console.log(error);
  }
  process.exit();
}

setTimeout(run, 2000);
