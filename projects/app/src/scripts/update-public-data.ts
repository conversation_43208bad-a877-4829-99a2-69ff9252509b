require('dotenv').config({
  path: process.env.NODE_ENV === 'production' ? ['.env.prod'] : ['.env.local']
});

import { connectMongo } from '@fastgpt/service/common/mongo/init';
import { MongoApp } from '@fastgpt/service/core/app/schema';
import { MongoDataset } from '@fastgpt/service/core/dataset/schema';
import {
  DatasetVisibleEnum,
  DatasetInteralIdentificationEnum
} from '@fastgpt/global/core/dataset/constants';
import { ParentIdType } from '@fastgpt/global/common/parentFolder/type';

const publicDataSetIds = [
  '66a0cd3e9b6c6fa809ab01cf',
  '668ceb70c03b1a5d8e0c811f',
  '668fec584dc4ab72b3b36e88',
  '668f9d9d52625b51f6965076',
  '668feb9d4dc4ab72b3b36c9b'
];
const publicAppIds = [
  '67b2b6370c4cc448c791f211',
  '6693e55e410deeeb4e7c9684',
  '669237e7c06b90b716b75514',
  //'66928219247e474961375b8a',
  '669524184b802db6826b5d34',
  '66a0a1f34e28e5dc0f1025c2',
  '66a06adc4c3c1593174bc314',
  '669f847289b0f65e1dec4e1f',
  '6698760d4b802db682789a48',
  '6697753e4b802db6827710bc',
  '66971e914b802db68276a347',
  '669616f54b802db682761e9e',
  '6694d9dc9d6eda8f93052ee9',
  '669e1476ea94ad31b89fe4b3',
  '66b2dd32a61213972b0b1f46',
  //'66b2df06a61213972b0b2275',
  '66b9afbb6145a57ef807faa7',

  '66be042c97d601c00302d21f',
  '66be03f397d601c00302d1f5',
  '66be03b197d601c00302d1ba',
  '66be036897d601c00302d195',
  '66be031697d601c00302d161',
  '66be02cf97d601c00302d129',
  '66be018e97d601c00302d02c',
  '66bdfeeb97d601c00302cc8c',
  '66d6c0582741090931b46aec',
  '66f4e0e9a6e46495a0e54921',
  '674d8f223e45bcd2cd31602f',
  '67e75d07e5296b256a2475d8',
  '6805e46ff53aeba15e20cff7', // jimeng
  '6809a0aff53aeba15e22a684',
  '68197fb6f08b2b4b71491443',
  '680dd96dfe4016c0a5d9e4c5'
];
const publicAppFolders = [
  '6693e55e410deeeb4e7c9684',
  '669237e7c06b90b716b75514',
  //'66928219247e474961375b8a',
  '669524184b802db6826b5d34',
  '66a0a1f34e28e5dc0f1025c2',
  '66a06adc4c3c1593174bc314',
  '669f847289b0f65e1dec4e1f',
  '6698760d4b802db682789a48',
  '6697753e4b802db6827710bc',
  '66971e914b802db68276a347',
  '669616f54b802db682761e9e',
  '6694d9dc9d6eda8f93052ee9',
  '669e1476ea94ad31b89fe4b3',

  '66be042c97d601c00302d21f',
  '66be03f397d601c00302d1f5',
  '66be03b197d601c00302d1ba',
  '66be036897d601c00302d195',
  '66be031697d601c00302d161',
  '66be02cf97d601c00302d129',
  '66be018e97d601c00302d02c',
  '66bdfeeb97d601c00302cc8c',
  '66d6c0582741090931b46aec',
  '674d8e9b3e45bcd2cd315fe0',
  '66f4e0e9a6e46495a0e54921',
  '6809a0aff53aeba15e22a684',
  '680dd96dfe4016c0a5d9e4c5'
];
const internalDatasets = [
  {
    id: '66922306c06b90b716b58c0a',
    key: DatasetInteralIdentificationEnum.MUTUAL_FUND_CODE_EMB
  },
  {
    id: '66922311c06b90b716b58c3f',
    key: DatasetInteralIdentificationEnum.MUTUAL_COMPANY_CODE_EMB
  },
  {
    id: '669222d1c06b90b716b58b70',
    key: DatasetInteralIdentificationEnum.MANAGER_CODE_EMB
  },
  {
    id: '669222fac06b90b716b58bd5',
    key: DatasetInteralIdentificationEnum.STOCK_CODE_EMB
  },
  {
    id: '6692232ac06b90b716b58c76',
    key: DatasetInteralIdentificationEnum.BENCHMARK_CODE_EMB
  },
  {
    id: '669504d09d6eda8f9305722d',
    key: DatasetInteralIdentificationEnum.HEDGE_FUND_CODE_EMB
  },
  {
    id: '669504db9d6eda8f93057262',
    key: DatasetInteralIdentificationEnum.HEDGE_COMPANY_CODE_EMB
  },
  {
    id: '669505a29d6eda8f930572d9',
    key: DatasetInteralIdentificationEnum.MUTUAL_FUND_CLASS_EMB
  }
];

async function updatePublicDatasets(datasetIds: Array<string>) {
  if (!datasetIds.length) {
    return;
  }
  const datasets = await MongoDataset.find(
    {
      _id: { $in: datasetIds }
    },
    'parentId name'
  )
    .lean()
    .exec();
  await MongoDataset.updateMany(
    {
      _id: { $in: datasetIds }
    },
    {
      visibility: DatasetVisibleEnum.public
    }
  );
  console.log(`Updated ${datasets.length} datasets`);
  datasets.forEach((item, index) => {
    console.log(`${index}: ${item.name}`);
  });
  const parentIds = datasets.map((item) => item.parentId || '').filter(Boolean);
  await updatePublicDatasets(parentIds);
}

async function updatePublicApps(appIds: Array<ParentIdType>) {
  if (!appIds.length) {
    return;
  }
  const apps = await MongoApp.find(
    {
      _id: { $in: appIds }
    },
    'parentId name'
  )
    .lean()
    .exec();
  await MongoApp.updateMany(
    {
      _id: { $in: appIds }
    },
    {
      visibility: DatasetVisibleEnum.public
    }
  );
  console.log(`Updated ${apps.length} apps`);
  apps.forEach((item, index) => {
    console.log(`${index}: ${item.name}`);
  });
  const parentIds = apps.map((item) => item.parentId).filter(Boolean);
  await updatePublicApps(parentIds);
}

async function updatePublicFolder(appIds: Array<ParentIdType>) {
  await MongoApp.updateMany(
    {
      parentId: { $in: appIds }
    },
    {
      visibility: DatasetVisibleEnum.public
    }
  );
  console.log(`Updated ${appIds.length} folders`);
}

async function updateDatasetInternalIdentification() {
  for (const item of internalDatasets) {
    await MongoDataset.updateOne(
      {
        _id: item.id
      },
      {
        internalIdentification: item.key
      }
    );
    console.log(`Updated ${item.key}`);
  }
}

async function updateTypoApps() {
  const appIds = ['66b2dd32a61213972b0b1f46'];
  await MongoApp.updateMany(
    {
      _id: { $in: appIds }
    },
    {
      visibility: DatasetVisibleEnum.private
    }
  );
}

async function run() {
  try {
    await connectMongo();
    // await connectMongo({ beforeHook: () => {}, afterHook: async () => {} });
    await updatePublicApps(publicAppIds);
    //await updateTypoApps()
    //await updatePublicFolder(publicAppFolders);
  } catch (error) {
    console.log(error);
  }
  process.exit();
}

setTimeout(run, 2000);
