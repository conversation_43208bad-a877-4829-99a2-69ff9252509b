require('dotenv').config({
  path: process.env.NODE_ENV === 'production' ? ['.env.prod'] : ['.env.local']
});

import { PRICE_SCALE } from '@fastgpt/global/support/wallet/constants';
import { MongoUser } from '@fastgpt/service/support/user/schema';
import { hashStr } from '@fastgpt/global/common/string/tools';
import { createDefaultTeam } from '@fastgpt/service/support/user/team/controller';
import { mongoSessionRun } from '@fastgpt/service/common/mongo/sessionRun';
import { connectMongo } from '@fastgpt/service/common/mongo/init';
import { MongoApp } from '@fastgpt/service/core/app/schema';
import { MongoTeam } from '@fastgpt/service/support/user/team/teamSchema';
import _ from 'lodash';

const userList = [
  {
    username: 'towa_plugin',
    password: process.env.TOWA_PLUGIN_USER_PASSWORD || ''
  }
];

async function updatePassword() {
  await MongoUser.updateOne(
    {
      username: 'qutke'
    },
    {
      password: hashStr('towa@qutke888')
    }
  );
}

async function getUserApps() {
  const apps = await MongoApp.find({}, 'teamId name intro avatar').lean().exec();
  const teamIds = apps.map((app) => app.teamId);
  const teams = await MongoTeam.find(
    {
      _id: { $in: teamIds }
    },
    'ownerId'
  )
    .populate('ownerId', 'fofproUserId nickName username')
    .lean()
    .exec();
  const teamInfoMap = teams.reduce((out, item) => {
    return {
      ...out,
      [item._id.toString()]: item.ownerId
    };
  }, {}) as any;
  const results = apps.map((app) => {
    const owner = teamInfoMap[app.teamId.toString()];
    return {
      name: app.name,
      id: app._id,
      intro: app.intro,
      avatar: app.avatar,
      userId: owner.fofproUserId,
      username: owner.username,
      nickname: owner.nickName
    };
  });
  console.log(_.countBy(results, 'username'));
}

async function createUsers() {
  for (const item of userList) {
    const user = await MongoUser.findOne({ username: item.username }).lean().exec();
    if (!user) {
      const userData = {
        username: item.username,
        password: hashStr(item.password)
      };
      await mongoSessionRun(async (session) => {
        const [user] = await MongoUser.create([userData], { session });
        const rootId = user._id;
        await createDefaultTeam({ userId: rootId, balance: 9999 * PRICE_SCALE, session });
      });
      console.log(`User ${item.username} created`);
    } else {
      console.log(`User ${item.username} already existed`);
    }
  }
}

async function run() {
  try {
    await connectMongo();
    await createUsers();
    const allUsers = await MongoUser.find({}, 'username nickName fofproUserId').lean().exec();
    allUsers.forEach((user) => console.log(user));
  } catch (error) {
    console.log(error);
  }
  process.exit();
}

setTimeout(run, 2000);
