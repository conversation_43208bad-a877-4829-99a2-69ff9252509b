require('dotenv').config({
  path: process.env.NODE_ENV === 'production' ? ['.env.prod'] : ['.env.local']
});

import { connectMongo } from '@fastgpt/service/common/mongo/init';
import { MongoApp } from '@fastgpt/service/core/app/schema';
import { MongoTeam } from '@fastgpt/service/support/user/team/teamSchema';
import { MongoTeamMember } from '@fastgpt/service/support/user/team/teamMemberSchema';
import { AppPublishStatusEnum } from '@fastgpt/global/core/app/constants';

const targetUserIds = [
  '670777bea6e46495a0e74f42' // amy
];
const appIds = [
  '673459ad31edda9d6817d12c',
  '6732d92fb076220962789d16',
  '6699e79dc7b3712cc2fb84e5',
  '66d992dfa6e46495a0dcd21e',
  '66923a7dc06b90b716b75600',
  '66bf6571352a931dc64741c7',
  '6718ab4c500cbc9c82e551d2',
  '66ed462fa6e46495a0e41441'
];

async function syncAppToUsers(appId: string, targetUserIds: Array<string>) {
  const app = await MongoApp.findOne({ _id: appId }).lean().exec();
  delete app?._id;
  delete app?.parentId;
  for (const userId of targetUserIds) {
    const team = await MongoTeam.findOne({ ownerId: userId }).lean().exec();
    const tmb = await MongoTeamMember.findOne({
      userId: userId,
      defaultTeam: true
    });
    app.tmbId = tmb._id;
    app.teamId = team._id;
    await MongoApp.create([app]);
  }
  console.log(`Sync done ${app.name}`);
}

async function syncApps(appIds: Array<string>, targetUserIds: Array<string>) {
  for (const appId of appIds) {
    await syncAppToUsers(appId, targetUserIds);
  }
}

async function removeErrorApps(targetUserIds) {
  for (const userId of targetUserIds) {
    const team = await MongoTeam.findOne({ ownerId: userId }).lean().exec();
    await MongoApp.deleteMany({ parentId: '66a1fff0f0d1c5e6b9096073', teamId: team._id });
  }
}

async function fixAppStatus() {
  const ids = [
    '67346a41f84b4ef6a3d6d442',
    '673465ae0a7ebe7e65b30bcf',
    '673465ae0a7ebe7e65b30bc9',
    '67346018e022ba4b7a776a92',
    '673465ae0a7ebe7e65b30bd5',
    '673465ae0a7ebe7e65b30bdb',
    '67346a41f84b4ef6a3d6d436',
    '67346a41f84b4ef6a3d6d43c',
    '67346a41f84b4ef6a3d6d448'
  ];
  const r = await MongoApp.updateMany(
    {
      _id: { $in: ids }
    },
    {
      towaStoreStatus: AppPublishStatusEnum.unpublished
    }
  );
  console.log(`Updated`, r);
}

async function run() {
  try {
    await connectMongo();
    await syncApps(appIds, targetUserIds);
    // await removeErrorApps(targetUserIds)
  } catch (error) {
    console.log(error);
  }
  process.exit();
}

setTimeout(run, 2000);
