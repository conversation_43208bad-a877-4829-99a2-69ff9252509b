import React from 'react';
import { Box, Text, VStack } from '@chakra-ui/react';
import dayjs from 'dayjs';
import { useSystemStore } from '@/web/common/system/useSystemStore';

const Copyright = ({ isAppPage }: { isAppPage?: boolean }) => {
  const { feConfigs } = useSystemStore();
  if (isAppPage && feConfigs.disableAppPageCopyright) {
    return null;
  }
  return (
    <Box
      position="absolute"
      bottom="0"
      left="0"
      right="0"
      p="3"
      bg={'myGray.100'}
      backdropFilter="blur(5px)"
      zIndex="1"
    >
      <VStack spacing="1">
        <Text fontSize="xs" color="gray.600" textAlign="center">
          Copyright © {dayjs().year()} {feConfigs.companyName}
        </Text>
        <Text fontSize="xs" color="gray.600" textAlign="center">
          {feConfigs.icp}
        </Text>
      </VStack>
    </Box>
  );
};

export default Copyright;
