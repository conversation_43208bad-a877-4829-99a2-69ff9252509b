import { Box, Flex } from '@chakra-ui/react';
import React from 'react';
import { useTranslation } from 'next-i18next';
import { langMap, setLngStore } from '@/web/common/utils/i18n';
import { useRouter } from 'next/router';
import MySelect from '@fastgpt/web/components/common/MySelect';

const LanguageSelector = () => {
  const { t, i18n } = useTranslation();
  const router = useRouter();

  return (
    <Flex alignItems={'center'} w={['150px']}>
      <Box flex={'1 0 0'}>
        <MySelect
          value={i18n.language}
          list={Object.entries(langMap).map(([key, lang]) => ({
            label: lang.label,
            value: key
          }))}
          onchange={(val: any) => {
            const lang = val;
            setLngStore(lang);
            router.replace(
              {
                query: router.query
              },
              router.asPath,
              { locale: lang }
            );
          }}
        />
      </Box>
    </Flex>
  );
};

export default LanguageSelector;
