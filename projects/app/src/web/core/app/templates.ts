// @ts-nocheck

import { AppItemType } from '@/types/app';
import type { AppSchema } from '@fastgpt/global/core/app/type';
import { AppTypeEnum } from '@fastgpt/global/core/app/constants';
import {
  NodeOutputKeyEnum,
  WorkflowIOValueTypeEnum
} from '@fastgpt/global/core/workflow/constants';
import {
  FlowNodeInputTypeEnum,
  FlowNodeOutputTypeEnum,
  FlowNodeTypeEnum
} from '@fastgpt/global/core/workflow/node/constant';

type TemplateType = (AppItemType & {
  avatar: string;
  intro: string;
  type: AppTypeEnum;
  chatConfig?: AppSchema['chatConfig'];
})[];

// template
export const simpleBotTemplates: TemplateType = [
  {
    id: 'simpleChat',
    avatar: '/imgs/workflow/AI.png',
    name: 'workflow:template.simple_robot',
    intro: 'workflow:template.simple_robot_intro',
    type: AppTypeEnum.simple,
    modules: [
      {
        nodeId: 'userGuide',
        name: 'workflow:template.system_config',
        intro: 'workflow:template.system_config_dec',
        avatar: '/imgs/workflow/userGuide.png',
        flowNodeType: FlowNodeTypeEnum.systemConfig,
        position: {
          x: 531.2422736065552,
          y: -486.7611729549753
        },
        version: '481',
        inputs: [
          {
            key: 'welcomeText',
            renderTypeList: [FlowNodeInputTypeEnum.hidden],
            valueType: WorkflowIOValueTypeEnum.string,
            label: 'core.app.Welcome Text',
            value: ''
          },
          {
            key: 'variables',
            renderTypeList: [FlowNodeInputTypeEnum.hidden],
            valueType: WorkflowIOValueTypeEnum.any,
            label: 'core.app.Chat Variable',
            value: []
          },
          {
            key: 'questionGuide',
            valueType: WorkflowIOValueTypeEnum.boolean,
            renderTypeList: [FlowNodeInputTypeEnum.hidden],
            label: 'core.app.Question Guide',
            value: false
          },
          {
            key: 'tts',
            renderTypeList: [FlowNodeInputTypeEnum.hidden],
            valueType: WorkflowIOValueTypeEnum.any,
            label: '',
            value: {
              type: 'web'
            }
          },
          {
            key: 'whisper',
            renderTypeList: [FlowNodeInputTypeEnum.hidden],
            valueType: WorkflowIOValueTypeEnum.any,
            label: '',
            value: {
              open: false,
              autoSend: false,
              autoTTSResponse: false
            }
          },
          {
            key: 'scheduleTrigger',
            renderTypeList: [FlowNodeInputTypeEnum.hidden],
            valueType: WorkflowIOValueTypeEnum.any,
            label: '',
            value: null
          }
        ],
        outputs: []
      },
      {
        nodeId: '448745',
        name: 'workflow:template.workflow_start',
        intro: '',
        avatar: '/imgs/workflow/userChatInput.svg',
        flowNodeType: FlowNodeTypeEnum.workflowStart,
        position: {
          x: 558.4082376415505,
          y: 123.72387429194112
        },
        version: '481',
        inputs: [
          {
            key: 'userChatInput',
            renderTypeList: [FlowNodeInputTypeEnum.reference, FlowNodeInputTypeEnum.textarea],
            valueType: WorkflowIOValueTypeEnum.string,
            label: 'core.module.input.label.user question',
            required: true,
            toolDescription: 'core.module.input.label.user question'
          }
        ],
        outputs: [
          {
            id: 'userChatInput',
            key: 'userChatInput',
            label: 'core.module.input.label.user question',
            valueType: WorkflowIOValueTypeEnum.string,
            type: FlowNodeOutputTypeEnum.static
          }
        ]
      },
      {
        nodeId: 'loOvhld2ZTKa',
        name: 'workflow:template.ai_chat',
        intro: 'workflow:template.ai_chat_intro',
        avatar: '/imgs/workflow/AI.png',
        flowNodeType: FlowNodeTypeEnum.chatNode,
        showStatus: true,
        position: {
          x: 1097.7317280958762,
          y: -244.16014496351386
        },
        version: '481',
        inputs: [
          {
            key: 'model',
            renderTypeList: [
              FlowNodeInputTypeEnum.settingLLMModel,
              FlowNodeInputTypeEnum.reference
            ],
            label: 'core.module.input.label.aiModel',
            valueType: WorkflowIOValueTypeEnum.string,
            value: 'swl-chat'
          },
          {
            key: 'temperature',
            renderTypeList: [FlowNodeInputTypeEnum.hidden],
            label: '',
            value: 0,
            valueType: WorkflowIOValueTypeEnum.number,
            min: 0,
            max: 10,
            step: 1
          },
          {
            key: 'maxToken',
            renderTypeList: [FlowNodeInputTypeEnum.hidden],
            label: '',
            value: 2000,
            valueType: WorkflowIOValueTypeEnum.number,
            min: 100,
            max: 4000,
            step: 50
          },
          {
            key: 'isResponseAnswerText',
            renderTypeList: [FlowNodeInputTypeEnum.hidden],
            label: '',
            value: true,
            valueType: WorkflowIOValueTypeEnum.boolean
          },
          {
            key: 'quoteTemplate',
            renderTypeList: [FlowNodeInputTypeEnum.hidden],
            label: '',
            valueType: WorkflowIOValueTypeEnum.string
          },
          {
            key: 'quotePrompt',
            renderTypeList: [FlowNodeInputTypeEnum.hidden],
            label: '',
            valueType: WorkflowIOValueTypeEnum.string
          },
          {
            key: 'systemPrompt',
            renderTypeList: [FlowNodeInputTypeEnum.textarea, FlowNodeInputTypeEnum.reference],
            max: 3000,
            valueType: WorkflowIOValueTypeEnum.string,
            label: 'core.ai.Prompt',
            description: 'core.app.tip.chatNodeSystemPromptTip',
            placeholder: 'core.app.tip.chatNodeSystemPromptTip',
            value: ''
          },
          {
            key: 'history',
            renderTypeList: [FlowNodeInputTypeEnum.numberInput, FlowNodeInputTypeEnum.reference],
            valueType: WorkflowIOValueTypeEnum.chatHistory,
            label: 'core.module.input.label.chat history',
            required: true,
            min: 0,
            max: 30,
            value: 6
          },
          {
            key: 'userChatInput',
            renderTypeList: [FlowNodeInputTypeEnum.reference, FlowNodeInputTypeEnum.textarea],
            valueType: WorkflowIOValueTypeEnum.string,
            label: 'core.module.input.label.user question',
            required: true,
            toolDescription: 'core.module.input.label.user question',
            value: ['448745', 'userChatInput']
          },
          {
            key: 'quoteQA',
            renderTypeList: [FlowNodeInputTypeEnum.settingDatasetQuotePrompt],
            label: '',
            debugLabel: 'workflow:template.dataset_search',
            description: '',
            valueType: WorkflowIOValueTypeEnum.datasetQuote
          }
        ],
        outputs: [
          {
            id: 'history',
            key: 'history',
            label: 'core.module.output.label.New context',
            description: 'core.module.output.description.New context',
            valueType: WorkflowIOValueTypeEnum.chatHistory,
            type: FlowNodeOutputTypeEnum.static
          },
          {
            id: 'answerText',
            key: 'answerText',
            label: 'core.module.output.label.Ai response content',
            description: 'core.module.output.description.Ai response content',
            valueType: WorkflowIOValueTypeEnum.string,
            type: FlowNodeOutputTypeEnum.static
          }
        ]
      }
    ],
    edges: [
      {
        source: '448745',
        target: 'loOvhld2ZTKa',
        sourceHandle: '448745-source-right',
        targetHandle: 'loOvhld2ZTKa-target-left'
      }
    ]
  }
];

export const workflowTemplates: TemplateType = [
  {
    id: 'EmptyWorkflow',
    avatar: '/imgs/app/avatar/workflow.svg',
    name: 'workflow:template.empty_template',
    intro: 'workflow:template.empty_template_desc',
    type: AppTypeEnum.workflow,
    modules: [
      {
        nodeId: 'userGuide',
        name: 'workflow:template.system_config',
        intro: 'workflow:template.system_config_dec',
        avatar: '/imgs/workflow/userGuide.png',
        flowNodeType: 'userGuide',
        position: {
          x: 531.2422736065552,
          y: -486.7611729549753
        },
        version: '481',
        inputs: [
          {
            key: 'welcomeText',
            renderTypeList: ['hidden'],
            valueType: 'string',
            label: 'core.app.Welcome Text',
            value: ''
          },
          {
            key: 'variables',
            renderTypeList: ['hidden'],
            valueType: 'any',
            label: 'core.app.Chat Variable',
            value: []
          },
          {
            key: 'questionGuide',
            valueType: 'boolean',
            renderTypeList: ['hidden'],
            label: 'core.app.Question Guide',
            value: false
          },
          {
            key: 'tts',
            renderTypeList: ['hidden'],
            valueType: 'any',
            label: '',
            value: {
              type: 'web'
            }
          },
          {
            key: 'whisper',
            renderTypeList: ['hidden'],
            valueType: 'any',
            label: '',
            value: {
              open: false,
              autoSend: false,
              autoTTSResponse: false
            }
          },
          {
            key: 'scheduleTrigger',
            renderTypeList: ['hidden'],
            valueType: 'any',
            label: '',
            value: null
          }
        ],
        outputs: []
      },
      {
        nodeId: '448745',
        name: 'workflow:template.workflow_start',
        intro: '',
        avatar: '/imgs/workflow/userChatInput.svg',
        flowNodeType: 'workflowStart',
        position: {
          x: 558.4082376415505,
          y: 123.72387429194112
        },
        version: '481',
        inputs: [
          {
            key: 'userChatInput',
            renderTypeList: ['reference', 'textarea'],
            valueType: 'string',
            label: 'core.module.input.label.user question',
            required: true,
            toolDescription: 'core.module.input.label.user question'
          }
        ],
        outputs: [
          {
            id: 'userChatInput',
            key: 'userChatInput',
            label: 'core.module.input.label.user question',
            type: 'static',
            valueType: 'string'
          }
        ]
      },
      {
        nodeId: 'tJQy3Rdob9nJ',
        name: 'workflow:template.ai_chat',
        intro: 'workflow:template.ai_chat_intro',
        avatar: '/imgs/workflow/AI.png',
        flowNodeType: 'chatNode',
        showStatus: true,
        position: {
          x: 1458.7088563573445,
          y: -73.81000209104992
        },
        version: '481',
        inputs: [
          {
            key: 'model',
            renderTypeList: ['settingLLMModel', 'reference'],
            label: 'core.module.input.label.aiModel',
            valueType: 'string',
            value: 'swl-chat'
          },
          {
            key: 'temperature',
            renderTypeList: ['hidden'],
            label: '',
            value: 0,
            valueType: 'number',
            min: 0,
            max: 10,
            step: 1
          },
          {
            key: 'maxToken',
            renderTypeList: ['hidden'],
            label: '',
            value: 2000,
            valueType: 'number',
            min: 100,
            max: 4000,
            step: 50
          },
          {
            key: 'isResponseAnswerText',
            renderTypeList: ['hidden'],
            label: '',
            value: true,
            valueType: 'boolean'
          },
          {
            key: 'quoteTemplate',
            renderTypeList: ['hidden'],
            label: '',
            valueType: 'string'
          },
          {
            key: 'quotePrompt',
            renderTypeList: ['hidden'],
            label: '',
            valueType: 'string'
          },
          {
            key: 'systemPrompt',
            renderTypeList: ['textarea', 'reference'],
            max: 3000,
            valueType: 'string',
            label: 'core.ai.Prompt',
            description: 'core.app.tip.chatNodeSystemPromptTip',
            placeholder: 'core.app.tip.chatNodeSystemPromptTip'
          },
          {
            key: 'history',
            renderTypeList: ['numberInput', 'reference'],
            valueType: 'chatHistory',
            label: 'core.module.input.label.chat history',
            description: '最多携带多少轮对话记录',
            required: true,
            min: 0,
            max: 50,
            value: 6
          },
          {
            key: 'userChatInput',
            renderTypeList: ['reference', 'textarea'],
            valueType: 'string',
            label: 'core.module.input.label.user question',
            required: true,
            toolDescription: 'core.module.input.label.user question',
            value: ['448745', 'userChatInput']
          },
          {
            key: 'quoteQA',
            renderTypeList: ['settingDatasetQuotePrompt'],
            label: '',
            debugLabel: 'core.common.workflow.Dataset quote',
            description: '',
            valueType: 'datasetQuote'
          }
        ],
        outputs: [
          {
            id: 'history',
            key: 'history',
            required: true,
            label: 'core.module.output.label.New context',
            description: 'core.module.output.description.New context',
            valueType: 'chatHistory',
            type: 'static'
          },
          {
            id: 'answerText',
            key: 'answerText',
            required: true,
            label: 'core.module.output.label.Ai response content',
            description: 'core.module.output.description.Ai response content',
            valueType: 'string',
            type: 'static'
          }
        ]
      }
    ],
    edges: [
      {
        source: '448745',
        target: 'tJQy3Rdob9nJ',
        sourceHandle: '448745-source-right',
        targetHandle: 'tJQy3Rdob9nJ-target-left'
      }
    ]
  }
];

export const pluginTemplates: TemplateType = [
  {
    id: 'plugin-simple',
    avatar: '/imgs/workflow/AI.png',
    name: '默认模板',
    intro: '标准的插件初始模板',
    type: AppTypeEnum.plugin,
    modules: [
      {
        nodeId: 'pluginInput',
        name: '自定义插件输入',
        avatar: '/imgs/workflow/input.png',
        flowNodeType: FlowNodeTypeEnum.pluginInput,
        showStatus: false,
        position: {
          x: 616.4226348688949,
          y: -165.05298493910115
        },
        version: '481',
        inputs: [],
        outputs: []
      },
      {
        nodeId: 'pluginOutput',
        name: '自定义插件输出',
        avatar: '/imgs/workflow/output.png',
        flowNodeType: FlowNodeTypeEnum.pluginOutput,
        showStatus: false,
        position: {
          x: 1607.7142331269126,
          y: -151.8669210746189
        },
        version: '481',
        inputs: [],
        outputs: []
      }
    ],
    edges: []
  }
];

export const defaultAppTemplates = [
  simpleBotTemplates[0],
  simpleBotTemplates[1],
  workflowTemplates[0],
  workflowTemplates[1]
];
