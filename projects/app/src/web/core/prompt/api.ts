import { GET, POST, PUT, DELETE } from '@/web/common/api/request';
import type { PromptTemplate } from '@fastgpt/service/core/prompt/schema';

export const getTemplateList = (data: {
  type?: 'simple' | 'workflow';
  searchText?: string;
  isPublic?: boolean;
  pageNum?: number;
  pageSize?: number;
}) => {
  const { type, searchText, isPublic, pageNum = 1, pageSize = 12 } = data;
  return GET<{
    pageNum: number;
    pageSize: number;
    total: number;
    list: (PromptTemplate & { contents: any[] })[];
  }>('/core/prompt/list', {
    pageNum,
    pageSize,
    type,
    searchText,
    isPublic: isPublic ? 'true' : undefined
  });
};

export const createTemplate = (data: {
  template: {
    title: string;
    description?: string;
    tags?: string[];
    type: 'simple' | 'workflow';
    isPublic?: boolean;
  };
  contents: {
    title: string;
    content: string;
    order: number;
    variables?: {
      name: string;
      description?: string;
      type: 'string' | 'number' | 'boolean';
      required: boolean;
    }[];
  }[];
}) => POST('/core/prompt/create', data);

export const updateTemplate = (data: {
  id: string;
  template?: {
    title?: string;
    description?: string;
    tags?: string[];
    type?: 'simple' | 'workflow';
    isPublic?: boolean;
  };
  contents?: {
    id?: string;
    data: {
      title?: string;
      content?: string;
      order?: number;
      variables?: {
        name: string;
        description?: string;
        type: 'string' | 'number' | 'boolean';
        required: boolean;
      }[];
    };
  }[];
}) => POST('/core/prompt/update', data);

export const deleteTemplate = (id: string) => DELETE(`/core/prompt/delete?id=${id}`);

export const getTemplateDetail = (id: string) =>
  GET<PromptTemplate & { contents: any[] }>(`/core/prompt/detail?id=${id}`);

export const getPromptTemplateList = () => GET<any[]>('/core/prompt/list');

export const getPromptTemplateDetail = (id: string) => GET<any>(`/core/prompt/detail?id=${id}`);

export const createPromptTemplate = (data: { title: string; description: string }) =>
  POST<string>('/core/prompt/create', data);

export const updatePromptTemplate = (id: string, data: { title?: string; description?: string }) =>
  PUT(`/core/prompt/update?id=${id}`, data);

export const deletePromptTemplate = (id: string) => DELETE(`/core/prompt/delete?id=${id}`);

export const addPromptTemplateItem = (
  templateId: string,
  data: { title: string; content: string }
) => POST(`/core/prompt/item/add?templateId=${templateId}`, data);

export const updatePromptTemplateItem = (
  templateId: string,
  itemId: string,
  data: { title?: string; content?: string }
) => PUT(`/core/prompt/item/update?templateId=${templateId}&itemId=${itemId}`, data);

export const deletePromptTemplateItem = (templateId: string, itemId: string) =>
  DELETE(`/core/prompt/item/delete?templateId=${templateId}&itemId=${itemId}`);
