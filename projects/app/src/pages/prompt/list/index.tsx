import React, { useCallback, useState } from 'react';
import { Box, Flex, Grid, useDisclosure, Text, Button } from '@chakra-ui/react';
import { useI18n } from '@/web/context/I18n';
import { useContextSelector } from 'use-context-selector';
import { useToast } from '@fastgpt/web/hooks/useToast';
import { useUserStore } from '@/web/support/user/useUserStore';
import { useLoading } from '@fastgpt/web/hooks/useLoading';
import { getTemplateList } from '@/web/core/prompt/api';
import PageContainer from '@/components/PageContainer';
import TemplateList from './components/TemplateList';
import { AddTemplateModal } from './components/AddTemplateModal';
import SearchBar from './components/SearchBar';
import MyIcon from '@fastgpt/web/components/common/Icon';
import { serviceSideProps } from '@/web/common/utils/i18n';
import { PromptListContext } from './components/context';
import { useRouter } from 'next/router';
import PromptListContextProvider from './components/context';

const PromptList = () => {
  const { promptT } = useI18n();
  const { toast } = useToast();
  const { Loading } = useLoading();
  const { userInfo } = useUserStore();
  const addTemplateModal = useDisclosure();
  const router = useRouter();

  const {
    templates,
    searchText,
    setSearchText,
    selectedType,
    isPublic,
    setIsPublic,
    isFetching,
    refetch
  } = useContextSelector(PromptListContext, (v) => v);

  return (
    <PageContainer>
      <Flex flexDirection={'column'} h={'100%'}>
        <Box px={5} pt={4}>
          <SearchBar
            searchText={searchText}
            onSearchChange={setSearchText}
            type={selectedType || 'simple'}
            onTypeChange={(type) => {
              router.push({
                query: {
                  ...router.query,
                  type
                }
              });
            }}
            isPublic={isPublic}
            onPublicChange={setIsPublic}
          />
        </Box>
        <Box px={5} py={4}>
          <Grid
            gap={4}
            templateColumns={['1fr', '1fr 1fr', '1fr 1fr 1fr', '1fr 1fr 1fr 1fr']}
            w={'100%'}
          >
            {templates.list.length === 0 ? (
              <Flex
                direction="column"
                align="center"
                justify="center"
                h="200px"
                w="100%"
                gridColumn="1/-1"
              >
                <Text color="gray.500" mb={4}>
                  {promptT('No Templates')}
                </Text>
                <Button
                  colorScheme="primary"
                  leftIcon={<MyIcon name="common/addCircleLight" w={'16px'} />}
                  onClick={addTemplateModal.onOpen}
                >
                  {promptT('Create First Template')}
                </Button>
              </Flex>
            ) : (
              <TemplateList
                templates={templates.list}
                onRefresh={refetch}
                onAddTemplate={addTemplateModal.onOpen}
              />
            )}
          </Grid>
        </Box>
      </Flex>

      {addTemplateModal.isOpen && (
        <AddTemplateModal onClose={addTemplateModal.onClose} onSuccess={refetch} />
      )}

      <Loading loading={isFetching} />
    </PageContainer>
  );
};

function ContextRender() {
  return (
    <PromptListContextProvider>
      <PromptList />
    </PromptListContextProvider>
  );
}

export default ContextRender;

export async function getServerSideProps(content: any) {
  return {
    props: {
      ...(await serviceSideProps(content, ['prompt', 'user']))
    }
  };
}
