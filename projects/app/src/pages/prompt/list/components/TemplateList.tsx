import React from 'react';
import { Box, Button, Center, Grid } from '@chakra-ui/react';
import { useI18n } from '@/web/context/I18n';
import MyIcon from '@fastgpt/web/components/common/Icon';
import type { PromptTemplate } from '@fastgpt/service/core/prompt/schema';
import TemplateCard from './TemplateCard';

type Props = {
  templates: (PromptTemplate & { contents: any[] })[];
  onRefresh: () => void;
  onAddTemplate: () => void;
};

const TemplateList = ({ templates, onRefresh, onAddTemplate }: Props) => {
  const { promptT } = useI18n();

  return (
    <>
      {templates.map((template) => (
        <TemplateCard key={template._id} template={template} onRefresh={onRefresh} />
      ))}

      <Center
        border={'1px dashed'}
        borderColor={'gray.200'}
        borderRadius={'md'}
        h={'200px'}
        cursor={'pointer'}
        _hover={{
          borderColor: 'primary.500',
          '& > button': {
            bg: 'primary.500',
            color: 'white'
          }
        }}
        onClick={onAddTemplate}
      >
        <Button variant={'outline'} leftIcon={<MyIcon name="common/addCircleLight" w={'16px'} />}>
          {promptT('Add Template')}
        </Button>
      </Center>
    </>
  );
};

export default TemplateList;
