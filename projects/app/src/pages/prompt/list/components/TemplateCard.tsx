import React from 'react';
import {
  Box,
  Card,
  CardBody,
  Flex,
  IconButton,
  Menu,
  MenuButton,
  MenuItem,
  MenuList,
  Text,
  useDisclosure,
  Badge
} from '@chakra-ui/react';
import { useI18n } from '@/web/context/I18n';
import { useToast } from '@fastgpt/web/hooks/useToast';
import type { PromptTemplate } from '@fastgpt/service/core/prompt/schema';
import MyIcon from '@fastgpt/web/components/common/Icon';
import { deleteTemplate, updateTemplate } from '@/web/core/prompt/api';
import { EditTemplateModal } from './EditTemplateModal';

type Props = {
  template: PromptTemplate & { contents: any[] };
  onRefresh: () => void;
};

const TemplateCard = ({ template, onRefresh }: Props) => {
  const { promptT } = useI18n();
  const { toast } = useToast();
  const editTemplateModal = useDisclosure();

  const handleDelete = async () => {
    try {
      if (!window.confirm(promptT('Delete Template Confirm'))) return;
      await deleteTemplate(template._id);
      toast({
        title: promptT('Delete Template Success'),
        status: 'success'
      });
      onRefresh();
    } catch (error) {
      toast({
        title: String(error),
        status: 'error'
      });
    }
  };

  const handleTogglePublic = async () => {
    try {
      await updateTemplate({
        id: template._id,
        template: {
          isPublic: !template.isPublic
        }
      });
      toast({
        title: template.isPublic ? promptT('Make Private Success') : promptT('Make Public Success'),
        status: 'success'
      });
      onRefresh();
    } catch (error) {
      toast({
        title: String(error),
        status: 'error'
      });
    }
  };

  return (
    <Card
      position={'relative'}
      h={'200px'}
      border={'1px solid'}
      borderColor={'gray.200'}
      _hover={{
        boxShadow: 'md',
        borderColor: 'primary.500',
        transform: 'scale(1.01)'
      }}
      transition={'all 0.2s'}
      cursor={'pointer'}
    >
      <CardBody>
        <Flex h={'100%'} flexDirection={'column'}>
          <Flex justifyContent={'space-between'} alignItems={'center'} mb={2}>
            <Text fontSize={'lg'} fontWeight={'bold'} noOfLines={1}>
              {template.title}
              {template.isPublic && (
                <Badge ml={2} colorScheme="green">
                  {promptT('Public')}
                </Badge>
              )}
            </Text>

            <Menu>
              <MenuButton
                as={IconButton}
                size={'sm'}
                variant={'ghost'}
                icon={<MyIcon name="common/moreLight" w={'16px'} />}
                aria-label={''}
              />
              <MenuList>
                <MenuItem
                  icon={<MyIcon name="edit" w={'16px'} />}
                  onClick={editTemplateModal.onOpen}
                >
                  {promptT('Edit Template')}
                </MenuItem>
                <MenuItem icon={<MyIcon name="delete" w={'16px'} />} onClick={handleDelete}>
                  {promptT('Delete Template')}
                </MenuItem>
              </MenuList>
            </Menu>
          </Flex>

          <Box flex={1} overflow={'hidden'}>
            <Text noOfLines={2} color={'gray.500'} fontSize={'sm'}>
              {template.description}
            </Text>
          </Box>

          <Flex mt={2} gap={2}>
            {template.tags?.map((tag) => (
              <Box
                key={tag}
                px={2}
                py={1}
                bg={'primary.50'}
                color={'primary.600'}
                fontSize={'xs'}
                borderRadius={'full'}
              >
                {tag}
              </Box>
            ))}
          </Flex>
        </Flex>
      </CardBody>

      {editTemplateModal.isOpen && (
        <EditTemplateModal
          template={template}
          onClose={editTemplateModal.onClose}
          onSuccess={onRefresh}
        />
      )}
    </Card>
  );
};

export default TemplateCard;
