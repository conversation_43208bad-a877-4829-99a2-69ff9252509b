import React, { useCallback, useEffect, useState } from 'react';
import { createContext } from 'use-context-selector';
import { useRouter } from 'next/router';
import { useQuery } from '@tanstack/react-query';
import { useToast } from '@fastgpt/web/hooks/useToast';
import { getTemplateList } from '@/web/core/prompt/api';
import type { PromptTemplate } from '@fastgpt/service/core/prompt/schema';

type ContextType = {
  templates: {
    pageNum: number;
    pageSize: number;
    total: number;
    list: (PromptTemplate & { contents: any[] })[];
  };
  searchText: string;
  setSearchText: (text: string) => void;
  selectedType: 'simple' | 'workflow' | undefined;
  isPublic: boolean;
  setIsPublic: (isPublic: boolean) => void;
  isFetching: boolean;
  refetch: () => void;
};

export const PromptListContext = createContext<ContextType>({
  templates: {
    pageNum: 1,
    pageSize: 12,
    total: 0,
    list: []
  },
  searchText: '',
  setSearchText: () => {},
  selectedType: undefined,
  isPublic: false,
  setIsPublic: () => {},
  isFetching: false,
  refetch: () => {}
});

const PromptListContextProvider = ({ children }: { children: React.ReactNode }) => {
  const router = useRouter();
  const { toast } = useToast();
  const [searchText, setSearchText] = useState('');
  const [isPublic, setIsPublic] = useState(false);

  const selectedType = router.query?.type as 'simple' | 'workflow' | undefined;

  // fetch templates
  const {
    data: templates = {
      pageNum: 1,
      pageSize: 12,
      total: 0,
      list: []
    },
    isFetching,
    refetch
  } = useQuery(
    ['getTemplateList', selectedType, searchText, isPublic],
    () =>
      getTemplateList({
        type: selectedType,
        searchText,
        isPublic,
        pageNum: 1,
        pageSize: 12
      }),
    {
      onError(err) {
        toast({
          title: String(err),
          status: 'error'
        });
      }
    }
  );

  return (
    <PromptListContext.Provider
      value={{
        templates,
        searchText,
        setSearchText,
        selectedType,
        isPublic,
        setIsPublic,
        isFetching,
        refetch
      }}
    >
      {children}
    </PromptListContext.Provider>
  );
};

export default PromptListContextProvider;
