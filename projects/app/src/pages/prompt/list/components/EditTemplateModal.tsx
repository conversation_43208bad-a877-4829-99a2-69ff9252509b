import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>nt,
  <PERSON>dal<PERSON>eader,
  Modal<PERSON>ody,
  ModalCloseButton
} from '@chakra-ui/react';
import { useI18n } from '@/web/context/I18n';
import { useToast } from '@fastgpt/web/hooks/useToast';
import type { PromptTemplate } from '@fastgpt/service/core/prompt/schema';
import { updateTemplate } from '@/web/core/prompt/api';
import TemplateForm from './TemplateForm';

type Props = {
  template: PromptTemplate & { contents: any[] };
  onClose: () => void;
  onSuccess: () => void;
};

export const EditTemplateModal = ({ template, onClose, onSuccess }: Props) => {
  const { promptT } = useI18n();
  const { toast } = useToast();

  const handleSubmit = async (data: any) => {
    try {
      await updateTemplate({
        id: template._id,
        template: {
          title: data.title,
          description: data.description,
          tags: data.tags,
          type: data.type
        },
        contents: data.contents.map((content: any) => ({
          id: content.id,
          data: {
            title: content.title,
            content: content.content,
            order: content.order,
            variables: content.variables
          }
        }))
      });
      toast({
        title: promptT('Update Template Success'),
        status: 'success'
      });
      onSuccess();
      onClose();
    } catch (error) {
      toast({
        title: String(error),
        status: 'error'
      });
    }
  };

  return (
    <Modal isOpen={true} onClose={onClose} size="2xl">
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>{promptT('Edit Template')}</ModalHeader>
        <ModalCloseButton />
        <ModalBody pb={6}>
          <TemplateForm
            defaultValues={{
              title: template.title,
              description: template.description,
              tags: template.tags,
              type: template.type,
              contents: template.contents
            }}
            onSubmit={handleSubmit}
            onCancel={onClose}
          />
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};
