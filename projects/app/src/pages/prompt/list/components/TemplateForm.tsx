import React from 'react';
import {
  Box,
  Button,
  Flex,
  FormControl,
  FormLabel,
  Input,
  Radio,
  RadioGroup,
  Stack,
  Textarea,
  VStack
} from '@chakra-ui/react';
import { useI18n } from '@/web/context/I18n';
import { useForm, useFieldArray } from 'react-hook-form';
import MyIcon from '@fastgpt/web/components/common/Icon';

type FormData = {
  title: string;
  description?: string;
  tags?: string[];
  type: 'simple' | 'workflow';
  contents: {
    title: string;
    content: string;
    order: number;
    variables?: {
      name: string;
      description?: string;
      type: 'string' | 'number' | 'boolean';
      required: boolean;
    }[];
  }[];
};

type Props = {
  defaultValues?: Partial<FormData>;
  onSubmit: (data: FormData) => void;
  onCancel: () => void;
};

const TemplateForm = ({ defaultValues, onSubmit, onCancel }: Props) => {
  const { promptT } = useI18n();
  const {
    register,
    control,
    handleSubmit,
    watch,
    formState: { errors, isSubmitting }
  } = useForm<FormData>({
    defaultValues: {
      type: 'simple',
      contents: [{ title: '', content: '', order: 0 }],
      ...defaultValues
    }
  });

  const {
    fields: contents,
    append,
    remove
  } = useFieldArray({
    control,
    name: 'contents'
  });

  const type = watch('type');

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <VStack spacing={6}>
        <FormControl isRequired>
          <FormLabel>{promptT('Template Type')}</FormLabel>
          <RadioGroup defaultValue={type}>
            <Stack direction="row" spacing={4}>
              <Radio {...register('type')} value="simple">
                {promptT('Simple')}
              </Radio>
              <Radio {...register('type')} value="workflow">
                {promptT('Workflow')}
              </Radio>
            </Stack>
          </RadioGroup>
        </FormControl>

        <FormControl isRequired>
          <FormLabel>{promptT('Template Title')}</FormLabel>
          <Input {...register('title', { required: true })} />
        </FormControl>

        <FormControl>
          <FormLabel>{promptT('Template Description')}</FormLabel>
          <Textarea {...register('description')} />
        </FormControl>

        <Box w="100%">
          <Flex justify="space-between" align="center" mb={4}>
            <FormLabel mb={0}>{promptT('Template Content')}</FormLabel>
            {type === 'workflow' && (
              <Button
                size="sm"
                leftIcon={<MyIcon name="common/addCircleLight" />}
                onClick={() => append({ title: '', content: '', order: contents.length })}
              >
                {promptT('Add Content')}
              </Button>
            )}
          </Flex>

          <VStack spacing={4} align="stretch">
            {contents.map((field, index) => (
              <Box key={field.id} position="relative" p={4} borderWidth={1} borderRadius="md">
                <FormControl isRequired>
                  <FormLabel>{promptT('Content Title')}</FormLabel>
                  <Input {...register(`contents.${index}.title`, { required: true })} />
                </FormControl>

                <FormControl isRequired mt={4}>
                  <FormLabel>{promptT('Content')}</FormLabel>
                  <Textarea
                    {...register(`contents.${index}.content`, { required: true })}
                    rows={6}
                  />
                </FormControl>

                {type === 'workflow' && contents.length > 1 && (
                  <Button
                    position="absolute"
                    top={2}
                    right={2}
                    size="sm"
                    colorScheme="red"
                    variant="ghost"
                    onClick={() => remove(index)}
                  >
                    <MyIcon name="delete" />
                  </Button>
                )}
              </Box>
            ))}
          </VStack>
        </Box>

        <Flex gap={4} w="100%" justify="flex-end">
          <Button onClick={onCancel}>{promptT('Cancel')}</Button>
          <Button colorScheme="primary" type="submit" isLoading={isSubmitting}>
            {promptT('Confirm')}
          </Button>
        </Flex>
      </VStack>
    </form>
  );
};

export default TemplateForm;
