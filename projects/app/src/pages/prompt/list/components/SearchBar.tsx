import React from 'react';
import {
  Flex,
  Input,
  InputGroup,
  InputLeftElement,
  Select,
  Switch,
  FormControl,
  FormLabel
} from '@chakra-ui/react';
import { useI18n } from '@/web/context/I18n';
import MyIcon from '@fastgpt/web/components/common/Icon';

type Props = {
  searchText: string;
  onSearchChange: (text: string) => void;
  type: 'simple' | 'workflow';
  onTypeChange: (type: 'simple' | 'workflow') => void;
  isPublic: boolean;
  onPublicChange: (isPublic: boolean) => void;
};

const SearchBar = ({
  searchText,
  onSearchChange,
  type,
  onTypeChange,
  isPublic,
  onPublicChange
}: Props) => {
  const { promptT } = useI18n();

  return (
    <Flex gap={4} mb={4} flexWrap="wrap">
      <InputGroup maxW="300px">
        <InputLeftElement h={'full'} alignItems={'center'} display={'flex'}>
          <MyIcon name="common/searchLight" w={'1rem'} />
        </InputLeftElement>
        <Input
          value={searchText}
          onChange={(e) => onSearchChange(e.target.value)}
          placeholder={promptT('search_prompt')}
          maxLength={30}
          bg={'white'}
        />
      </InputGroup>

      <Select
        maxW="200px"
        value={type}
        onChange={(e) => onTypeChange(e.target.value as 'simple' | 'workflow')}
      >
        <option value="simple">{promptT('type.Simple')}</option>
        <option value="workflow">{promptT('type.Workflow')}</option>
      </Select>

      <FormControl display="flex" alignItems="center" maxW="150px">
        <FormLabel htmlFor="public-switch" mb={0}>
          {promptT('Public')}
        </FormLabel>
        <Switch
          id="public-switch"
          isChecked={isPublic}
          onChange={(e) => onPublicChange(e.target.checked)}
        />
      </FormControl>
    </Flex>
  );
};

export default SearchBar;
