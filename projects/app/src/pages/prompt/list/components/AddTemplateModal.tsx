import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>dalOverlay,
  Modal<PERSON>ontent,
  ModalHeader,
  ModalBody,
  ModalCloseButton
} from '@chakra-ui/react';
import { useI18n } from '@/web/context/I18n';
import { useToast } from '@fastgpt/web/hooks/useToast';
import { createTemplate } from '@/web/core/prompt/api';
import TemplateForm from './TemplateForm';

type Props = {
  onClose: () => void;
  onSuccess: () => void;
};

export const AddTemplateModal = ({ onClose, onSuccess }: Props) => {
  const { promptT } = useI18n();
  const { toast } = useToast();

  const handleSubmit = async (data: any) => {
    try {
      await createTemplate(data);
      toast({
        title: promptT('Create Template Success'),
        status: 'success'
      });
      onSuccess();
      onClose();
    } catch (error) {
      toast({
        title: String(error),
        status: 'error'
      });
    }
  };

  return (
    <Modal isOpen={true} onClose={onClose} size="2xl">
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>{promptT('Add Template')}</ModalHeader>
        <ModalCloseButton />
        <ModalBody pb={6}>
          <TemplateForm onSubmit={handleSubmit} onCancel={onClose} />
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};
