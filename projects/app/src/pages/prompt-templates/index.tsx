import React, { useState } from 'react';
import {
  Box,
  Button,
  Flex,
  Heading,
  Input,
  InputGroup,
  InputLeftElement,
  SimpleGrid,
  Card,
  CardBody,
  Text,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  FormControl,
  FormLabel,
  Textarea,
  IconButton,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  useToast
} from '@chakra-ui/react';
import { useRouter } from 'next/router';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import PageContainer from '@/components/PageContainer';
import MyIcon from '@fastgpt/web/components/common/Icon';
import { PromptTemplatesProvider, usePromptTemplates } from './context';
import { formatTimeToChatTime } from '@fastgpt/global/common/string/time';

// 创建模板表单类型
type TemplateFormData = {
  title: string;
  description: string;
};

// 提示词模板列表组件
const PromptTemplateList = () => {
  const { t } = useTranslation();
  const router = useRouter();
  const toast = useToast();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { templates, isLoading, createTemplate, deleteTemplate } = usePromptTemplates();
  const [searchText, setSearchText] = useState('');
  const [formData, setFormData] = useState<TemplateFormData>({
    title: '',
    description: ''
  });

  // 处理表单变化
  const handleFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value
    }));
  };

  // 处理创建模板
  const handleCreateTemplate = async () => {
    if (!formData.title.trim()) {
      toast({
        title: t('common:prompt.Title is required'),
        status: 'warning',
        duration: 3000
      });
      return;
    }

    try {
      const id = await createTemplate({
        title: formData.title,
        description: formData.description
      });

      // 重置表单并关闭弹窗
      setFormData({ title: '', description: '' });
      onClose();

      // 导航到新创建的模板详情页
      router.push(`/prompt-templates/${id}`);
    } catch (error) {
      console.error('Failed to create template:', error);
    }
  };

  // 处理删除模板
  const handleDeleteTemplate = async (id: string) => {
    if (confirm(t('common:prompt.Confirm Delete'))) {
      try {
        await deleteTemplate(id);
      } catch (error) {
        console.error('Failed to delete template:', error);
      }
    }
  };

  // 过滤模板
  const filteredTemplates = templates.filter((template) =>
    template.title.toLowerCase().includes(searchText.toLowerCase())
  );

  return (
    <PageContainer isLoading={isLoading} insertProps={{ px: [4, 6] }}>
      {/* 标题和添加按钮 */}
      <Flex justifyContent="space-between" alignItems="center" mb={6}>
        <Heading as="h1" size="lg">
          {t('common:prompt.title')}
        </Heading>
        <Button colorScheme="primary" leftIcon={<MyIcon name="add" w="14px" />} onClick={onOpen}>
          {t('common:prompt.Add Template')}
        </Button>
      </Flex>

      {/* 搜索框 */}
      <InputGroup mb={6} maxW="500px">
        <InputLeftElement pointerEvents="none">
          <MyIcon name="search" w="16px" color="gray.400" />
        </InputLeftElement>
        <Input
          placeholder={t('common:prompt.search_prompt')}
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
        />
      </InputGroup>

      {/* 模板列表 */}
      {isLoading ? (
        <Flex justify="center" align="center" h="200px">
          <Text>{t('common:prompt.Loading')}</Text>
        </Flex>
      ) : filteredTemplates.length === 0 ? (
        <Flex
          direction="column"
          justify="center"
          align="center"
          h="200px"
          bg="gray.50"
          borderRadius="md"
        >
          <MyIcon name="empty" w="48px" h="48px" color="gray.500" />
          <Text mt={2} color="gray.500">
            {searchText
              ? t('common:prompt.No matching templates')
              : t('common:prompt.No templates yet')}
          </Text>
        </Flex>
      ) : (
        <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={4}>
          {filteredTemplates.map((template) => (
            <Card
              key={template._id}
              cursor="pointer"
              _hover={{ shadow: 'md' }}
              onClick={() => router.push(`/prompt-templates/${template._id}`)}
              position="relative"
            >
              <CardBody p={4}>
                <Flex position="absolute" top={2} right={2}>
                  <Menu>
                    <MenuButton
                      as={IconButton}
                      aria-label="Options"
                      icon={<MyIcon name="more" w="14px" />}
                      variant="ghost"
                      size="sm"
                      onClick={(e) => e.stopPropagation()}
                    />
                    <MenuList>
                      <MenuItem
                        icon={<MyIcon name="delete" w="14px" />}
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteTemplate(template._id);
                        }}
                      >
                        {t('common:prompt.Delete')}
                      </MenuItem>
                    </MenuList>
                  </Menu>
                </Flex>
                <Heading as="h3" size="md" mb={2} noOfLines={1}>
                  {template.title}
                </Heading>
                {template.description && (
                  <Text color="gray.600" mb={3} noOfLines={2}>
                    {template.description}
                  </Text>
                )}
                <Text fontSize="sm" color="gray.500">
                  {formatTimeToChatTime(template.createdAt)}
                </Text>
              </CardBody>
            </Card>
          ))}
        </SimpleGrid>
      )}

      {/* 创建模板弹窗 */}
      <Modal isOpen={isOpen} onClose={onClose}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>{t('common:prompt.Create Template')}</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <FormControl mb={4} isRequired>
              <FormLabel>{t('common:prompt.Title')}</FormLabel>
              <Input
                name="title"
                placeholder={t('common:prompt.Enter template title')}
                value={formData.title}
                onChange={handleFormChange}
              />
            </FormControl>
            <FormControl>
              <FormLabel>{t('common:prompt.Description')}</FormLabel>
              <Textarea
                name="description"
                placeholder={t('common:prompt.Enter template description')}
                value={formData.description}
                onChange={handleFormChange}
                rows={4}
              />
            </FormControl>
          </ModalBody>
          <ModalFooter>
            <Button variant="ghost" mr={3} onClick={onClose}>
              {t('common:prompt.Cancel')}
            </Button>
            <Button colorScheme="primary" onClick={handleCreateTemplate}>
              {t('common:prompt.Create')}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </PageContainer>
  );
};

// 页面容器
const PromptTemplatesPage = () => {
  return (
    <PromptTemplatesProvider>
      <PromptTemplateList />
    </PromptTemplatesProvider>
  );
};

export async function getServerSideProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common']))
    }
  };
}

export default PromptTemplatesPage;
