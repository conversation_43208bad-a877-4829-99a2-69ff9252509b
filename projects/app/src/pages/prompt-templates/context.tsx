import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';
import { useToast } from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import { useRouter } from 'next/router';
import { useLoading } from '@fastgpt/web/hooks/useLoading';
import {
  getPromptTemplateList,
  createPromptTemplate,
  updatePromptTemplate,
  deletePromptTemplate
} from '@/web/core/prompt/api';

// 定义提示词模板类型
export type PromptTemplate = {
  _id: string;
  title: string;
  description: string;
  createdAt: string;
};

// 创建上下文类型
type PromptTemplatesContextType = {
  templates: PromptTemplate[];
  isLoading: boolean;
  refresh: () => Promise<void>;
  createTemplate: (data: { title: string; description: string }) => Promise<string>;
  updateTemplate: (id: string, data: { title?: string; description?: string }) => Promise<void>;
  deleteTemplate: (id: string) => Promise<void>;
};

// 创建上下文
const PromptTemplatesContext = createContext<PromptTemplatesContextType | undefined>(undefined);

// 提供者组件
export const PromptTemplatesProvider = ({ children }: { children: ReactNode }) => {
  const { t } = useTranslation();
  const router = useRouter();
  const toast = useToast();
  const { isLoading, setIsLoading } = useLoading();
  const [templates, setTemplates] = useState<PromptTemplate[]>([]);

  // 获取提示词模板列表
  const refresh = useCallback(async () => {
    try {
      setIsLoading(true);
      const data = await getPromptTemplateList();
      setTemplates(data);
    } catch (error) {
      console.error('Failed to fetch templates:', error);
      toast({
        title: t('common:prompt.Failed to load templates'),
        status: 'error',
        duration: 3000
      });
    } finally {
      setIsLoading(false);
    }
  }, [setIsLoading, toast, t]);

  // 创建提示词模板
  const createTemplate = useCallback(
    async (data: { title: string; description: string }) => {
      try {
        setIsLoading(true);
        const id = await createPromptTemplate(data);
        toast({
          title: t('common:prompt.Create Success'),
          status: 'success',
          duration: 2000
        });
        await refresh();
        return id;
      } catch (error) {
        console.error('Failed to create template:', error);
        toast({
          title: t('common:prompt.Create Failed'),
          status: 'error',
          duration: 3000
        });
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [setIsLoading, toast, t, refresh]
  );

  // 更新提示词模板
  const updateTemplate = useCallback(
    async (id: string, data: { title?: string; description?: string }) => {
      try {
        setIsLoading(true);
        await updatePromptTemplate(id, data);
        toast({
          title: t('common:prompt.Update Success'),
          status: 'success',
          duration: 2000
        });
        await refresh();
      } catch (error) {
        console.error('Failed to update template:', error);
        toast({
          title: t('common:prompt.Update Failed'),
          status: 'error',
          duration: 3000
        });
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [setIsLoading, toast, t, refresh]
  );

  // 删除提示词模板
  const deleteTemplate = useCallback(
    async (id: string) => {
      try {
        setIsLoading(true);
        await deletePromptTemplate(id);
        toast({
          title: t('common:prompt.Delete Success'),
          status: 'success',
          duration: 2000
        });
        await refresh();
      } catch (error) {
        console.error('Failed to delete template:', error);
        toast({
          title: t('common:prompt.Delete Failed'),
          status: 'error',
          duration: 3000
        });
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [setIsLoading, toast, t, refresh]
  );

  // 初始加载
  React.useEffect(() => {
    refresh();
  }, [refresh]);

  const contextValue: PromptTemplatesContextType = {
    templates,
    isLoading,
    refresh,
    createTemplate,
    updateTemplate,
    deleteTemplate
  };

  return (
    <PromptTemplatesContext.Provider value={contextValue}>
      {children}
    </PromptTemplatesContext.Provider>
  );
};

// 自定义钩子
export const usePromptTemplates = () => {
  const context = useContext(PromptTemplatesContext);
  if (context === undefined) {
    throw new Error('usePromptTemplates must be used within a PromptTemplatesProvider');
  }
  return context;
};
