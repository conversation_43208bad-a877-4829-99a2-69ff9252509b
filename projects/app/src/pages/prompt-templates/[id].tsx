import React, { useState, useEffect } from 'react';
import {
  Box,
  <PERSON>ton,
  Card,
  CardHeader,
  CardBody,
  Flex,
  Heading,
  IconButton,
  Text,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  FormControl,
  FormLabel,
  Input,
  Textarea,
  useToast,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  Tabs,
  TabList,
  Tab,
  TabPanels,
  TabPanel
} from '@chakra-ui/react';
import { useRouter } from 'next/router';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import PageContainer from '@/components/PageContainer';
import MyIcon from '@fastgpt/web/components/common/Icon';
import { useLoading } from '@fastgpt/web/hooks/useLoading';
import {
  getPromptTemplateDetail,
  addPromptTemplateItem,
  updatePromptTemplateItem,
  deletePromptTemplateItem
} from '@/web/core/prompt/api';
import Link from 'next/link';

// 定义提示词模板类型
type PromptTemplateItem = {
  _id: string;
  title: string;
  content: string;
};

type PromptTemplateDetail = {
  _id: string;
  title: string;
  description: string;
  items: PromptTemplateItem[];
  createdAt: string;
  updatedAt: string;
};

const PromptTemplateDetail = () => {
  const { t } = useTranslation();
  const router = useRouter();
  const { id } = router.query;
  const toast = useToast();
  const { isLoading, Loading, setIsLoading } = useLoading();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [template, setTemplate] = useState<PromptTemplateDetail | null>(null);
  const [newItem, setNewItem] = useState({
    title: '',
    content: ''
  });
  const [editingItem, setEditingItem] = useState<PromptTemplateItem | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [tabIndex, setTabIndex] = useState(0);

  // 获取提示词模板详情
  const fetchTemplateDetail = async () => {
    if (!id) return;

    try {
      setIsLoading(true);
      const data = await getPromptTemplateDetail(id as string);
      setTemplate(data);
    } catch (error) {
      console.error('Failed to fetch template detail:', error);
      toast({
        title: t('common:prompt.Failed to load template'),
        status: 'error',
        duration: 3000
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (id) {
      fetchTemplateDetail();
    }
  }, [id]);

  // 添加新的提示词项
  const handleAddItem = async () => {
    if (!newItem.title.trim() || !newItem.content.trim()) {
      toast({
        title: t('common:prompt.Title and content are required'),
        status: 'warning',
        duration: 3000
      });
      return;
    }

    try {
      setIsLoading(true);
      await addPromptTemplateItem(id as string, {
        title: newItem.title,
        content: newItem.content
      });

      toast({
        title: t('common:prompt.Add Content Success'),
        status: 'success',
        duration: 2000
      });

      // 重置表单并关闭弹窗
      setNewItem({ title: '', content: '' });
      onClose();

      // 刷新模板详情
      fetchTemplateDetail();
    } catch (error) {
      console.error('Failed to add item:', error);
      toast({
        title: t('common:prompt.Add Content Failed'),
        status: 'error',
        duration: 3000
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 更新提示词项
  const handleUpdateItem = async () => {
    if (!editingItem || !editingItem.title.trim() || !editingItem.content.trim()) {
      toast({
        title: t('common:prompt.Title and content are required'),
        status: 'warning',
        duration: 3000
      });
      return;
    }

    try {
      setIsLoading(true);
      await updatePromptTemplateItem(id as string, editingItem._id, {
        title: editingItem.title,
        content: editingItem.content
      });

      toast({
        title: t('common:prompt.Update Content Success'),
        status: 'success',
        duration: 2000
      });

      // 重置状态并关闭弹窗
      setEditingItem(null);
      setIsEditing(false);
      onClose();

      // 刷新模板详情
      fetchTemplateDetail();
    } catch (error) {
      console.error('Failed to update item:', error);
      toast({
        title: t('common:prompt.Update Content Failed'),
        status: 'error',
        duration: 3000
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 删除提示词项
  const handleDeleteItem = async (itemId: string) => {
    if (!window.confirm(t('common:prompt.Delete Content Confirm'))) {
      return;
    }

    try {
      setIsLoading(true);
      await deletePromptTemplateItem(id as string, itemId);

      toast({
        title: t('common:prompt.Delete Content Success'),
        status: 'success',
        duration: 2000
      });

      // 刷新模板详情
      fetchTemplateDetail();
    } catch (error) {
      console.error('Failed to delete item:', error);
      toast({
        title: t('common:prompt.Delete Content Failed'),
        status: 'error',
        duration: 3000
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 打开编辑弹窗
  const handleEditClick = (item: PromptTemplateItem) => {
    setEditingItem(item);
    setIsEditing(true);
    onOpen();
  };

  // 复制内容到剪贴板
  const handleCopy = (content: string) => {
    navigator.clipboard.writeText(content).then(
      () => {
        toast({
          title: t('common:prompt.Copy Success'),
          status: 'success',
          duration: 1000
        });
      },
      (err) => {
        toast({
          title: t('common:prompt.Copy Failed'),
          description: String(err),
          status: 'error'
        });
      }
    );
  };

  return (
    <PageContainer>
      <Box p={5}>
        {/* 面包屑导航 */}
        <Breadcrumb mb={4}>
          <BreadcrumbItem>
            <BreadcrumbLink as={Link} href="/prompt-templates">
              {t('common:prompt.title')}
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbItem isCurrentPage>
            <Text>{template?.title || ''}</Text>
          </BreadcrumbItem>
        </Breadcrumb>

        {/* 标题和添加按钮 */}
        <Flex justifyContent="space-between" alignItems="center" mb={4}>
          <Box>
            <Heading as="h1" size="lg" mb={1}>
              {template?.title}
            </Heading>
            {template?.description && (
              <Text color="gray.600" maxW="800px">
                {template.description}
              </Text>
            )}
          </Box>
          <Button
            colorScheme="primary"
            leftIcon={<MyIcon name="add" w="14px" />}
            onClick={() => {
              setIsEditing(false);
              setNewItem({ title: '', content: '' });
              onOpen();
            }}
          >
            {t('common:prompt.Add Content')}
          </Button>
        </Flex>

        {/* 提示词内容列表 */}
        {template?.items && template.items.length > 0 ? (
          <Box mt={6}>
            <Tabs
              variant="enclosed"
              colorScheme="primary"
              index={tabIndex}
              onChange={setTabIndex}
              isLazy
            >
              <TabList>
                {template.items.map((item) => (
                  <Tab key={item._id}>{item.title}</Tab>
                ))}
              </TabList>

              <TabPanels mt={4}>
                {template.items.map((item, index) => (
                  <TabPanel key={item._id} p={0}>
                    <Card boxShadow="sm" borderRadius="md" overflow="hidden" position="relative">
                      <Flex position="absolute" top={2} right={2} zIndex={1}>
                        <IconButton
                          aria-label="Edit content"
                          icon={<MyIcon name="edit" w="14px" />}
                          size="sm"
                          mr={2}
                          onClick={(e) => {
                            e.stopPropagation();
                            handleEditClick(item);
                          }}
                        />
                        <IconButton
                          aria-label="Delete content"
                          icon={<MyIcon name="delete" w="14px" />}
                          size="sm"
                          mr={2}
                          colorScheme="red"
                          variant="ghost"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteItem(item._id);
                          }}
                        />
                        <IconButton
                          aria-label="Copy content"
                          icon={<MyIcon name="copy" w="14px" />}
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleCopy(item.content);
                          }}
                        />
                      </Flex>
                      <CardHeader bg="gray.50" py={3} px={4}>
                        <Text fontWeight="bold">{item.title}</Text>
                      </CardHeader>
                      <CardBody
                        p={4}
                        onClick={() => handleCopy(item.content)}
                        cursor="pointer"
                        _hover={{ bg: 'gray.50' }}
                      >
                        <Box
                          whiteSpace="pre-wrap"
                          p={2}
                          borderRadius="md"
                          fontSize="sm"
                          fontFamily="mono"
                          maxHeight="500px"
                          overflowY="auto"
                        >
                          {item.content}
                        </Box>
                      </CardBody>
                    </Card>
                  </TabPanel>
                ))}
              </TabPanels>
            </Tabs>
          </Box>
        ) : (
          <Flex
            justifyContent="center"
            alignItems="center"
            height="200px"
            bg="gray.50"
            borderRadius="md"
            mt={6}
          >
            <Text color="gray.500">{t('common:prompt.No Content')}</Text>
          </Flex>
        )}
      </Box>

      {/* 添加/编辑提示词项弹窗 */}
      <Modal isOpen={isOpen} onClose={onClose} size="xl">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>
            {isEditing ? t('common:prompt.Edit Content') : t('common:prompt.Add Content')}
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody pb={6}>
            <FormControl isRequired>
              <FormLabel>{t('common:prompt.Content Title')}</FormLabel>
              <Input
                value={isEditing ? editingItem?.title || '' : newItem.title}
                onChange={(e) => {
                  if (isEditing && editingItem) {
                    setEditingItem({ ...editingItem, title: e.target.value });
                  } else {
                    setNewItem({ ...newItem, title: e.target.value });
                  }
                }}
                placeholder={t('common:prompt.Enter content title')}
              />
            </FormControl>

            <FormControl mt={4} isRequired>
              <FormLabel>{t('common:prompt.Content')}</FormLabel>
              <Textarea
                value={isEditing ? editingItem?.content || '' : newItem.content}
                onChange={(e) => {
                  if (isEditing && editingItem) {
                    setEditingItem({ ...editingItem, content: e.target.value });
                  } else {
                    setNewItem({ ...newItem, content: e.target.value });
                  }
                }}
                placeholder={t('common:prompt.Enter content')}
                rows={10}
                fontFamily="mono"
              />
            </FormControl>
          </ModalBody>

          <ModalFooter>
            <Button
              colorScheme="primary"
              mr={3}
              onClick={isEditing ? handleUpdateItem : handleAddItem}
            >
              {isEditing ? t('common:prompt.Update') : t('common:prompt.Add')}
            </Button>
            <Button onClick={onClose}>{t('common:prompt.Cancel')}</Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      <Loading loading={isLoading} />
    </PageContainer>
  );
};

export default PromptTemplateDetail;

export async function getServerSideProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common']))
    }
  };
}
