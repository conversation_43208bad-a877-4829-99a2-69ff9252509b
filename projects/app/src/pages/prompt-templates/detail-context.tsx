import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';
import { useToast } from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import { useRouter } from 'next/router';
import { useLoading } from '@fastgpt/web/hooks/useLoading';
import {
  getPromptTemplateDetail,
  addPromptTemplateItem,
  updatePromptTemplateItem,
  deletePromptTemplateItem
} from '@/web/core/prompt/api';

// 定义提示词项类型
export type PromptTemplateItem = {
  _id: string;
  title: string;
  content: string;
};

// 定义提示词模板详情类型
export type PromptTemplateDetail = {
  _id: string;
  title: string;
  description: string;
  items: PromptTemplateItem[];
  createdAt: string;
  updatedAt: string;
};

// 创建上下文类型
type PromptTemplateDetailContextType = {
  template: PromptTemplateDetail | null;
  isLoading: boolean;
  refresh: () => Promise<void>;
  addItem: (data: { title: string; content: string }) => Promise<string>;
  updateItem: (itemId: string, data: { title?: string; content?: string }) => Promise<void>;
  deleteItem: (itemId: string) => Promise<void>;
};

// 创建上下文
const PromptTemplateDetailContext = createContext<PromptTemplateDetailContextType | undefined>(
  undefined
);

// 提供者组件
export const PromptTemplateDetailProvider = ({
  children,
  templateId
}: {
  children: ReactNode;
  templateId: string;
}) => {
  const { t } = useTranslation();
  const toast = useToast();
  const { isLoading, setIsLoading } = useLoading();
  const [template, setTemplate] = useState<PromptTemplateDetail | null>(null);

  // 获取提示词模板详情
  const refresh = useCallback(async () => {
    if (!templateId) return;

    try {
      setIsLoading(true);
      const data = await getPromptTemplateDetail(templateId);
      setTemplate(data);
    } catch (error) {
      console.error('Failed to fetch template detail:', error);
      toast({
        title: t('common:prompt.Failed to load template'),
        status: 'error',
        duration: 3000
      });
    } finally {
      setIsLoading(false);
    }
  }, [templateId, setIsLoading, toast, t]);

  // 添加提示词项
  const addItem = useCallback(
    async (data: { title: string; content: string }) => {
      if (!templateId) throw new Error('Template ID is required');

      try {
        setIsLoading(true);
        const itemId = await addPromptTemplateItem(templateId, data);
        toast({
          title: t('common:prompt.Add Content Success'),
          status: 'success',
          duration: 2000
        });
        await refresh();
        return itemId;
      } catch (error) {
        console.error('Failed to add item:', error);
        toast({
          title: t('common:prompt.Add Content Failed'),
          status: 'error',
          duration: 3000
        });
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [templateId, setIsLoading, toast, t, refresh]
  );

  // 更新提示词项
  const updateItem = useCallback(
    async (itemId: string, data: { title?: string; content?: string }) => {
      if (!templateId) throw new Error('Template ID is required');

      try {
        setIsLoading(true);
        await updatePromptTemplateItem(templateId, itemId, data);
        toast({
          title: t('common:prompt.Update Content Success'),
          status: 'success',
          duration: 2000
        });
        await refresh();
      } catch (error) {
        console.error('Failed to update item:', error);
        toast({
          title: t('common:prompt.Update Content Failed'),
          status: 'error',
          duration: 3000
        });
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [templateId, setIsLoading, toast, t, refresh]
  );

  // 删除提示词项
  const deleteItem = useCallback(
    async (itemId: string) => {
      if (!templateId) throw new Error('Template ID is required');

      try {
        setIsLoading(true);
        await deletePromptTemplateItem(templateId, itemId);
        toast({
          title: t('common:prompt.Delete Content Success'),
          status: 'success',
          duration: 2000
        });
        await refresh();
      } catch (error) {
        console.error('Failed to delete item:', error);
        toast({
          title: t('common:prompt.Delete Content Failed'),
          status: 'error',
          duration: 3000
        });
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [templateId, setIsLoading, toast, t, refresh]
  );

  // 初始加载
  React.useEffect(() => {
    if (templateId) {
      refresh();
    }
  }, [templateId, refresh]);

  const contextValue: PromptTemplateDetailContextType = {
    template,
    isLoading,
    refresh,
    addItem,
    updateItem,
    deleteItem
  };

  return (
    <PromptTemplateDetailContext.Provider value={contextValue}>
      {children}
    </PromptTemplateDetailContext.Provider>
  );
};

// 自定义钩子
export const usePromptTemplateDetail = () => {
  const context = useContext(PromptTemplateDetailContext);
  if (context === undefined) {
    throw new Error('usePromptTemplateDetail must be used within a PromptTemplateDetailProvider');
  }
  return context;
};
