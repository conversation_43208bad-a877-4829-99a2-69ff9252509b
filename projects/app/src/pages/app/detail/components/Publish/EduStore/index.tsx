import React from 'react';
import { Box, Flex, Image, Center } from '@chakra-ui/react';
import { getPublishStatus } from '@/web/core/app/api';
import { AppPublishStatusEnum } from '@fastgpt/global/core/app/constants';
import { useQuery } from '@tanstack/react-query';
import { useTranslation } from 'next-i18next';
import MyBox from '@fastgpt/web/components/common/MyBox';
import MyTag from '@fastgpt/web/components/common/Tag/index';
import { publishStatusStyle } from '../../constants';

const PublishEduStore = ({ appId }: { appId: string }) => {
  const { t } = useTranslation();
  const {
    data: publishStatus = {},
    isLoading: isGetting,
    refetch
  } = useQuery(['getPublishStatus', appId], () => getPublishStatus(appId));

  const isPublished = publishStatus.towaStoreStatus === AppPublishStatusEnum.published;

  return (
    <MyBox
      isLoading={isGetting}
      display={'flex'}
      flexDirection={'column'}
      h={'100%'}
      position={'relative'}
    >
      <Box display={['block', 'flex']} alignItems={'center'}>
        <Box flex={1}>
          <Flex alignItems={'flex-end'}>
            <Box color={'myGray.900'} fontSize={'lg'}>
              {t('core.app.publish.Edu Store')}
            </Box>
            <MyTag
              py={1}
              showDot
              ml={3}
              type={'borderFill'}
              colorSchema={
                isPublished
                  ? publishStatusStyle.published.colorSchema
                  : publishStatusStyle.unPublish.colorSchema
              }
            >
              {isPublished
                ? t(publishStatusStyle.published.text)
                : t(publishStatusStyle.unPublish.text)}
            </MyTag>
          </Flex>
          <Box fontSize={'mini'} color={'myGray.600'} mt={2}>
            Advanced students can contact the instructor to apply for listing
          </Box>
        </Box>
      </Box>
      <Box maxW="sm" overflow="hidden" mt="30px">
        <Image width={200} src="/imgs/app/fofpro-wechat.png" />
      </Box>
    </MyBox>
  );
};

export default React.memo(PublishEduStore);
