import React, { useState, useCallback, useEffect } from 'react';
import { Center, Flex, useDisclosure } from '@chakra-ui/react';
import { Box, Button, Container, Grid, GridItem, Heading, Text, VStack } from '@chakra-ui/react';
import Image from 'next/image';
import { LoginPageTypeEnum } from '@/web/support/user/login/constants';
import { useSystemStore } from '@/web/common/system/useSystemStore';
import type { ResLogin } from '@/global/support/api/userRes.d';
import { useRouter } from 'next/router';
import { useUserStore } from '@/web/support/user/useUserStore';
import { useChatStore } from '@/web/core/chat/context/storeChat';
import LoginForm from './components/LoginForm/LoginForm';
import dynamic from 'next/dynamic';
import { serviceSideProps } from '@/web/common/utils/i18n';
import { clearToken, setToken } from '@/web/support/user/auth';
import Script from 'next/script';
import Loading from '@fastgpt/web/components/common/MyLoading';
import { useMount } from 'ahooks';
import { useTranslation } from 'next-i18next';

const RegisterForm = dynamic(() => import('./components/RegisterForm'));
const ForgetPasswordForm = dynamic(() => import('./components/ForgetPasswordForm'));
const WechatForm = dynamic(() => import('./components/LoginForm/WechatForm'));
const CommunityModal = dynamic(() => import('@/components/CommunityModal'));

const Login = () => {
  const router = useRouter();
  const { t } = useTranslation(['common', 'user']);
  const { lastRoute = '' } = router.query as { lastRoute: string };
  const { feConfigs } = useSystemStore();
  const [pageType, setPageType] = useState<`${LoginPageTypeEnum}`>();
  const { setUserInfo } = useUserStore();
  const { setLastChatId, setLastChatAppId } = useChatStore();
  const { isOpen, onOpen, onClose } = useDisclosure();

  const loginSuccess = useCallback(
    (res: ResLogin) => {
      // init store
      setLastChatId('');
      setLastChatAppId('');

      setUserInfo(res.user);
      setToken(res.token);
      setTimeout(() => {
        router.push(lastRoute ? decodeURIComponent(lastRoute) : '/app/list');
      }, 300);
    },
    [lastRoute, router, setLastChatId, setLastChatAppId, setUserInfo]
  );

  function DynamicComponent({ type }: { type: `${LoginPageTypeEnum}` }) {
    const TypeMap = {
      [LoginPageTypeEnum.passwordLogin]: LoginForm,
      [LoginPageTypeEnum.register]: RegisterForm,
      [LoginPageTypeEnum.forgetPassword]: ForgetPasswordForm,
      [LoginPageTypeEnum.wechat]: WechatForm
    };

    const Component = TypeMap[type];

    return <Component setPageType={setPageType} loginSuccess={loginSuccess} />;
  }

  /* default login type */
  useEffect(() => {
    setPageType(
      feConfigs?.oauth?.wechat ? LoginPageTypeEnum.wechat : LoginPageTypeEnum.passwordLogin
    );
  }, [feConfigs.oauth]);

  useMount(() => {
    clearToken();
    router.prefetch('/app/list');
  });

  return (
    <>
      {feConfigs.googleClientVerKey && (
        <Script
          src={`https://www.recaptcha.net/recaptcha/api.js?render=${feConfigs.googleClientVerKey}`}
        ></Script>
      )}

      <Grid templateColumns={{ base: '1fr', md: '1fr 1fr' }} minH="100vh" bg="black">
        {/* 左侧动画背景区域 */}
        <GridItem
          display={{ base: 'none', md: 'flex' }}
          position="relative"
          overflow="hidden"
          // bgGradient="linear(to-br, #1E293B, #0F172A)"
          bgGradient="linear(to-br, #334155, #1E293B)"
        >
          {/* 动态光效背景 */}
          <Box
            position="absolute"
            top="0"
            left="0"
            right="0"
            bottom="0"
            opacity="0.2"
            bgImage="url('/imgs/grid-pattern.svg')"
            bgSize="cover"
            animation="pulse 3s infinite"
          />

          {/* AI主题内容 */}
          <VStack
            w="full"
            h="full"
            justify="center"
            spacing={8}
            p={10}
            color="white"
            position="relative"
            zIndex={1}
          >
            <Heading
              size="2xl"
              textAlign="center"
              bgGradient="linear(to-r, #c250c6 0%, #547cf0 100%)"
              bgClip="text"
            >
              {t('user:login_index.AI Future Creator Program')}
            </Heading>
            <Text fontSize="lg" textAlign="center" maxW="80%" color="whiteAlpha.800">
              {t('user:login_index.Join AI First')}, {t('user:login_index.Be Different')}.
              {t('user:login_index.AI Today Like Internet')}
            </Text>
            <Box
              w="80%"
              h="300px"
              position="relative"
              filter="drop-shadow(0 0 20px rgba(255,123,140,0.3))"
            >
              <Image src="/imgs/ai-future.svg" alt="AI Future" layout="fill" objectFit="contain" />
            </Box>
          </VStack>
        </GridItem>

        {/* 右侧登录表单区域 - 修改为浅色背景 */}
        <GridItem
          display="flex"
          alignItems="center"
          justifyContent="center"
          bg="white"
          position="relative"
        >
          {/* 科技感装饰线条 - 调整透明度和颜色 */}
          <Box
            position="absolute"
            top="0"
            left="0"
            right="0"
            bottom="0"
            bgImage="url('/images/cyber-lines.svg')"
            bgSize="cover"
            opacity="0.05"
          />

          <Container maxW="md" p={8} position="relative">
            <VStack spacing={8} align="stretch">
              <Box
                p={8}
                borderRadius="xl"
                bg="white"
                boxShadow="xl"
                border="1px solid"
                borderColor="gray.100"
                _hover={{
                  boxShadow: '2xl',
                  borderColor: 'pink.200',
                  transform: 'translateY(-2px)',
                  transition: 'all 0.3s'
                }}
              >
                {pageType ? (
                  <DynamicComponent type={pageType} />
                ) : (
                  <Center w="full" h="full" position="relative">
                    <Loading fixed={false} />
                  </Center>
                )}
              </Box>

              {feConfigs?.concatMd && (
                <Text
                  color="gray.600"
                  cursor="pointer"
                  textAlign="center"
                  onClick={onOpen}
                  fontSize="sm"
                  _hover={{ color: 'pink.500' }}
                >
                  {t('user:login_index.Cannot Login')}
                </Text>
              )}
            </VStack>

            {/* <Text fontSize="sm" color="gray.600" textAlign="center">
              Copyright © 2023
            </Text>
            <Text fontSize="sm" color="gray.600" textAlign="center">
              京ICP备2023010266号
            </Text> */}
          </Container>
        </GridItem>
      </Grid>

      {isOpen && <CommunityModal onClose={onClose} />}
    </>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: { ...(await serviceSideProps(context, ['app', 'user'])) }
  };
}

export default Login;
