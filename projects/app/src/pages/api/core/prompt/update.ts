import type { NextApiRequest, NextApiResponse } from 'next';
import { jsonRes } from '@fastgpt/service/common/response';
import { connectToDatabase } from '@/service/mongo';
import { authUserPer } from '@fastgpt/service/support/permission/user/auth';
import { MongoPromptTemplate } from '@fastgpt/service/core/prompt/schema';
import { Schema } from '@fastgpt/service/common/mongo';
import { WritePermissionVal } from '@fastgpt/global/support/permission/constant';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    await connectToDatabase();
    const { tmbId, teamId, permission } = await authUserPer({
      req,
      authToken: true,
      per: WritePermissionVal
    });
    const { id } = req.query;
    const { title, description } = req.body;

    if (!id || typeof id !== 'string') {
      throw new Error('Invalid template ID');
    }

    if (req.method === 'PUT') {
      // 更新提示词模板
      const updateData: Record<string, any> = {};

      if (title !== undefined) {
        updateData.title = title;
      }

      if (description !== undefined) {
        updateData.description = description;
      }

      const result = await MongoPromptTemplate.updateOne(
        {
          _id: new Schema.Types.ObjectId(id),
          teamId,
          tmbId
        },
        { $set: updateData }
      );

      if (result.matchedCount === 0) {
        throw new Error('Template not found');
      }

      jsonRes(res, {
        data: true
      });
    } else {
      throw new Error('Method not allowed');
    }
  } catch (error) {
    jsonRes(res, {
      code: 500,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}
