import type { NextApiRequest, NextApiResponse } from 'next';
import { jsonRes } from '@fastgpt/service/common/response';
import { connectToDatabase } from '@/service/mongo';
import { authUserPer } from '@fastgpt/service/support/permission/user/auth';
import { MongoPromptItem } from '@fastgpt/service/core/prompt/schema';
import { Schema } from '@fastgpt/service/common/mongo';
import { WritePermissionVal } from '@fastgpt/global/support/permission/constant';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    await connectToDatabase();
    const { tmbId, teamId, permission } = await authUserPer({
      req,
      authToken: true,
      per: WritePermissionVal
    });
    const { templateId, itemId } = req.query;
    const { title, content } = req.body;

    if (!templateId || typeof templateId !== 'string' || !itemId || typeof itemId !== 'string') {
      throw new Error('Invalid template or item ID');
    }

    if (req.method === 'PUT') {
      // 更新提示词项
      const updateFields: Record<string, any> = {};

      if (title !== undefined) {
        updateFields.title = title;
      }

      if (content !== undefined) {
        updateFields.content = content;
      }

      const result = await MongoPromptItem.updateOne(
        {
          _id: new Schema.Types.ObjectId(itemId),
          templateId: new Schema.Types.ObjectId(templateId),
          teamId,
          tmbId
        },
        { $set: updateFields }
      );

      if (result.matchedCount === 0) {
        throw new Error('Item not found');
      }

      jsonRes(res, {
        data: true
      });
    } else {
      throw new Error('Method not allowed');
    }
  } catch (error) {
    jsonRes(res, {
      code: 500,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}
