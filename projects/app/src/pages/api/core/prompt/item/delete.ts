import type { NextApiRequest, NextApiResponse } from 'next';
import { jsonRes } from '@fastgpt/service/common/response';
import { connectToDatabase } from '@/service/mongo';
import { authUserPer } from '@fastgpt/service/support/permission/user/auth';
import { MongoPromptItem } from '@fastgpt/service/core/prompt/schema';
import { Schema } from '@fastgpt/service/common/mongo';
import { WritePermissionVal } from '@fastgpt/global/support/permission/constant';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    await connectToDatabase();
    const { tmbId, teamId, permission } = await authUserPer({
      req,
      authToken: true,
      per: WritePermissionVal
    });
    const { templateId, itemId } = req.query;

    if (!templateId || typeof templateId !== 'string' || !itemId || typeof itemId !== 'string') {
      throw new Error('Invalid template or item ID');
    }

    if (req.method === 'DELETE') {
      // 删除提示词项
      const result = await MongoPromptItem.deleteOne({
        _id: new Schema.Types.ObjectId(itemId),
        templateId: new Schema.Types.ObjectId(templateId),
        teamId,
        tmbId
      });

      if (result.deletedCount === 0) {
        throw new Error('Item not found');
      }

      jsonRes(res, {
        data: true
      });
    } else {
      throw new Error('Method not allowed');
    }
  } catch (error) {
    jsonRes(res, {
      code: 500,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}
