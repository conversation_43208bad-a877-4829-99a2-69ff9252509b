import type { NextApiRequest, NextApiResponse } from 'next';
import { jsonRes } from '@fastgpt/service/common/response';
import { connectToDatabase } from '@/service/mongo';
import { authUserPer } from '@fastgpt/service/support/permission/user/auth';
import { MongoPromptTemplate, MongoPromptItem } from '@fastgpt/service/core/prompt/schema';
import { Schema } from '@fastgpt/service/common/mongo';
import { WritePermissionVal } from '@fastgpt/global/support/permission/constant';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    await connectToDatabase();
    const { tmbId, teamId, permission } = await authUserPer({
      req,
      authToken: true,
      per: WritePermissionVal
    });
    const { templateId } = req.query;
    const { title, content } = req.body;

    if (!templateId || typeof templateId !== 'string') {
      throw new Error('Invalid template ID');
    }

    if (!title || !content) {
      throw new Error('Title and content are required');
    }

    if (req.method === 'POST') {
      // 检查模板是否存在
      const template = await MongoPromptTemplate.findOne({
        _id: new Schema.Types.ObjectId(templateId),
        teamId,
        tmbId
      });

      if (!template) {
        throw new Error('Template not found');
      }

      // 添加提示词项
      const item = await MongoPromptItem.create({
        templateId: new Schema.Types.ObjectId(templateId),
        teamId,
        tmbId,
        title,
        content
      });

      jsonRes(res, {
        data: item._id.toString()
      });
    } else {
      throw new Error('Method not allowed');
    }
  } catch (error) {
    jsonRes(res, {
      code: 500,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}
