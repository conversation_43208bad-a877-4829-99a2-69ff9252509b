import type { NextApiRequest, NextApiResponse } from 'next';
import { jsonRes } from '@fastgpt/service/common/response';
import { connectToDatabase } from '@/service/mongo';
import { authUserPer } from '@fastgpt/service/support/permission/user/auth';
import { MongoPromptTemplate, MongoPromptItem } from '@fastgpt/service/core/prompt/schema';
import { Schema } from '@fastgpt/service/common/mongo';
import { ReadPermissionVal } from '@fastgpt/global/support/permission/constant';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    await connectToDatabase();
    const { tmbId, teamId, permission } = await authUserPer({
      req,
      authToken: true,
      per: ReadPermissionVal
    });
    const { id } = req.query;

    if (!id || typeof id !== 'string') {
      throw new Error('Invalid template ID');
    }

    if (req.method === 'GET') {
      // 获取提示词模板详情
      const template = await MongoPromptTemplate.findOne({
        _id: new Schema.Types.ObjectId(id),
        teamId,
        tmbId
      }).lean();

      if (!template) {
        throw new Error('Template not found');
      }

      // 获取该模板下的所有提示词项
      const items = await MongoPromptItem.find({
        templateId: new Schema.Types.ObjectId(id),
        teamId,
        tmbId
      }).lean();

      jsonRes(res, {
        data: {
          ...template,
          items
        }
      });
    } else {
      throw new Error('Method not allowed');
    }
  } catch (error) {
    jsonRes(res, {
      code: 500,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}
