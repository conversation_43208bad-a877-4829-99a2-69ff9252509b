import type { NextApiRequest, NextApiResponse } from 'next';
import { jsonRes } from '@fastgpt/service/common/response';
import { connectToDatabase } from '@/service/mongo';
import { authUserPer } from '@fastgpt/service/support/permission/user/auth';
import { MongoPromptTemplate, MongoPromptItem } from '@fastgpt/service/core/prompt/schema';
import { Schema } from '@fastgpt/service/common/mongo';
import { WritePermissionVal } from '@fastgpt/global/support/permission/constant';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    await connectToDatabase();
    const { tmbId, teamId, permission } = await authUserPer({
      req,
      authToken: true,
      per: WritePermissionVal
    });
    const { id } = req.query;

    if (!id || typeof id !== 'string') {
      throw new Error('Invalid template ID');
    }

    if (req.method === 'DELETE') {
      // 删除提示词模板
      const result = await MongoPromptTemplate.deleteOne({
        _id: new Schema.Types.ObjectId(id),
        teamId,
        tmbId
      });

      if (result.deletedCount === 0) {
        throw new Error('Template not found');
      }

      // 删除该模板下的所有提示词项
      await MongoPromptItem.deleteMany({
        templateId: new Schema.Types.ObjectId(id),
        teamId,
        tmbId
      });

      jsonRes(res, {
        data: true
      });
    } else {
      throw new Error('Method not allowed');
    }
  } catch (error) {
    jsonRes(res, {
      code: 500,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}
