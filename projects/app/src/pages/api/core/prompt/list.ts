import type { NextApiRequest, NextApiResponse } from 'next';
import { jsonRes } from '@fastgpt/service/common/response';
import { connectToDatabase } from '@/service/mongo';
import { authUserPer } from '@fastgpt/service/support/permission/user/auth';
import { MongoPromptTemplate } from '@fastgpt/service/core/prompt/schema';
import { ReadPermissionVal } from '@fastgpt/global/support/permission/constant';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    await connectToDatabase();
    const { tmbId, teamId, permission } = await authUserPer({
      req,
      authToken: true,
      per: ReadPermissionVal
    });

    if (req.method === 'GET') {
      // 获取提示词模板列表
      const templates = await MongoPromptTemplate.find(
        { teamId, tmbId },
        'title description createdAt'
      )
        .sort({ createdAt: -1 })
        .lean();

      jsonRes(res, {
        data: templates
      });
    } else {
      throw new Error('Method not allowed');
    }
  } catch (error) {
    jsonRes(res, {
      code: 500,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}
