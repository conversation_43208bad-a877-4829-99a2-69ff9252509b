import type { NextApiRequest, NextApiResponse } from 'next';
import { jsonRes } from '@fastgpt/service/common/response';
import { connectToDatabase } from '@/service/mongo';
import { authUserPer } from '@fastgpt/service/support/permission/user/auth';
import { MongoPromptTemplate } from '@fastgpt/service/core/prompt/schema';
import { WritePermissionVal } from '@fastgpt/global/support/permission/constant';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    await connectToDatabase();
    const { tmbId, teamId, permission } = await authUserPer({
      req,
      authToken: true,
      per: WritePermissionVal
    });

    if (req.method === 'POST') {
      const { title, description } = req.body;

      if (!title) {
        throw new Error('Title is required');
      }

      // 创建提示词模板
      const template = await MongoPromptTemplate.create({
        teamId,
        tmbId,
        title,
        description: description || ''
      });

      jsonRes(res, {
        data: template._id.toString()
      });
    } else {
      throw new Error('Method not allowed');
    }
  } catch (error) {
    jsonRes(res, {
      code: 500,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}
