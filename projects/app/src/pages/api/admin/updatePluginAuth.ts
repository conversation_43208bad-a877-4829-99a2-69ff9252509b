import type { NextApiRequest, NextApiResponse } from 'next';
import { jsonRes } from '@fastgpt/service/common/response';
import { connectToDatabase } from '@/service/mongo';
import { authCert } from '@fastgpt/service/support/permission/auth/common';
import { MongoApp } from '@fastgpt/service/core/app/schema';
import { MongoUser } from '@fastgpt/service/support/user/schema';
import { createFofproUser } from '@fastgpt/service/support/user/controller';
import { MongoTeamMember } from '@fastgpt/service/support/user/team/teamMemberSchema';
import _ from 'lodash';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    await connectToDatabase();
    await authCert({ req, authPluginApiToken: true });
    const appId = req.body.appId;
    const unionId = req.body.unionId;
    if (!appId || !unionId) {
      throw new Error('missing parameter appId or unionId');
    }
    let user = await MongoUser.findOne(
      {
        username: unionId
      },
      'status tmbId'
    );
    let userId = user?._id;
    if (!user) {
      const userProps = {
        username: unionId,
        nickName: unionId,
        fofproUserId: _.random(1000000000, 9999999999)
      };
      userId = await createFofproUser(userProps);
    }
    const tmb = await MongoTeamMember.findOne({
      userId: userId,
      defaultTeam: true
    });
    const tmbId = String(tmb?._id);
    await MongoApp.updateOne(
      {
        _id: appId
      },
      {
        $addToSet: {
          authTmbIds: tmbId
        }
      }
    );

    jsonRes(res, {
      message: 'success'
    });
  } catch (error) {
    console.log(error);

    jsonRes(res, {
      code: 500,
      error
    });
  }
}
